{"ast": null, "code": "import Metadata from '../metadata.js';\nimport { VALID_DIGITS } from '../constants.js';\nvar CAPTURING_DIGIT_PATTERN = new RegExp('([' + VALID_DIGITS + '])');\nexport default function stripIddPrefix(number, country, callingCode, metadata) {\n  if (!country) {\n    return;\n  } // Check if the number is IDD-prefixed.\n\n  var countryMetadata = new Metadata(metadata);\n  countryMetadata.selectNumberingPlan(country, callingCode);\n  var IDDPrefixPattern = new RegExp(countryMetadata.IDDPrefix());\n  if (number.search(IDDPrefixPattern) !== 0) {\n    return;\n  } // Strip IDD prefix.\n\n  number = number.slice(number.match(IDDPrefixPattern)[0].length); // If there're any digits after an IDD prefix,\n  // then those digits are a country calling code.\n  // Since no country code starts with a `0`,\n  // the code below validates that the next digit (if present) is not `0`.\n\n  var matchedGroups = number.match(CAPTURING_DIGIT_PATTERN);\n  if (matchedGroups && matchedGroups[1] != null && matchedGroups[1].length > 0) {\n    if (matchedGroups[1] === '0') {\n      return;\n    }\n  }\n  return number;\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "VALID_DIGITS", "CAPTURING_DIGIT_PATTERN", "RegExp", "stripIddPrefix", "number", "country", "callingCode", "metadata", "countryMetadata", "selectNumberingPlan", "IDDPrefixPattern", "IDDPrefix", "search", "slice", "match", "length", "matchedGroups"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/helpers/stripIddPrefix.js"], "sourcesContent": ["import Metadata from '../metadata.js';\nimport { VALID_DIGITS } from '../constants.js';\nvar CAPTURING_DIGIT_PATTERN = new RegExp('([' + VALID_DIGITS + '])');\nexport default function stripIddPrefix(number, country, callingCode, metadata) {\n  if (!country) {\n    return;\n  } // Check if the number is IDD-prefixed.\n\n\n  var countryMetadata = new Metadata(metadata);\n  countryMetadata.selectNumberingPlan(country, callingCode);\n  var IDDPrefixPattern = new RegExp(countryMetadata.IDDPrefix());\n\n  if (number.search(IDDPrefixPattern) !== 0) {\n    return;\n  } // Strip IDD prefix.\n\n\n  number = number.slice(number.match(IDDPrefixPattern)[0].length); // If there're any digits after an IDD prefix,\n  // then those digits are a country calling code.\n  // Since no country code starts with a `0`,\n  // the code below validates that the next digit (if present) is not `0`.\n\n  var matchedGroups = number.match(CAPTURING_DIGIT_PATTERN);\n\n  if (matchedGroups && matchedGroups[1] != null && matchedGroups[1].length > 0) {\n    if (matchedGroups[1] === '0') {\n      return;\n    }\n  }\n\n  return number;\n}\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,IAAIC,uBAAuB,GAAG,IAAIC,MAAM,CAAC,IAAI,GAAGF,YAAY,GAAG,IAAI,CAAC;AACpE,eAAe,SAASG,cAAcA,CAACC,MAAM,EAAEC,OAAO,EAAEC,WAAW,EAAEC,QAAQ,EAAE;EAC7E,IAAI,CAACF,OAAO,EAAE;IACZ;EACF,CAAC,CAAC;;EAGF,IAAIG,eAAe,GAAG,IAAIT,QAAQ,CAACQ,QAAQ,CAAC;EAC5CC,eAAe,CAACC,mBAAmB,CAACJ,OAAO,EAAEC,WAAW,CAAC;EACzD,IAAII,gBAAgB,GAAG,IAAIR,MAAM,CAACM,eAAe,CAACG,SAAS,CAAC,CAAC,CAAC;EAE9D,IAAIP,MAAM,CAACQ,MAAM,CAACF,gBAAgB,CAAC,KAAK,CAAC,EAAE;IACzC;EACF,CAAC,CAAC;;EAGFN,MAAM,GAAGA,MAAM,CAACS,KAAK,CAACT,MAAM,CAACU,KAAK,CAACJ,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAACK,MAAM,CAAC,CAAC,CAAC;EACjE;EACA;EACA;;EAEA,IAAIC,aAAa,GAAGZ,MAAM,CAACU,KAAK,CAACb,uBAAuB,CAAC;EAEzD,IAAIe,aAAa,IAAIA,aAAa,CAAC,CAAC,CAAC,IAAI,IAAI,IAAIA,aAAa,CAAC,CAAC,CAAC,CAACD,MAAM,GAAG,CAAC,EAAE;IAC5E,IAAIC,aAAa,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MAC5B;IACF;EACF;EAEA,OAAOZ,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}