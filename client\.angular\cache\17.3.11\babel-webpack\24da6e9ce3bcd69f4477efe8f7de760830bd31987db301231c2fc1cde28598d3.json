{"ast": null, "code": "function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nimport normalizeArguments from '../normalizeArguments.js';\nimport PhoneNumberMatcher from '../PhoneNumberMatcher.js';\n/**\r\n * @return ES6 `for ... of` iterator.\r\n */\n\nexport default function searchNumbers() {\n  var _normalizeArguments = normalizeArguments(arguments),\n    text = _normalizeArguments.text,\n    options = _normalizeArguments.options,\n    metadata = _normalizeArguments.metadata;\n  var matcher = new PhoneNumberMatcher(text, options, metadata);\n  return _defineProperty({}, Symbol.iterator, function () {\n    return {\n      next: function next() {\n        if (matcher.hasNext()) {\n          return {\n            done: false,\n            value: matcher.next()\n          };\n        }\n        return {\n          done: true\n        };\n      }\n    };\n  });\n}", "map": {"version": 3, "names": ["_defineProperty", "obj", "key", "value", "Object", "defineProperty", "enumerable", "configurable", "writable", "normalizeArguments", "PhoneNumberMatcher", "searchNumbers", "_normalizeArguments", "arguments", "text", "options", "metadata", "matcher", "Symbol", "iterator", "next", "hasNext", "done"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/legacy/searchNumbers.js"], "sourcesContent": ["function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nimport normalizeArguments from '../normalizeArguments.js';\nimport PhoneNumberMatcher from '../PhoneNumberMatcher.js';\n/**\r\n * @return ES6 `for ... of` iterator.\r\n */\n\nexport default function searchNumbers() {\n  var _normalizeArguments = normalizeArguments(arguments),\n      text = _normalizeArguments.text,\n      options = _normalizeArguments.options,\n      metadata = _normalizeArguments.metadata;\n\n  var matcher = new PhoneNumberMatcher(text, options, metadata);\n  return _defineProperty({}, Symbol.iterator, function () {\n    return {\n      next: function next() {\n        if (matcher.hasNext()) {\n          return {\n            done: false,\n            value: matcher.next()\n          };\n        }\n\n        return {\n          done: true\n        };\n      }\n    };\n  });\n}\n"], "mappings": "AAAA,SAASA,eAAeA,CAACC,GAAG,EAAEC,GAAG,EAAEC,KAAK,EAAE;EAAE,IAAID,GAAG,IAAID,GAAG,EAAE;IAAEG,MAAM,CAACC,cAAc,CAACJ,GAAG,EAAEC,GAAG,EAAE;MAAEC,KAAK,EAAEA,KAAK;MAAEG,UAAU,EAAE,IAAI;MAAEC,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEP,GAAG,CAACC,GAAG,CAAC,GAAGC,KAAK;EAAE;EAAE,OAAOF,GAAG;AAAE;AAEhN,OAAOQ,kBAAkB,MAAM,0BAA0B;AACzD,OAAOC,kBAAkB,MAAM,0BAA0B;AACzD;AACA;AACA;;AAEA,eAAe,SAASC,aAAaA,CAAA,EAAG;EACtC,IAAIC,mBAAmB,GAAGH,kBAAkB,CAACI,SAAS,CAAC;IACnDC,IAAI,GAAGF,mBAAmB,CAACE,IAAI;IAC/BC,OAAO,GAAGH,mBAAmB,CAACG,OAAO;IACrCC,QAAQ,GAAGJ,mBAAmB,CAACI,QAAQ;EAE3C,IAAIC,OAAO,GAAG,IAAIP,kBAAkB,CAACI,IAAI,EAAEC,OAAO,EAAEC,QAAQ,CAAC;EAC7D,OAAOhB,eAAe,CAAC,CAAC,CAAC,EAAEkB,MAAM,CAACC,QAAQ,EAAE,YAAY;IACtD,OAAO;MACLC,IAAI,EAAE,SAASA,IAAIA,CAAA,EAAG;QACpB,IAAIH,OAAO,CAACI,OAAO,CAAC,CAAC,EAAE;UACrB,OAAO;YACLC,IAAI,EAAE,KAAK;YACXnB,KAAK,EAAEc,OAAO,CAACG,IAAI,CAAC;UACtB,CAAC;QACH;QAEA,OAAO;UACLE,IAAI,EAAE;QACR,CAAC;MACH;IACF,CAAC;EACH,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}