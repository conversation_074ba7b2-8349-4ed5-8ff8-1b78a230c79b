{"ast": null, "code": "import Metadata from '../metadata.js';\n/**\r\n * <PERSON><PERSON> that makes it easy to distinguish whether a region has a single\r\n * international dialing prefix or not. If a region has a single international\r\n * prefix (e.g. 011 in USA), it will be represented as a string that contains\r\n * a sequence of ASCII digits, and possibly a tilde, which signals waiting for\r\n * the tone. If there are multiple available international prefixes in a\r\n * region, they will be represented as a regex string that always contains one\r\n * or more characters that are not ASCII digits or a tilde.\r\n */\n\nvar SINGLE_IDD_PREFIX_REG_EXP = /^[\\d]+(?:[~\\u2053\\u223C\\uFF5E][\\d]+)?$/; // For regions that have multiple IDD prefixes\n// a preferred IDD prefix is returned.\n\nexport default function getIddPrefix(country, callingCode, metadata) {\n  var countryMetadata = new Metadata(metadata);\n  countryMetadata.selectNumberingPlan(country, callingCode);\n  if (countryMetadata.defaultIDDPrefix()) {\n    return countryMetadata.defaultIDDPrefix();\n  }\n  if (SINGLE_IDD_PREFIX_REG_EXP.test(countryMetadata.IDDPrefix())) {\n    return countryMetadata.IDDPrefix();\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "SINGLE_IDD_PREFIX_REG_EXP", "getIddPrefix", "country", "callingCode", "metadata", "countryMetadata", "selectNumberingPlan", "defaultIDDPrefix", "test", "IDDPrefix"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/helpers/getIddPrefix.js"], "sourcesContent": ["import Metadata from '../metadata.js';\n/**\r\n * <PERSON><PERSON> that makes it easy to distinguish whether a region has a single\r\n * international dialing prefix or not. If a region has a single international\r\n * prefix (e.g. 011 in USA), it will be represented as a string that contains\r\n * a sequence of ASCII digits, and possibly a tilde, which signals waiting for\r\n * the tone. If there are multiple available international prefixes in a\r\n * region, they will be represented as a regex string that always contains one\r\n * or more characters that are not ASCII digits or a tilde.\r\n */\n\nvar SINGLE_IDD_PREFIX_REG_EXP = /^[\\d]+(?:[~\\u2053\\u223C\\uFF5E][\\d]+)?$/; // For regions that have multiple IDD prefixes\n// a preferred IDD prefix is returned.\n\nexport default function getIddPrefix(country, callingCode, metadata) {\n  var countryMetadata = new Metadata(metadata);\n  countryMetadata.selectNumberingPlan(country, callingCode);\n\n  if (countryMetadata.defaultIDDPrefix()) {\n    return countryMetadata.defaultIDDPrefix();\n  }\n\n  if (SINGLE_IDD_PREFIX_REG_EXP.test(countryMetadata.IDDPrefix())) {\n    return countryMetadata.IDDPrefix();\n  }\n}\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,yBAAyB,GAAG,wCAAwC,CAAC,CAAC;AAC1E;;AAEA,eAAe,SAASC,YAAYA,CAACC,OAAO,EAAEC,WAAW,EAAEC,QAAQ,EAAE;EACnE,IAAIC,eAAe,GAAG,IAAIN,QAAQ,CAACK,QAAQ,CAAC;EAC5CC,eAAe,CAACC,mBAAmB,CAACJ,OAAO,EAAEC,WAAW,CAAC;EAEzD,IAAIE,eAAe,CAACE,gBAAgB,CAAC,CAAC,EAAE;IACtC,OAAOF,eAAe,CAACE,gBAAgB,CAAC,CAAC;EAC3C;EAEA,IAAIP,yBAAyB,CAACQ,IAAI,CAACH,eAAe,CAACI,SAAS,CAAC,CAAC,CAAC,EAAE;IAC/D,OAAOJ,eAAe,CAACI,SAAS,CAAC,CAAC;EACpC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}