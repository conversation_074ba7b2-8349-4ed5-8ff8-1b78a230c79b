{"ast": null, "code": "import withMetadataArgument from './withMetadataArgument.js';\nimport { getCountries as _getCountries } from '../../core/index.js';\nexport function getCountries() {\n  return withMetadataArgument(_getCountries, arguments);\n}", "map": {"version": 3, "names": ["withMetadataArgument", "getCountries", "_getCountries", "arguments"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/min/exports/getCountries.js"], "sourcesContent": ["import withMetadataArgument from './withMetadataArgument.js'\r\nimport { getCountries as _getCountries } from '../../core/index.js'\r\n\r\nexport function getCountries() {\r\n\treturn withMetadataArgument(_getCountries, arguments)\r\n}"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,2BAA2B;AAC5D,SAASC,YAAY,IAAIC,aAAa,QAAQ,qBAAqB;AAEnE,OAAO,SAASD,YAAYA,CAAA,EAAG;EAC9B,OAAOD,oBAAoB,CAACE,aAAa,EAAEC,SAAS,CAAC;AACtD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}