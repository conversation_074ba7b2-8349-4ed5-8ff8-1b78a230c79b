{"ast": null, "code": "// Importing from a \".js\" file is a workaround for Node.js \"ES Modules\"\n// importing system which is even uncapable of importing \"*.json\" files.\nimport metadata from '../../metadata.min.json.js';\nimport { PhoneNumberMatcher as _PhoneNumberMatcher } from '../../core/index.js';\nexport function PhoneNumberMatcher(text, options) {\n  return _PhoneNumberMatcher.call(this, text, options, metadata);\n}\nPhoneNumberMatcher.prototype = Object.create(_PhoneNumberMatcher.prototype, {});\nPhoneNumberMatcher.prototype.constructor = PhoneNumberMatcher;", "map": {"version": 3, "names": ["metadata", "PhoneNumberMatcher", "_PhoneNumberMatcher", "text", "options", "call", "prototype", "Object", "create", "constructor"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/min/exports/PhoneNumberMatcher.js"], "sourcesContent": ["// Importing from a \".js\" file is a workaround for Node.js \"ES Modules\"\r\n// importing system which is even uncapable of importing \"*.json\" files.\r\nimport metadata from '../../metadata.min.json.js'\r\n\r\nimport { PhoneNumberMatcher as _PhoneNumberMatcher } from '../../core/index.js'\r\n\r\nexport function PhoneNumberMatcher(text, options) {\r\n\treturn _PhoneNumberMatcher.call(this, text, options, metadata)\r\n}\r\nPhoneNumberMatcher.prototype = Object.create(_PhoneNumberMatcher.prototype, {})\r\nPhoneNumberMatcher.prototype.constructor = PhoneNumberMatcher\r\n"], "mappings": "AAAA;AACA;AACA,OAAOA,QAAQ,MAAM,4BAA4B;AAEjD,SAASC,kBAAkB,IAAIC,mBAAmB,QAAQ,qBAAqB;AAE/E,OAAO,SAASD,kBAAkBA,CAACE,IAAI,EAAEC,OAAO,EAAE;EACjD,OAAOF,mBAAmB,CAACG,IAAI,CAAC,IAAI,EAAEF,IAAI,EAAEC,OAAO,EAAEJ,QAAQ,CAAC;AAC/D;AACAC,kBAAkB,CAACK,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACN,mBAAmB,CAACI,SAAS,EAAE,CAAC,CAAC,CAAC;AAC/EL,kBAAkB,CAACK,SAAS,CAACG,WAAW,GAAGR,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}