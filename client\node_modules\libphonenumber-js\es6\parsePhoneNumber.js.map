{"version": 3, "file": "parsePhoneNumber.js", "names": ["normalizeArguments", "parsePhoneNumber_", "parsePhoneNumber", "arguments", "text", "options", "metadata"], "sources": ["../source/parsePhoneNumber.js"], "sourcesContent": ["import normalizeArguments from './normalizeArguments.js'\r\nimport parsePhoneNumber_ from './parsePhoneNumber_.js'\r\n\r\nexport default function parsePhoneNumber() {\r\n\tconst { text, options, metadata } = normalizeArguments(arguments)\r\n\treturn parsePhoneNumber_(text, options, metadata)\r\n}\r\n"], "mappings": "AAAA,OAAOA,kBAAP,MAA+B,yBAA/B;AACA,OAAOC,iBAAP,MAA8B,wBAA9B;AAEA,eAAe,SAASC,gBAAT,GAA4B;EAC1C,0BAAoCF,kBAAkB,CAACG,SAAD,CAAtD;EAAA,IAAQC,IAAR,uBAAQA,IAAR;EAAA,IAAcC,OAAd,uBAAcA,OAAd;EAAA,IAAuBC,QAAvB,uBAAuBA,QAAvB;;EACA,OAAOL,iBAAiB,CAACG,IAAD,EAAOC,OAAP,EAAgBC,QAAhB,CAAxB;AACA"}