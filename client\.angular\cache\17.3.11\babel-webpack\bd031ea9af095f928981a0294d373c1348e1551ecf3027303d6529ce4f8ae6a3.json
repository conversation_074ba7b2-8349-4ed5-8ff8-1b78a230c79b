{"ast": null, "code": "var objectConstructor = {}.constructor;\nexport default function isObject(object) {\n  return object !== undefined && object !== null && object.constructor === objectConstructor;\n}", "map": {"version": 3, "names": ["objectConstructor", "constructor", "isObject", "object", "undefined"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/helpers/isObject.js"], "sourcesContent": ["var objectConstructor = {}.constructor;\nexport default function isObject(object) {\n  return object !== undefined && object !== null && object.constructor === objectConstructor;\n}\n"], "mappings": "AAAA,IAAIA,iBAAiB,GAAG,CAAC,CAAC,CAACC,WAAW;AACtC,eAAe,SAASC,QAAQA,CAACC,MAAM,EAAE;EACvC,OAAOA,MAAM,KAAKC,SAAS,IAAID,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACF,WAAW,KAAKD,iBAAiB;AAC5F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}