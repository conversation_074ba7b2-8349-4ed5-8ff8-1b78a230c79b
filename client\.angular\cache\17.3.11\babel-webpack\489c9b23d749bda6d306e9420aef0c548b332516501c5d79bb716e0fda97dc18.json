{"ast": null, "code": "import stripIddPrefix from './stripIddPrefix.js';\nimport extractCountryCallingCodeFromInternationalNumberWithoutPlusSign from './extractCountryCallingCodeFromInternationalNumberWithoutPlusSign.js';\nimport Metadata from '../metadata.js';\nimport { MAX_LENGTH_COUNTRY_CODE } from '../constants.js';\n/**\r\n * Converts a phone number digits (possibly with a `+`)\r\n * into a calling code and the rest phone number digits.\r\n * The \"rest phone number digits\" could include\r\n * a national prefix, carrier code, and national\r\n * (significant) number.\r\n * @param  {string} number — Phone number digits (possibly with a `+`).\r\n * @param  {string} [country] — Default country.\r\n * @param  {string} [callingCode] — Default calling code (some phone numbering plans are non-geographic).\r\n * @param  {object} metadata\r\n * @return {object} `{ countryCallingCodeSource: string?, countryCallingCode: string?, number: string }`\r\n * @example\r\n * // Returns `{ countryCallingCode: \"1\", number: \"2133734253\" }`.\r\n * extractCountryCallingCode('2133734253', 'US', null, metadata)\r\n * extractCountryCallingCode('2133734253', null, '1', metadata)\r\n * extractCountryCallingCode('+12133734253', null, null, metadata)\r\n * extractCountryCallingCode('+12133734253', 'RU', null, metadata)\r\n */\n\nexport default function extractCountryCallingCode(number, country, callingCode, metadata) {\n  if (!number) {\n    return {};\n  }\n  var isNumberWithIddPrefix; // If this is not an international phone number,\n  // then either extract an \"IDD\" prefix, or extract a\n  // country calling code from a number by autocorrecting it\n  // by prepending a leading `+` in cases when it starts\n  // with the country calling code.\n  // https://wikitravel.org/en/International_dialling_prefix\n  // https://github.com/catamphetamine/libphonenumber-js/issues/376\n\n  if (number[0] !== '+') {\n    // Convert an \"out-of-country\" dialing phone number\n    // to a proper international phone number.\n    var numberWithoutIDD = stripIddPrefix(number, country, callingCode, metadata); // If an IDD prefix was stripped then\n    // convert the number to international one\n    // for subsequent parsing.\n\n    if (numberWithoutIDD && numberWithoutIDD !== number) {\n      isNumberWithIddPrefix = true;\n      number = '+' + numberWithoutIDD;\n    } else {\n      // Check to see if the number starts with the country calling code\n      // for the default country. If so, we remove the country calling code,\n      // and do some checks on the validity of the number before and after.\n      // https://github.com/catamphetamine/libphonenumber-js/issues/376\n      if (country || callingCode) {\n        var _extractCountryCallin = extractCountryCallingCodeFromInternationalNumberWithoutPlusSign(number, country, callingCode, metadata),\n          countryCallingCode = _extractCountryCallin.countryCallingCode,\n          shorterNumber = _extractCountryCallin.number;\n        if (countryCallingCode) {\n          return {\n            countryCallingCodeSource: 'FROM_NUMBER_WITHOUT_PLUS_SIGN',\n            countryCallingCode: countryCallingCode,\n            number: shorterNumber\n          };\n        }\n      }\n      return {\n        // No need to set it to `UNSPECIFIED`. It can be just `undefined`.\n        // countryCallingCodeSource: 'UNSPECIFIED',\n        number: number\n      };\n    }\n  } // Fast abortion: country codes do not begin with a '0'\n\n  if (number[1] === '0') {\n    return {};\n  }\n  metadata = new Metadata(metadata); // The thing with country phone codes\n  // is that they are orthogonal to each other\n  // i.e. there's no such country phone code A\n  // for which country phone code B exists\n  // where B starts with A.\n  // Therefore, while scanning digits,\n  // if a valid country code is found,\n  // that means that it is the country code.\n  //\n\n  var i = 2;\n  while (i - 1 <= MAX_LENGTH_COUNTRY_CODE && i <= number.length) {\n    var _countryCallingCode = number.slice(1, i);\n    if (metadata.hasCallingCode(_countryCallingCode)) {\n      metadata.selectNumberingPlan(_countryCallingCode);\n      return {\n        countryCallingCodeSource: isNumberWithIddPrefix ? 'FROM_NUMBER_WITH_IDD' : 'FROM_NUMBER_WITH_PLUS_SIGN',\n        countryCallingCode: _countryCallingCode,\n        number: number.slice(i)\n      };\n    }\n    i++;\n  }\n  return {};\n} // The possible values for the returned `countryCallingCodeSource` are:\n//\n// Copy-pasted from:\n// https://github.com/google/libphonenumber/blob/master/resources/phonenumber.proto\n//\n// // The source from which the country_code is derived. This is not set in the\n// // general parsing method, but in the method that parses and keeps raw_input.\n// // New fields could be added upon request.\n// enum CountryCodeSource {\n//  // Default value returned if this is not set, because the phone number was\n//  // created using parse, not parseAndKeepRawInput. hasCountryCodeSource will\n//  // return false if this is the case.\n//  UNSPECIFIED = 0;\n//\n//  // The country_code is derived based on a phone number with a leading \"+\",\n//  // e.g. the French number \"+33 1 42 68 53 00\".\n//  FROM_NUMBER_WITH_PLUS_SIGN = 1;\n//\n//  // The country_code is derived based on a phone number with a leading IDD,\n//  // e.g. the French number \"011 33 1 42 68 53 00\", as it is dialled from US.\n//  FROM_NUMBER_WITH_IDD = 5;\n//\n//  // The country_code is derived based on a phone number without a leading\n//  // \"+\", e.g. the French number \"33 1 42 68 53 00\" when defaultCountry is\n//  // supplied as France.\n//  FROM_NUMBER_WITHOUT_PLUS_SIGN = 10;\n//\n//  // The country_code is derived NOT based on the phone number itself, but\n//  // from the defaultCountry parameter provided in the parsing function by the\n//  // clients. This happens mostly for numbers written in the national format\n//  // (without country code). For example, this would be set when parsing the\n//  // French number \"01 42 68 53 00\", when defaultCountry is supplied as\n//  // France.\n//  FROM_DEFAULT_COUNTRY = 20;\n// }", "map": {"version": 3, "names": ["stripIddPrefix", "extractCountryCallingCodeFromInternationalNumberWithoutPlusSign", "<PERSON><PERSON><PERSON>", "MAX_LENGTH_COUNTRY_CODE", "extractCountryCallingCode", "number", "country", "callingCode", "metadata", "isNumberWithIddPrefix", "numberWithoutIDD", "_extractCountryCallin", "countryCallingCode", "shorterNumber", "countryCallingCodeSource", "i", "length", "_countryCallingCode", "slice", "hasCallingCode", "selectNumberingPlan"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/helpers/extractCountryCallingCode.js"], "sourcesContent": ["import stripIddPrefix from './stripIddPrefix.js';\nimport extractCountryCallingCodeFromInternationalNumberWithoutPlusSign from './extractCountryCallingCodeFromInternationalNumberWithoutPlusSign.js';\nimport Metadata from '../metadata.js';\nimport { MAX_LENGTH_COUNTRY_CODE } from '../constants.js';\n/**\r\n * Converts a phone number digits (possibly with a `+`)\r\n * into a calling code and the rest phone number digits.\r\n * The \"rest phone number digits\" could include\r\n * a national prefix, carrier code, and national\r\n * (significant) number.\r\n * @param  {string} number — Phone number digits (possibly with a `+`).\r\n * @param  {string} [country] — Default country.\r\n * @param  {string} [callingCode] — Default calling code (some phone numbering plans are non-geographic).\r\n * @param  {object} metadata\r\n * @return {object} `{ countryCallingCodeSource: string?, countryCallingCode: string?, number: string }`\r\n * @example\r\n * // Returns `{ countryCallingCode: \"1\", number: \"2133734253\" }`.\r\n * extractCountryCallingCode('2133734253', 'US', null, metadata)\r\n * extractCountryCallingCode('2133734253', null, '1', metadata)\r\n * extractCountryCallingCode('+12133734253', null, null, metadata)\r\n * extractCountryCallingCode('+12133734253', 'RU', null, metadata)\r\n */\n\nexport default function extractCountryCallingCode(number, country, callingCode, metadata) {\n  if (!number) {\n    return {};\n  }\n\n  var isNumberWithIddPrefix; // If this is not an international phone number,\n  // then either extract an \"IDD\" prefix, or extract a\n  // country calling code from a number by autocorrecting it\n  // by prepending a leading `+` in cases when it starts\n  // with the country calling code.\n  // https://wikitravel.org/en/International_dialling_prefix\n  // https://github.com/catamphetamine/libphonenumber-js/issues/376\n\n  if (number[0] !== '+') {\n    // Convert an \"out-of-country\" dialing phone number\n    // to a proper international phone number.\n    var numberWithoutIDD = stripIddPrefix(number, country, callingCode, metadata); // If an IDD prefix was stripped then\n    // convert the number to international one\n    // for subsequent parsing.\n\n    if (numberWithoutIDD && numberWithoutIDD !== number) {\n      isNumberWithIddPrefix = true;\n      number = '+' + numberWithoutIDD;\n    } else {\n      // Check to see if the number starts with the country calling code\n      // for the default country. If so, we remove the country calling code,\n      // and do some checks on the validity of the number before and after.\n      // https://github.com/catamphetamine/libphonenumber-js/issues/376\n      if (country || callingCode) {\n        var _extractCountryCallin = extractCountryCallingCodeFromInternationalNumberWithoutPlusSign(number, country, callingCode, metadata),\n            countryCallingCode = _extractCountryCallin.countryCallingCode,\n            shorterNumber = _extractCountryCallin.number;\n\n        if (countryCallingCode) {\n          return {\n            countryCallingCodeSource: 'FROM_NUMBER_WITHOUT_PLUS_SIGN',\n            countryCallingCode: countryCallingCode,\n            number: shorterNumber\n          };\n        }\n      }\n\n      return {\n        // No need to set it to `UNSPECIFIED`. It can be just `undefined`.\n        // countryCallingCodeSource: 'UNSPECIFIED',\n        number: number\n      };\n    }\n  } // Fast abortion: country codes do not begin with a '0'\n\n\n  if (number[1] === '0') {\n    return {};\n  }\n\n  metadata = new Metadata(metadata); // The thing with country phone codes\n  // is that they are orthogonal to each other\n  // i.e. there's no such country phone code A\n  // for which country phone code B exists\n  // where B starts with A.\n  // Therefore, while scanning digits,\n  // if a valid country code is found,\n  // that means that it is the country code.\n  //\n\n  var i = 2;\n\n  while (i - 1 <= MAX_LENGTH_COUNTRY_CODE && i <= number.length) {\n    var _countryCallingCode = number.slice(1, i);\n\n    if (metadata.hasCallingCode(_countryCallingCode)) {\n      metadata.selectNumberingPlan(_countryCallingCode);\n      return {\n        countryCallingCodeSource: isNumberWithIddPrefix ? 'FROM_NUMBER_WITH_IDD' : 'FROM_NUMBER_WITH_PLUS_SIGN',\n        countryCallingCode: _countryCallingCode,\n        number: number.slice(i)\n      };\n    }\n\n    i++;\n  }\n\n  return {};\n} // The possible values for the returned `countryCallingCodeSource` are:\n//\n// Copy-pasted from:\n// https://github.com/google/libphonenumber/blob/master/resources/phonenumber.proto\n//\n// // The source from which the country_code is derived. This is not set in the\n// // general parsing method, but in the method that parses and keeps raw_input.\n// // New fields could be added upon request.\n// enum CountryCodeSource {\n//  // Default value returned if this is not set, because the phone number was\n//  // created using parse, not parseAndKeepRawInput. hasCountryCodeSource will\n//  // return false if this is the case.\n//  UNSPECIFIED = 0;\n//\n//  // The country_code is derived based on a phone number with a leading \"+\",\n//  // e.g. the French number \"+33 1 42 68 53 00\".\n//  FROM_NUMBER_WITH_PLUS_SIGN = 1;\n//\n//  // The country_code is derived based on a phone number with a leading IDD,\n//  // e.g. the French number \"011 33 1 42 68 53 00\", as it is dialled from US.\n//  FROM_NUMBER_WITH_IDD = 5;\n//\n//  // The country_code is derived based on a phone number without a leading\n//  // \"+\", e.g. the French number \"33 1 42 68 53 00\" when defaultCountry is\n//  // supplied as France.\n//  FROM_NUMBER_WITHOUT_PLUS_SIGN = 10;\n//\n//  // The country_code is derived NOT based on the phone number itself, but\n//  // from the defaultCountry parameter provided in the parsing function by the\n//  // clients. This happens mostly for numbers written in the national format\n//  // (without country code). For example, this would be set when parsing the\n//  // French number \"01 42 68 53 00\", when defaultCountry is supplied as\n//  // France.\n//  FROM_DEFAULT_COUNTRY = 20;\n// }\n"], "mappings": "AAAA,OAAOA,cAAc,MAAM,qBAAqB;AAChD,OAAOC,+DAA+D,MAAM,sEAAsE;AAClJ,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,SAASC,uBAAuB,QAAQ,iBAAiB;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,yBAAyBA,CAACC,MAAM,EAAEC,OAAO,EAAEC,WAAW,EAAEC,QAAQ,EAAE;EACxF,IAAI,CAACH,MAAM,EAAE;IACX,OAAO,CAAC,CAAC;EACX;EAEA,IAAII,qBAAqB,CAAC,CAAC;EAC3B;EACA;EACA;EACA;EACA;EACA;;EAEA,IAAIJ,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACrB;IACA;IACA,IAAIK,gBAAgB,GAAGV,cAAc,CAACK,MAAM,EAAEC,OAAO,EAAEC,WAAW,EAAEC,QAAQ,CAAC,CAAC,CAAC;IAC/E;IACA;;IAEA,IAAIE,gBAAgB,IAAIA,gBAAgB,KAAKL,MAAM,EAAE;MACnDI,qBAAqB,GAAG,IAAI;MAC5BJ,MAAM,GAAG,GAAG,GAAGK,gBAAgB;IACjC,CAAC,MAAM;MACL;MACA;MACA;MACA;MACA,IAAIJ,OAAO,IAAIC,WAAW,EAAE;QAC1B,IAAII,qBAAqB,GAAGV,+DAA+D,CAACI,MAAM,EAAEC,OAAO,EAAEC,WAAW,EAAEC,QAAQ,CAAC;UAC/HI,kBAAkB,GAAGD,qBAAqB,CAACC,kBAAkB;UAC7DC,aAAa,GAAGF,qBAAqB,CAACN,MAAM;QAEhD,IAAIO,kBAAkB,EAAE;UACtB,OAAO;YACLE,wBAAwB,EAAE,+BAA+B;YACzDF,kBAAkB,EAAEA,kBAAkB;YACtCP,MAAM,EAAEQ;UACV,CAAC;QACH;MACF;MAEA,OAAO;QACL;QACA;QACAR,MAAM,EAAEA;MACV,CAAC;IACH;EACF,CAAC,CAAC;;EAGF,IAAIA,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACrB,OAAO,CAAC,CAAC;EACX;EAEAG,QAAQ,GAAG,IAAIN,QAAQ,CAACM,QAAQ,CAAC,CAAC,CAAC;EACnC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA,IAAIO,CAAC,GAAG,CAAC;EAET,OAAOA,CAAC,GAAG,CAAC,IAAIZ,uBAAuB,IAAIY,CAAC,IAAIV,MAAM,CAACW,MAAM,EAAE;IAC7D,IAAIC,mBAAmB,GAAGZ,MAAM,CAACa,KAAK,CAAC,CAAC,EAAEH,CAAC,CAAC;IAE5C,IAAIP,QAAQ,CAACW,cAAc,CAACF,mBAAmB,CAAC,EAAE;MAChDT,QAAQ,CAACY,mBAAmB,CAACH,mBAAmB,CAAC;MACjD,OAAO;QACLH,wBAAwB,EAAEL,qBAAqB,GAAG,sBAAsB,GAAG,4BAA4B;QACvGG,kBAAkB,EAAEK,mBAAmB;QACvCZ,MAAM,EAAEA,MAAM,CAACa,KAAK,CAACH,CAAC;MACxB,CAAC;IACH;IAEAA,CAAC,EAAE;EACL;EAEA,OAAO,CAAC,CAAC;AACX,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}