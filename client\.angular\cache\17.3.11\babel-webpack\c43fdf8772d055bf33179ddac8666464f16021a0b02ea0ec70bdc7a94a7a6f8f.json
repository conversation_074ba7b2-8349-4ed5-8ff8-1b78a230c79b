{"ast": null, "code": "function _createForOfIteratorHelperLoose(o, allowArrayLike) {\n  var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"];\n  if (it) return (it = it.call(o)).next.bind(it);\n  if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") {\n    if (it) o = it;\n    var i = 0;\n    return function () {\n      if (i >= o.length) return {\n        done: true\n      };\n      return {\n        done: false,\n        value: o[i++]\n      };\n    };\n  }\n  throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n  return arr2;\n}\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\n\n// This is a port of Google Android `libphonenumber`'s\n// `phonenumberutil.js` of December 31th, 2018.\n//\n// https://github.com/googlei18n/libphonenumber/commits/master/javascript/i18n/phonenumbers/phonenumberutil.js\nimport matchesEntirely from './helpers/matchesEntirely.js';\nimport formatNationalNumberUsingFormat from './helpers/formatNationalNumberUsingFormat.js';\nimport Metadata, { getCountryCallingCode } from './metadata.js';\nimport getIddPrefix from './helpers/getIddPrefix.js';\nimport { formatRFC3966 } from './helpers/RFC3966.js';\nvar DEFAULT_OPTIONS = {\n  formatExtension: function formatExtension(formattedNumber, extension, metadata) {\n    return \"\".concat(formattedNumber).concat(metadata.ext()).concat(extension);\n  }\n};\n/**\r\n * Formats a phone number.\r\n *\r\n * format(phoneNumberInstance, 'INTERNATIONAL', { ..., v2: true }, metadata)\r\n * format(phoneNumberInstance, 'NATIONAL', { ..., v2: true }, metadata)\r\n *\r\n * format({ phone: '8005553535', country: 'RU' }, 'INTERNATIONAL', { ... }, metadata)\r\n * format({ phone: '8005553535', country: 'RU' }, 'NATIONAL', undefined, metadata)\r\n *\r\n * @param  {object|PhoneNumber} input — If `options.v2: true` flag is passed, the `input` should be a `PhoneNumber` instance. Otherwise, it should be an object of shape `{ phone: '...', country: '...' }`.\r\n * @param  {string} format\r\n * @param  {object} [options]\r\n * @param  {object} metadata\r\n * @return {string}\r\n */\n\nexport default function formatNumber(input, format, options, metadata) {\n  // Apply default options.\n  if (options) {\n    options = _objectSpread(_objectSpread({}, DEFAULT_OPTIONS), options);\n  } else {\n    options = DEFAULT_OPTIONS;\n  }\n  metadata = new Metadata(metadata);\n  if (input.country && input.country !== '001') {\n    // Validate `input.country`.\n    if (!metadata.hasCountry(input.country)) {\n      throw new Error(\"Unknown country: \".concat(input.country));\n    }\n    metadata.country(input.country);\n  } else if (input.countryCallingCode) {\n    metadata.selectNumberingPlan(input.countryCallingCode);\n  } else return input.phone || '';\n  var countryCallingCode = metadata.countryCallingCode();\n  var nationalNumber = options.v2 ? input.nationalNumber : input.phone; // This variable should have been declared inside `case`s\n  // but Babel has a bug and it says \"duplicate variable declaration\".\n\n  var number;\n  switch (format) {\n    case 'NATIONAL':\n      // Legacy argument support.\n      // (`{ country: ..., phone: '' }`)\n      if (!nationalNumber) {\n        return '';\n      }\n      number = formatNationalNumber(nationalNumber, input.carrierCode, 'NATIONAL', metadata, options);\n      return addExtension(number, input.ext, metadata, options.formatExtension);\n    case 'INTERNATIONAL':\n      // Legacy argument support.\n      // (`{ country: ..., phone: '' }`)\n      if (!nationalNumber) {\n        return \"+\".concat(countryCallingCode);\n      }\n      number = formatNationalNumber(nationalNumber, null, 'INTERNATIONAL', metadata, options);\n      number = \"+\".concat(countryCallingCode, \" \").concat(number);\n      return addExtension(number, input.ext, metadata, options.formatExtension);\n    case 'E.164':\n      // `E.164` doesn't define \"phone number extensions\".\n      return \"+\".concat(countryCallingCode).concat(nationalNumber);\n    case 'RFC3966':\n      return formatRFC3966({\n        number: \"+\".concat(countryCallingCode).concat(nationalNumber),\n        ext: input.ext\n      });\n    // For reference, here's Google's IDD formatter:\n    // https://github.com/google/libphonenumber/blob/32719cf74e68796788d1ca45abc85dcdc63ba5b9/java/libphonenumber/src/com/google/i18n/phonenumbers/PhoneNumberUtil.java#L1546\n    // Not saying that this IDD formatter replicates it 1:1, but it seems to work.\n    // Who would even need to format phone numbers in IDD format anyway?\n\n    case 'IDD':\n      if (!options.fromCountry) {\n        return; // throw new Error('`fromCountry` option not passed for IDD-prefixed formatting.')\n      }\n      var formattedNumber = formatIDD(nationalNumber, input.carrierCode, countryCallingCode, options.fromCountry, metadata);\n      return addExtension(formattedNumber, input.ext, metadata, options.formatExtension);\n    default:\n      throw new Error(\"Unknown \\\"format\\\" argument passed to \\\"formatNumber()\\\": \\\"\".concat(format, \"\\\"\"));\n  }\n}\nfunction formatNationalNumber(number, carrierCode, formatAs, metadata, options) {\n  var format = chooseFormatForNumber(metadata.formats(), number);\n  if (!format) {\n    return number;\n  }\n  return formatNationalNumberUsingFormat(number, format, {\n    useInternationalFormat: formatAs === 'INTERNATIONAL',\n    withNationalPrefix: format.nationalPrefixIsOptionalWhenFormattingInNationalFormat() && options && options.nationalPrefix === false ? false : true,\n    carrierCode: carrierCode,\n    metadata: metadata\n  });\n}\nexport function chooseFormatForNumber(availableFormats, nationalNnumber) {\n  for (var _iterator = _createForOfIteratorHelperLoose(availableFormats), _step; !(_step = _iterator()).done;) {\n    var format = _step.value;\n\n    // Validate leading digits.\n    // The test case for \"else path\" could be found by searching for\n    // \"format.leadingDigitsPatterns().length === 0\".\n    if (format.leadingDigitsPatterns().length > 0) {\n      // The last leading_digits_pattern is used here, as it is the most detailed\n      var lastLeadingDigitsPattern = format.leadingDigitsPatterns()[format.leadingDigitsPatterns().length - 1]; // If leading digits don't match then move on to the next phone number format\n\n      if (nationalNnumber.search(lastLeadingDigitsPattern) !== 0) {\n        continue;\n      }\n    } // Check that the national number matches the phone number format regular expression\n\n    if (matchesEntirely(nationalNnumber, format.pattern())) {\n      return format;\n    }\n  }\n}\nfunction addExtension(formattedNumber, ext, metadata, formatExtension) {\n  return ext ? formatExtension(formattedNumber, ext, metadata) : formattedNumber;\n}\nfunction formatIDD(nationalNumber, carrierCode, countryCallingCode, fromCountry, metadata) {\n  var fromCountryCallingCode = getCountryCallingCode(fromCountry, metadata.metadata); // When calling within the same country calling code.\n\n  if (fromCountryCallingCode === countryCallingCode) {\n    var formattedNumber = formatNationalNumber(nationalNumber, carrierCode, 'NATIONAL', metadata); // For NANPA regions, return the national format for these regions\n    // but prefix it with the country calling code.\n\n    if (countryCallingCode === '1') {\n      return countryCallingCode + ' ' + formattedNumber;\n    } // If regions share a country calling code, the country calling code need\n    // not be dialled. This also applies when dialling within a region, so this\n    // if clause covers both these cases. Technically this is the case for\n    // dialling from La Reunion to other overseas departments of France (French\n    // Guiana, Martinique, Guadeloupe), but not vice versa - so we don't cover\n    // this edge case for now and for those cases return the version including\n    // country calling code. Details here:\n    // http://www.petitfute.com/voyage/225-info-pratiques-reunion\n    //\n\n    return formattedNumber;\n  }\n  var iddPrefix = getIddPrefix(fromCountry, undefined, metadata.metadata);\n  if (iddPrefix) {\n    return \"\".concat(iddPrefix, \" \").concat(countryCallingCode, \" \").concat(formatNationalNumber(nationalNumber, null, 'INTERNATIONAL', metadata));\n  }\n}", "map": {"version": 3, "names": ["_createForOfIteratorHelperLoose", "o", "allowArrayLike", "it", "Symbol", "iterator", "call", "next", "bind", "Array", "isArray", "_unsupportedIterableToArray", "length", "i", "done", "value", "TypeError", "minLen", "_arrayLikeToArray", "n", "Object", "prototype", "toString", "slice", "constructor", "name", "from", "test", "arr", "len", "arr2", "ownKeys", "object", "enumerableOnly", "keys", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "target", "arguments", "source", "for<PERSON>ach", "key", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "obj", "configurable", "writable", "matchesEntirely", "formatNationalNumberUsingFormat", "<PERSON><PERSON><PERSON>", "getCountryCallingCode", "getIddPrefix", "formatRFC3966", "DEFAULT_OPTIONS", "formatExtension", "formattedNumber", "extension", "metadata", "concat", "ext", "formatNumber", "input", "format", "options", "country", "hasCountry", "Error", "countryCallingCode", "selectNumberingPlan", "phone", "nationalNumber", "v2", "number", "formatNationalNumber", "carrierCode", "addExtension", "fromCountry", "formatIDD", "formatAs", "chooseFormatForNumber", "formats", "useInternationalFormat", "withNationalPrefix", "nationalPrefixIsOptionalWhenFormattingInNationalFormat", "nationalPrefix", "availableFormats", "nationalNnumber", "_iterator", "_step", "leadingDigitsPatterns", "lastLeadingDigitsPattern", "search", "pattern", "fromCountryCallingCode", "iddPrefix", "undefined"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/format.js"], "sourcesContent": ["function _createForOfIteratorHelperLoose(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (it) return (it = it.call(o)).next.bind(it); if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; return function () { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n// This is a port of Google Android `libphonenumber`'s\n// `phonenumberutil.js` of December 31th, 2018.\n//\n// https://github.com/googlei18n/libphonenumber/commits/master/javascript/i18n/phonenumbers/phonenumberutil.js\nimport matchesEntirely from './helpers/matchesEntirely.js';\nimport formatNationalNumberUsingFormat from './helpers/formatNationalNumberUsingFormat.js';\nimport Metadata, { getCountryCallingCode } from './metadata.js';\nimport getIddPrefix from './helpers/getIddPrefix.js';\nimport { formatRFC3966 } from './helpers/RFC3966.js';\nvar DEFAULT_OPTIONS = {\n  formatExtension: function formatExtension(formattedNumber, extension, metadata) {\n    return \"\".concat(formattedNumber).concat(metadata.ext()).concat(extension);\n  }\n};\n/**\r\n * Formats a phone number.\r\n *\r\n * format(phoneNumberInstance, 'INTERNATIONAL', { ..., v2: true }, metadata)\r\n * format(phoneNumberInstance, 'NATIONAL', { ..., v2: true }, metadata)\r\n *\r\n * format({ phone: '8005553535', country: 'RU' }, 'INTERNATIONAL', { ... }, metadata)\r\n * format({ phone: '8005553535', country: 'RU' }, 'NATIONAL', undefined, metadata)\r\n *\r\n * @param  {object|PhoneNumber} input — If `options.v2: true` flag is passed, the `input` should be a `PhoneNumber` instance. Otherwise, it should be an object of shape `{ phone: '...', country: '...' }`.\r\n * @param  {string} format\r\n * @param  {object} [options]\r\n * @param  {object} metadata\r\n * @return {string}\r\n */\n\nexport default function formatNumber(input, format, options, metadata) {\n  // Apply default options.\n  if (options) {\n    options = _objectSpread(_objectSpread({}, DEFAULT_OPTIONS), options);\n  } else {\n    options = DEFAULT_OPTIONS;\n  }\n\n  metadata = new Metadata(metadata);\n\n  if (input.country && input.country !== '001') {\n    // Validate `input.country`.\n    if (!metadata.hasCountry(input.country)) {\n      throw new Error(\"Unknown country: \".concat(input.country));\n    }\n\n    metadata.country(input.country);\n  } else if (input.countryCallingCode) {\n    metadata.selectNumberingPlan(input.countryCallingCode);\n  } else return input.phone || '';\n\n  var countryCallingCode = metadata.countryCallingCode();\n  var nationalNumber = options.v2 ? input.nationalNumber : input.phone; // This variable should have been declared inside `case`s\n  // but Babel has a bug and it says \"duplicate variable declaration\".\n\n  var number;\n\n  switch (format) {\n    case 'NATIONAL':\n      // Legacy argument support.\n      // (`{ country: ..., phone: '' }`)\n      if (!nationalNumber) {\n        return '';\n      }\n\n      number = formatNationalNumber(nationalNumber, input.carrierCode, 'NATIONAL', metadata, options);\n      return addExtension(number, input.ext, metadata, options.formatExtension);\n\n    case 'INTERNATIONAL':\n      // Legacy argument support.\n      // (`{ country: ..., phone: '' }`)\n      if (!nationalNumber) {\n        return \"+\".concat(countryCallingCode);\n      }\n\n      number = formatNationalNumber(nationalNumber, null, 'INTERNATIONAL', metadata, options);\n      number = \"+\".concat(countryCallingCode, \" \").concat(number);\n      return addExtension(number, input.ext, metadata, options.formatExtension);\n\n    case 'E.164':\n      // `E.164` doesn't define \"phone number extensions\".\n      return \"+\".concat(countryCallingCode).concat(nationalNumber);\n\n    case 'RFC3966':\n      return formatRFC3966({\n        number: \"+\".concat(countryCallingCode).concat(nationalNumber),\n        ext: input.ext\n      });\n    // For reference, here's Google's IDD formatter:\n    // https://github.com/google/libphonenumber/blob/32719cf74e68796788d1ca45abc85dcdc63ba5b9/java/libphonenumber/src/com/google/i18n/phonenumbers/PhoneNumberUtil.java#L1546\n    // Not saying that this IDD formatter replicates it 1:1, but it seems to work.\n    // Who would even need to format phone numbers in IDD format anyway?\n\n    case 'IDD':\n      if (!options.fromCountry) {\n        return; // throw new Error('`fromCountry` option not passed for IDD-prefixed formatting.')\n      }\n\n      var formattedNumber = formatIDD(nationalNumber, input.carrierCode, countryCallingCode, options.fromCountry, metadata);\n      return addExtension(formattedNumber, input.ext, metadata, options.formatExtension);\n\n    default:\n      throw new Error(\"Unknown \\\"format\\\" argument passed to \\\"formatNumber()\\\": \\\"\".concat(format, \"\\\"\"));\n  }\n}\n\nfunction formatNationalNumber(number, carrierCode, formatAs, metadata, options) {\n  var format = chooseFormatForNumber(metadata.formats(), number);\n\n  if (!format) {\n    return number;\n  }\n\n  return formatNationalNumberUsingFormat(number, format, {\n    useInternationalFormat: formatAs === 'INTERNATIONAL',\n    withNationalPrefix: format.nationalPrefixIsOptionalWhenFormattingInNationalFormat() && options && options.nationalPrefix === false ? false : true,\n    carrierCode: carrierCode,\n    metadata: metadata\n  });\n}\n\nexport function chooseFormatForNumber(availableFormats, nationalNnumber) {\n  for (var _iterator = _createForOfIteratorHelperLoose(availableFormats), _step; !(_step = _iterator()).done;) {\n    var format = _step.value;\n\n    // Validate leading digits.\n    // The test case for \"else path\" could be found by searching for\n    // \"format.leadingDigitsPatterns().length === 0\".\n    if (format.leadingDigitsPatterns().length > 0) {\n      // The last leading_digits_pattern is used here, as it is the most detailed\n      var lastLeadingDigitsPattern = format.leadingDigitsPatterns()[format.leadingDigitsPatterns().length - 1]; // If leading digits don't match then move on to the next phone number format\n\n      if (nationalNnumber.search(lastLeadingDigitsPattern) !== 0) {\n        continue;\n      }\n    } // Check that the national number matches the phone number format regular expression\n\n\n    if (matchesEntirely(nationalNnumber, format.pattern())) {\n      return format;\n    }\n  }\n}\n\nfunction addExtension(formattedNumber, ext, metadata, formatExtension) {\n  return ext ? formatExtension(formattedNumber, ext, metadata) : formattedNumber;\n}\n\nfunction formatIDD(nationalNumber, carrierCode, countryCallingCode, fromCountry, metadata) {\n  var fromCountryCallingCode = getCountryCallingCode(fromCountry, metadata.metadata); // When calling within the same country calling code.\n\n  if (fromCountryCallingCode === countryCallingCode) {\n    var formattedNumber = formatNationalNumber(nationalNumber, carrierCode, 'NATIONAL', metadata); // For NANPA regions, return the national format for these regions\n    // but prefix it with the country calling code.\n\n    if (countryCallingCode === '1') {\n      return countryCallingCode + ' ' + formattedNumber;\n    } // If regions share a country calling code, the country calling code need\n    // not be dialled. This also applies when dialling within a region, so this\n    // if clause covers both these cases. Technically this is the case for\n    // dialling from La Reunion to other overseas departments of France (French\n    // Guiana, Martinique, Guadeloupe), but not vice versa - so we don't cover\n    // this edge case for now and for those cases return the version including\n    // country calling code. Details here:\n    // http://www.petitfute.com/voyage/225-info-pratiques-reunion\n    //\n\n\n    return formattedNumber;\n  }\n\n  var iddPrefix = getIddPrefix(fromCountry, undefined, metadata.metadata);\n\n  if (iddPrefix) {\n    return \"\".concat(iddPrefix, \" \").concat(countryCallingCode, \" \").concat(formatNationalNumber(nationalNumber, null, 'INTERNATIONAL', metadata));\n  }\n}\n"], "mappings": "AAAA,SAASA,+BAA+BA,CAACC,CAAC,EAAEC,cAAc,EAAE;EAAE,IAAIC,EAAE,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAIH,CAAC,CAACG,MAAM,CAACC,QAAQ,CAAC,IAAIJ,CAAC,CAAC,YAAY,CAAC;EAAE,IAAIE,EAAE,EAAE,OAAO,CAACA,EAAE,GAAGA,EAAE,CAACG,IAAI,CAACL,CAAC,CAAC,EAAEM,IAAI,CAACC,IAAI,CAACL,EAAE,CAAC;EAAE,IAAIM,KAAK,CAACC,OAAO,CAACT,CAAC,CAAC,KAAKE,EAAE,GAAGQ,2BAA2B,CAACV,CAAC,CAAC,CAAC,IAAIC,cAAc,IAAID,CAAC,IAAI,OAAOA,CAAC,CAACW,MAAM,KAAK,QAAQ,EAAE;IAAE,IAAIT,EAAE,EAAEF,CAAC,GAAGE,EAAE;IAAE,IAAIU,CAAC,GAAG,CAAC;IAAE,OAAO,YAAY;MAAE,IAAIA,CAAC,IAAIZ,CAAC,CAACW,MAAM,EAAE,OAAO;QAAEE,IAAI,EAAE;MAAK,CAAC;MAAE,OAAO;QAAEA,IAAI,EAAE,KAAK;QAAEC,KAAK,EAAEd,CAAC,CAACY,CAAC,EAAE;MAAE,CAAC;IAAE,CAAC;EAAE;EAAE,MAAM,IAAIG,SAAS,CAAC,uIAAuI,CAAC;AAAE;AAE3lB,SAASL,2BAA2BA,CAACV,CAAC,EAAEgB,MAAM,EAAE;EAAE,IAAI,CAAChB,CAAC,EAAE;EAAQ,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOiB,iBAAiB,CAACjB,CAAC,EAAEgB,MAAM,CAAC;EAAE,IAAIE,CAAC,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAAChB,IAAI,CAACL,CAAC,CAAC,CAACsB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAE,IAAIJ,CAAC,KAAK,QAAQ,IAAIlB,CAAC,CAACuB,WAAW,EAAEL,CAAC,GAAGlB,CAAC,CAACuB,WAAW,CAACC,IAAI;EAAE,IAAIN,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOV,KAAK,CAACiB,IAAI,CAACzB,CAAC,CAAC;EAAE,IAAIkB,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACQ,IAAI,CAACR,CAAC,CAAC,EAAE,OAAOD,iBAAiB,CAACjB,CAAC,EAAEgB,MAAM,CAAC;AAAE;AAE/Z,SAASC,iBAAiBA,CAACU,GAAG,EAAEC,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGD,GAAG,CAAChB,MAAM,EAAEiB,GAAG,GAAGD,GAAG,CAAChB,MAAM;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEiB,IAAI,GAAG,IAAIrB,KAAK,CAACoB,GAAG,CAAC,EAAEhB,CAAC,GAAGgB,GAAG,EAAEhB,CAAC,EAAE,EAAE;IAAEiB,IAAI,CAACjB,CAAC,CAAC,GAAGe,GAAG,CAACf,CAAC,CAAC;EAAE;EAAE,OAAOiB,IAAI;AAAE;AAEtL,SAASC,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIC,IAAI,GAAGd,MAAM,CAACc,IAAI,CAACF,MAAM,CAAC;EAAE,IAAIZ,MAAM,CAACe,qBAAqB,EAAE;IAAE,IAAIC,OAAO,GAAGhB,MAAM,CAACe,qBAAqB,CAACH,MAAM,CAAC;IAAEC,cAAc,KAAKG,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;MAAE,OAAOlB,MAAM,CAACmB,wBAAwB,CAACP,MAAM,EAAEM,GAAG,CAAC,CAACE,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEN,IAAI,CAACO,IAAI,CAACC,KAAK,CAACR,IAAI,EAAEE,OAAO,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AAEpV,SAASS,aAAaA,CAACC,MAAM,EAAE;EAAE,KAAK,IAAI/B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgC,SAAS,CAACjC,MAAM,EAAEC,CAAC,EAAE,EAAE;IAAE,IAAIiC,MAAM,GAAG,IAAI,IAAID,SAAS,CAAChC,CAAC,CAAC,GAAGgC,SAAS,CAAChC,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGkB,OAAO,CAACX,MAAM,CAAC0B,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;MAAEC,eAAe,CAACL,MAAM,EAAEI,GAAG,EAAEF,MAAM,CAACE,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC,GAAG5B,MAAM,CAAC8B,yBAAyB,GAAG9B,MAAM,CAAC+B,gBAAgB,CAACP,MAAM,EAAExB,MAAM,CAAC8B,yBAAyB,CAACJ,MAAM,CAAC,CAAC,GAAGf,OAAO,CAACX,MAAM,CAAC0B,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;MAAE5B,MAAM,CAACgC,cAAc,CAACR,MAAM,EAAEI,GAAG,EAAE5B,MAAM,CAACmB,wBAAwB,CAACO,MAAM,EAAEE,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOJ,MAAM;AAAE;AAEzf,SAASK,eAAeA,CAACI,GAAG,EAAEL,GAAG,EAAEjC,KAAK,EAAE;EAAE,IAAIiC,GAAG,IAAIK,GAAG,EAAE;IAAEjC,MAAM,CAACgC,cAAc,CAACC,GAAG,EAAEL,GAAG,EAAE;MAAEjC,KAAK,EAAEA,KAAK;MAAEyB,UAAU,EAAE,IAAI;MAAEc,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEF,GAAG,CAACL,GAAG,CAAC,GAAGjC,KAAK;EAAE;EAAE,OAAOsC,GAAG;AAAE;;AAEhN;AACA;AACA;AACA;AACA,OAAOG,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,+BAA+B,MAAM,8CAA8C;AAC1F,OAAOC,QAAQ,IAAIC,qBAAqB,QAAQ,eAAe;AAC/D,OAAOC,YAAY,MAAM,2BAA2B;AACpD,SAASC,aAAa,QAAQ,sBAAsB;AACpD,IAAIC,eAAe,GAAG;EACpBC,eAAe,EAAE,SAASA,eAAeA,CAACC,eAAe,EAAEC,SAAS,EAAEC,QAAQ,EAAE;IAC9E,OAAO,EAAE,CAACC,MAAM,CAACH,eAAe,CAAC,CAACG,MAAM,CAACD,QAAQ,CAACE,GAAG,CAAC,CAAC,CAAC,CAACD,MAAM,CAACF,SAAS,CAAC;EAC5E;AACF,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASI,YAAYA,CAACC,KAAK,EAAEC,MAAM,EAAEC,OAAO,EAAEN,QAAQ,EAAE;EACrE;EACA,IAAIM,OAAO,EAAE;IACXA,OAAO,GAAG7B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmB,eAAe,CAAC,EAAEU,OAAO,CAAC;EACtE,CAAC,MAAM;IACLA,OAAO,GAAGV,eAAe;EAC3B;EAEAI,QAAQ,GAAG,IAAIR,QAAQ,CAACQ,QAAQ,CAAC;EAEjC,IAAII,KAAK,CAACG,OAAO,IAAIH,KAAK,CAACG,OAAO,KAAK,KAAK,EAAE;IAC5C;IACA,IAAI,CAACP,QAAQ,CAACQ,UAAU,CAACJ,KAAK,CAACG,OAAO,CAAC,EAAE;MACvC,MAAM,IAAIE,KAAK,CAAC,mBAAmB,CAACR,MAAM,CAACG,KAAK,CAACG,OAAO,CAAC,CAAC;IAC5D;IAEAP,QAAQ,CAACO,OAAO,CAACH,KAAK,CAACG,OAAO,CAAC;EACjC,CAAC,MAAM,IAAIH,KAAK,CAACM,kBAAkB,EAAE;IACnCV,QAAQ,CAACW,mBAAmB,CAACP,KAAK,CAACM,kBAAkB,CAAC;EACxD,CAAC,MAAM,OAAON,KAAK,CAACQ,KAAK,IAAI,EAAE;EAE/B,IAAIF,kBAAkB,GAAGV,QAAQ,CAACU,kBAAkB,CAAC,CAAC;EACtD,IAAIG,cAAc,GAAGP,OAAO,CAACQ,EAAE,GAAGV,KAAK,CAACS,cAAc,GAAGT,KAAK,CAACQ,KAAK,CAAC,CAAC;EACtE;;EAEA,IAAIG,MAAM;EAEV,QAAQV,MAAM;IACZ,KAAK,UAAU;MACb;MACA;MACA,IAAI,CAACQ,cAAc,EAAE;QACnB,OAAO,EAAE;MACX;MAEAE,MAAM,GAAGC,oBAAoB,CAACH,cAAc,EAAET,KAAK,CAACa,WAAW,EAAE,UAAU,EAAEjB,QAAQ,EAAEM,OAAO,CAAC;MAC/F,OAAOY,YAAY,CAACH,MAAM,EAAEX,KAAK,CAACF,GAAG,EAAEF,QAAQ,EAAEM,OAAO,CAACT,eAAe,CAAC;IAE3E,KAAK,eAAe;MAClB;MACA;MACA,IAAI,CAACgB,cAAc,EAAE;QACnB,OAAO,GAAG,CAACZ,MAAM,CAACS,kBAAkB,CAAC;MACvC;MAEAK,MAAM,GAAGC,oBAAoB,CAACH,cAAc,EAAE,IAAI,EAAE,eAAe,EAAEb,QAAQ,EAAEM,OAAO,CAAC;MACvFS,MAAM,GAAG,GAAG,CAACd,MAAM,CAACS,kBAAkB,EAAE,GAAG,CAAC,CAACT,MAAM,CAACc,MAAM,CAAC;MAC3D,OAAOG,YAAY,CAACH,MAAM,EAAEX,KAAK,CAACF,GAAG,EAAEF,QAAQ,EAAEM,OAAO,CAACT,eAAe,CAAC;IAE3E,KAAK,OAAO;MACV;MACA,OAAO,GAAG,CAACI,MAAM,CAACS,kBAAkB,CAAC,CAACT,MAAM,CAACY,cAAc,CAAC;IAE9D,KAAK,SAAS;MACZ,OAAOlB,aAAa,CAAC;QACnBoB,MAAM,EAAE,GAAG,CAACd,MAAM,CAACS,kBAAkB,CAAC,CAACT,MAAM,CAACY,cAAc,CAAC;QAC7DX,GAAG,EAAEE,KAAK,CAACF;MACb,CAAC,CAAC;IACJ;IACA;IACA;IACA;;IAEA,KAAK,KAAK;MACR,IAAI,CAACI,OAAO,CAACa,WAAW,EAAE;QACxB,OAAO,CAAC;MACV;MAEA,IAAIrB,eAAe,GAAGsB,SAAS,CAACP,cAAc,EAAET,KAAK,CAACa,WAAW,EAAEP,kBAAkB,EAAEJ,OAAO,CAACa,WAAW,EAAEnB,QAAQ,CAAC;MACrH,OAAOkB,YAAY,CAACpB,eAAe,EAAEM,KAAK,CAACF,GAAG,EAAEF,QAAQ,EAAEM,OAAO,CAACT,eAAe,CAAC;IAEpF;MACE,MAAM,IAAIY,KAAK,CAAC,8DAA8D,CAACR,MAAM,CAACI,MAAM,EAAE,IAAI,CAAC,CAAC;EACxG;AACF;AAEA,SAASW,oBAAoBA,CAACD,MAAM,EAAEE,WAAW,EAAEI,QAAQ,EAAErB,QAAQ,EAAEM,OAAO,EAAE;EAC9E,IAAID,MAAM,GAAGiB,qBAAqB,CAACtB,QAAQ,CAACuB,OAAO,CAAC,CAAC,EAAER,MAAM,CAAC;EAE9D,IAAI,CAACV,MAAM,EAAE;IACX,OAAOU,MAAM;EACf;EAEA,OAAOxB,+BAA+B,CAACwB,MAAM,EAAEV,MAAM,EAAE;IACrDmB,sBAAsB,EAAEH,QAAQ,KAAK,eAAe;IACpDI,kBAAkB,EAAEpB,MAAM,CAACqB,sDAAsD,CAAC,CAAC,IAAIpB,OAAO,IAAIA,OAAO,CAACqB,cAAc,KAAK,KAAK,GAAG,KAAK,GAAG,IAAI;IACjJV,WAAW,EAAEA,WAAW;IACxBjB,QAAQ,EAAEA;EACZ,CAAC,CAAC;AACJ;AAEA,OAAO,SAASsB,qBAAqBA,CAACM,gBAAgB,EAAEC,eAAe,EAAE;EACvE,KAAK,IAAIC,SAAS,GAAGhG,+BAA+B,CAAC8F,gBAAgB,CAAC,EAAEG,KAAK,EAAE,CAAC,CAACA,KAAK,GAAGD,SAAS,CAAC,CAAC,EAAElF,IAAI,GAAG;IAC3G,IAAIyD,MAAM,GAAG0B,KAAK,CAAClF,KAAK;;IAExB;IACA;IACA;IACA,IAAIwD,MAAM,CAAC2B,qBAAqB,CAAC,CAAC,CAACtF,MAAM,GAAG,CAAC,EAAE;MAC7C;MACA,IAAIuF,wBAAwB,GAAG5B,MAAM,CAAC2B,qBAAqB,CAAC,CAAC,CAAC3B,MAAM,CAAC2B,qBAAqB,CAAC,CAAC,CAACtF,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;;MAE1G,IAAImF,eAAe,CAACK,MAAM,CAACD,wBAAwB,CAAC,KAAK,CAAC,EAAE;QAC1D;MACF;IACF,CAAC,CAAC;;IAGF,IAAI3C,eAAe,CAACuC,eAAe,EAAExB,MAAM,CAAC8B,OAAO,CAAC,CAAC,CAAC,EAAE;MACtD,OAAO9B,MAAM;IACf;EACF;AACF;AAEA,SAASa,YAAYA,CAACpB,eAAe,EAAEI,GAAG,EAAEF,QAAQ,EAAEH,eAAe,EAAE;EACrE,OAAOK,GAAG,GAAGL,eAAe,CAACC,eAAe,EAAEI,GAAG,EAAEF,QAAQ,CAAC,GAAGF,eAAe;AAChF;AAEA,SAASsB,SAASA,CAACP,cAAc,EAAEI,WAAW,EAAEP,kBAAkB,EAAES,WAAW,EAAEnB,QAAQ,EAAE;EACzF,IAAIoC,sBAAsB,GAAG3C,qBAAqB,CAAC0B,WAAW,EAAEnB,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC;;EAEpF,IAAIoC,sBAAsB,KAAK1B,kBAAkB,EAAE;IACjD,IAAIZ,eAAe,GAAGkB,oBAAoB,CAACH,cAAc,EAAEI,WAAW,EAAE,UAAU,EAAEjB,QAAQ,CAAC,CAAC,CAAC;IAC/F;;IAEA,IAAIU,kBAAkB,KAAK,GAAG,EAAE;MAC9B,OAAOA,kBAAkB,GAAG,GAAG,GAAGZ,eAAe;IACnD,CAAC,CAAC;IACF;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAGA,OAAOA,eAAe;EACxB;EAEA,IAAIuC,SAAS,GAAG3C,YAAY,CAACyB,WAAW,EAAEmB,SAAS,EAAEtC,QAAQ,CAACA,QAAQ,CAAC;EAEvE,IAAIqC,SAAS,EAAE;IACb,OAAO,EAAE,CAACpC,MAAM,CAACoC,SAAS,EAAE,GAAG,CAAC,CAACpC,MAAM,CAACS,kBAAkB,EAAE,GAAG,CAAC,CAACT,MAAM,CAACe,oBAAoB,CAACH,cAAc,EAAE,IAAI,EAAE,eAAe,EAAEb,QAAQ,CAAC,CAAC;EAChJ;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}