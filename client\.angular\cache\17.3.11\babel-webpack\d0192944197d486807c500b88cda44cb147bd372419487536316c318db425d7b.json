{"ast": null, "code": "/** Returns a regular expression quantifier with an upper and lower limit. */\nexport function limit(lower, upper) {\n  if (lower < 0 || upper <= 0 || upper < lower) {\n    throw new TypeError();\n  }\n  return \"{\".concat(lower, \",\").concat(upper, \"}\");\n}\n/**\r\n * Trims away any characters after the first match of {@code pattern} in {@code candidate},\r\n * returning the trimmed version.\r\n */\n\nexport function trimAfterFirstMatch(regexp, string) {\n  var index = string.search(regexp);\n  if (index >= 0) {\n    return string.slice(0, index);\n  }\n  return string;\n}\nexport function startsWith(string, substring) {\n  return string.indexOf(substring) === 0;\n}\nexport function endsWith(string, substring) {\n  return string.indexOf(substring, string.length - substring.length) === string.length - substring.length;\n}", "map": {"version": 3, "names": ["limit", "lower", "upper", "TypeError", "concat", "trimAfterFirstMatch", "regexp", "string", "index", "search", "slice", "startsWith", "substring", "indexOf", "endsWith", "length"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/findNumbers/util.js"], "sourcesContent": ["/** Returns a regular expression quantifier with an upper and lower limit. */\nexport function limit(lower, upper) {\n  if (lower < 0 || upper <= 0 || upper < lower) {\n    throw new TypeError();\n  }\n\n  return \"{\".concat(lower, \",\").concat(upper, \"}\");\n}\n/**\r\n * Trims away any characters after the first match of {@code pattern} in {@code candidate},\r\n * returning the trimmed version.\r\n */\n\nexport function trimAfterFirstMatch(regexp, string) {\n  var index = string.search(regexp);\n\n  if (index >= 0) {\n    return string.slice(0, index);\n  }\n\n  return string;\n}\nexport function startsWith(string, substring) {\n  return string.indexOf(substring) === 0;\n}\nexport function endsWith(string, substring) {\n  return string.indexOf(substring, string.length - substring.length) === string.length - substring.length;\n}\n"], "mappings": "AAAA;AACA,OAAO,SAASA,KAAKA,CAACC,KAAK,EAAEC,KAAK,EAAE;EAClC,IAAID,KAAK,GAAG,CAAC,IAAIC,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAGD,KAAK,EAAE;IAC5C,MAAM,IAAIE,SAAS,CAAC,CAAC;EACvB;EAEA,OAAO,GAAG,CAACC,MAAM,CAACH,KAAK,EAAE,GAAG,CAAC,CAACG,MAAM,CAACF,KAAK,EAAE,GAAG,CAAC;AAClD;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASG,mBAAmBA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAClD,IAAIC,KAAK,GAAGD,MAAM,CAACE,MAAM,CAACH,MAAM,CAAC;EAEjC,IAAIE,KAAK,IAAI,CAAC,EAAE;IACd,OAAOD,MAAM,CAACG,KAAK,CAAC,CAAC,EAAEF,KAAK,CAAC;EAC/B;EAEA,OAAOD,MAAM;AACf;AACA,OAAO,SAASI,UAAUA,CAACJ,MAAM,EAAEK,SAAS,EAAE;EAC5C,OAAOL,MAAM,CAACM,OAAO,CAACD,SAAS,CAAC,KAAK,CAAC;AACxC;AACA,OAAO,SAASE,QAAQA,CAACP,MAAM,EAAEK,SAAS,EAAE;EAC1C,OAAOL,MAAM,CAACM,OAAO,CAACD,SAAS,EAAEL,MAAM,CAACQ,MAAM,GAAGH,SAAS,CAACG,MAAM,CAAC,KAAKR,MAAM,CAACQ,MAAM,GAAGH,SAAS,CAACG,MAAM;AACzG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}