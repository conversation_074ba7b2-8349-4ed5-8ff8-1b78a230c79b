{"ast": null, "code": "function _createForOfIteratorHelperLoose(o, allowArrayLike) {\n  var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"];\n  if (it) return (it = it.call(o)).next.bind(it);\n  if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") {\n    if (it) o = it;\n    var i = 0;\n    return function () {\n      if (i >= o.length) return {\n        done: true\n      };\n      return {\n        done: false,\n        value: o[i++]\n      };\n    };\n  }\n  throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n  return arr2;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nimport PatternParser from './AsYouTypeFormatter.PatternParser.js';\nvar PatternMatcher = /*#__PURE__*/function () {\n  function PatternMatcher(pattern) {\n    _classCallCheck(this, PatternMatcher);\n    this.matchTree = new PatternParser().parse(pattern);\n  }\n  _createClass(PatternMatcher, [{\n    key: \"match\",\n    value: function match(string) {\n      var _ref = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n        allowOverflow = _ref.allowOverflow;\n      if (!string) {\n        throw new Error('String is required');\n      }\n      var result = _match(string.split(''), this.matchTree, true);\n      if (result && result.match) {\n        delete result.matchedChars;\n      }\n      if (result && result.overflow) {\n        if (!allowOverflow) {\n          return;\n        }\n      }\n      return result;\n    }\n  }]);\n  return PatternMatcher;\n}();\n/**\r\n * Matches `characters` against a pattern compiled into a `tree`.\r\n * @param  {string[]} characters\r\n * @param  {Tree} tree — A pattern compiled into a `tree`. See the `*.d.ts` file for the description of the `tree` structure.\r\n * @param  {boolean} last — Whether it's the last (rightmost) subtree on its level of the match tree.\r\n * @return {object} See the `*.d.ts` file for the description of the result object.\r\n */\n\nexport { PatternMatcher as default };\nfunction _match(characters, tree, last) {\n  // If `tree` is a string, then `tree` is a single character.\n  // That's because when a pattern is parsed, multi-character-string parts\n  // of a pattern are compiled into arrays of single characters.\n  // I still wrote this piece of code for a \"general\" hypothetical case\n  // when `tree` could be a string of several characters, even though\n  // such case is not possible with the current implementation.\n  if (typeof tree === 'string') {\n    var characterString = characters.join('');\n    if (tree.indexOf(characterString) === 0) {\n      // `tree` is always a single character.\n      // If `tree.indexOf(characterString) === 0`\n      // then `characters.length === tree.length`.\n\n      /* istanbul ignore else */\n      if (characters.length === tree.length) {\n        return {\n          match: true,\n          matchedChars: characters\n        };\n      } // `tree` is always a single character.\n      // If `tree.indexOf(characterString) === 0`\n      // then `characters.length === tree.length`.\n\n      /* istanbul ignore next */\n\n      return {\n        partialMatch: true // matchedChars: characters\n      };\n    }\n    if (characterString.indexOf(tree) === 0) {\n      if (last) {\n        // The `else` path is not possible because `tree` is always a single character.\n        // The `else` case for `characters.length > tree.length` would be\n        // `characters.length <= tree.length` which means `characters.length <= 1`.\n        // `characters` array can't be empty, so that means `characters === [tree]`,\n        // which would also mean `tree.indexOf(characterString) === 0` and that'd mean\n        // that the `if (tree.indexOf(characterString) === 0)` condition before this\n        // `if` condition would be entered, and returned from there, not reaching this code.\n\n        /* istanbul ignore else */\n        if (characters.length > tree.length) {\n          return {\n            overflow: true\n          };\n        }\n      }\n      return {\n        match: true,\n        matchedChars: characters.slice(0, tree.length)\n      };\n    }\n    return;\n  }\n  if (Array.isArray(tree)) {\n    var restCharacters = characters.slice();\n    var i = 0;\n    while (i < tree.length) {\n      var subtree = tree[i];\n      var result = _match(restCharacters, subtree, last && i === tree.length - 1);\n      if (!result) {\n        return;\n      } else if (result.overflow) {\n        return result;\n      } else if (result.match) {\n        // Continue with the next subtree with the rest of the characters.\n        restCharacters = restCharacters.slice(result.matchedChars.length);\n        if (restCharacters.length === 0) {\n          if (i === tree.length - 1) {\n            return {\n              match: true,\n              matchedChars: characters\n            };\n          } else {\n            return {\n              partialMatch: true // matchedChars: characters\n            };\n          }\n        }\n      } else {\n        /* istanbul ignore else */\n        if (result.partialMatch) {\n          return {\n            partialMatch: true // matchedChars: characters\n          };\n        } else {\n          throw new Error(\"Unsupported match result:\\n\".concat(JSON.stringify(result, null, 2)));\n        }\n      }\n      i++;\n    } // If `last` then overflow has already been checked\n    // by the last element of the `tree` array.\n\n    /* istanbul ignore if */\n\n    if (last) {\n      return {\n        overflow: true\n      };\n    }\n    return {\n      match: true,\n      matchedChars: characters.slice(0, characters.length - restCharacters.length)\n    };\n  }\n  switch (tree.op) {\n    case '|':\n      var partialMatch;\n      for (var _iterator = _createForOfIteratorHelperLoose(tree.args), _step; !(_step = _iterator()).done;) {\n        var branch = _step.value;\n        var _result = _match(characters, branch, last);\n        if (_result) {\n          if (_result.overflow) {\n            return _result;\n          } else if (_result.match) {\n            return {\n              match: true,\n              matchedChars: _result.matchedChars\n            };\n          } else {\n            /* istanbul ignore else */\n            if (_result.partialMatch) {\n              partialMatch = true;\n            } else {\n              throw new Error(\"Unsupported match result:\\n\".concat(JSON.stringify(_result, null, 2)));\n            }\n          }\n        }\n      }\n      if (partialMatch) {\n        return {\n          partialMatch: true // matchedChars: ...\n        };\n      } // Not even a partial match.\n\n      return;\n    case '[]':\n      for (var _iterator2 = _createForOfIteratorHelperLoose(tree.args), _step2; !(_step2 = _iterator2()).done;) {\n        var _char = _step2.value;\n        if (characters[0] === _char) {\n          if (characters.length === 1) {\n            return {\n              match: true,\n              matchedChars: characters\n            };\n          }\n          if (last) {\n            return {\n              overflow: true\n            };\n          }\n          return {\n            match: true,\n            matchedChars: [_char]\n          };\n        }\n      } // No character matches.\n\n      return;\n\n    /* istanbul ignore next */\n\n    default:\n      throw new Error(\"Unsupported instruction tree: \".concat(tree));\n  }\n}", "map": {"version": 3, "names": ["_createForOfIteratorHelperLoose", "o", "allowArrayLike", "it", "Symbol", "iterator", "call", "next", "bind", "Array", "isArray", "_unsupportedIterableToArray", "length", "i", "done", "value", "TypeError", "minLen", "_arrayLikeToArray", "n", "Object", "prototype", "toString", "slice", "constructor", "name", "from", "test", "arr", "len", "arr2", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "_defineProperties", "target", "props", "descriptor", "enumerable", "configurable", "writable", "defineProperty", "key", "_createClass", "protoProps", "staticProps", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pattern", "matchTree", "parse", "match", "string", "_ref", "arguments", "undefined", "allowOverflow", "Error", "result", "_match", "split", "matched<PERSON><PERSON><PERSON>", "overflow", "default", "characters", "tree", "last", "characterString", "join", "indexOf", "partialMatch", "restCharacters", "subtree", "concat", "JSON", "stringify", "op", "_iterator", "args", "_step", "branch", "_result", "_iterator2", "_step2", "_char"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/AsYouTypeFormatter.PatternMatcher.js"], "sourcesContent": ["function _createForOfIteratorHelperLoose(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (it) return (it = it.call(o)).next.bind(it); if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; return function () { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\n\nimport PatternParser from './AsYouTypeFormatter.PatternParser.js';\n\nvar PatternMatcher = /*#__PURE__*/function () {\n  function PatternMatcher(pattern) {\n    _classCallCheck(this, PatternMatcher);\n\n    this.matchTree = new PatternParser().parse(pattern);\n  }\n\n  _createClass(PatternMatcher, [{\n    key: \"match\",\n    value: function match(string) {\n      var _ref = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n          allowOverflow = _ref.allowOverflow;\n\n      if (!string) {\n        throw new Error('String is required');\n      }\n\n      var result = _match(string.split(''), this.matchTree, true);\n\n      if (result && result.match) {\n        delete result.matchedChars;\n      }\n\n      if (result && result.overflow) {\n        if (!allowOverflow) {\n          return;\n        }\n      }\n\n      return result;\n    }\n  }]);\n\n  return PatternMatcher;\n}();\n/**\r\n * Matches `characters` against a pattern compiled into a `tree`.\r\n * @param  {string[]} characters\r\n * @param  {Tree} tree — A pattern compiled into a `tree`. See the `*.d.ts` file for the description of the `tree` structure.\r\n * @param  {boolean} last — Whether it's the last (rightmost) subtree on its level of the match tree.\r\n * @return {object} See the `*.d.ts` file for the description of the result object.\r\n */\n\n\nexport { PatternMatcher as default };\n\nfunction _match(characters, tree, last) {\n  // If `tree` is a string, then `tree` is a single character.\n  // That's because when a pattern is parsed, multi-character-string parts\n  // of a pattern are compiled into arrays of single characters.\n  // I still wrote this piece of code for a \"general\" hypothetical case\n  // when `tree` could be a string of several characters, even though\n  // such case is not possible with the current implementation.\n  if (typeof tree === 'string') {\n    var characterString = characters.join('');\n\n    if (tree.indexOf(characterString) === 0) {\n      // `tree` is always a single character.\n      // If `tree.indexOf(characterString) === 0`\n      // then `characters.length === tree.length`.\n\n      /* istanbul ignore else */\n      if (characters.length === tree.length) {\n        return {\n          match: true,\n          matchedChars: characters\n        };\n      } // `tree` is always a single character.\n      // If `tree.indexOf(characterString) === 0`\n      // then `characters.length === tree.length`.\n\n      /* istanbul ignore next */\n\n\n      return {\n        partialMatch: true // matchedChars: characters\n\n      };\n    }\n\n    if (characterString.indexOf(tree) === 0) {\n      if (last) {\n        // The `else` path is not possible because `tree` is always a single character.\n        // The `else` case for `characters.length > tree.length` would be\n        // `characters.length <= tree.length` which means `characters.length <= 1`.\n        // `characters` array can't be empty, so that means `characters === [tree]`,\n        // which would also mean `tree.indexOf(characterString) === 0` and that'd mean\n        // that the `if (tree.indexOf(characterString) === 0)` condition before this\n        // `if` condition would be entered, and returned from there, not reaching this code.\n\n        /* istanbul ignore else */\n        if (characters.length > tree.length) {\n          return {\n            overflow: true\n          };\n        }\n      }\n\n      return {\n        match: true,\n        matchedChars: characters.slice(0, tree.length)\n      };\n    }\n\n    return;\n  }\n\n  if (Array.isArray(tree)) {\n    var restCharacters = characters.slice();\n    var i = 0;\n\n    while (i < tree.length) {\n      var subtree = tree[i];\n\n      var result = _match(restCharacters, subtree, last && i === tree.length - 1);\n\n      if (!result) {\n        return;\n      } else if (result.overflow) {\n        return result;\n      } else if (result.match) {\n        // Continue with the next subtree with the rest of the characters.\n        restCharacters = restCharacters.slice(result.matchedChars.length);\n\n        if (restCharacters.length === 0) {\n          if (i === tree.length - 1) {\n            return {\n              match: true,\n              matchedChars: characters\n            };\n          } else {\n            return {\n              partialMatch: true // matchedChars: characters\n\n            };\n          }\n        }\n      } else {\n        /* istanbul ignore else */\n        if (result.partialMatch) {\n          return {\n            partialMatch: true // matchedChars: characters\n\n          };\n        } else {\n          throw new Error(\"Unsupported match result:\\n\".concat(JSON.stringify(result, null, 2)));\n        }\n      }\n\n      i++;\n    } // If `last` then overflow has already been checked\n    // by the last element of the `tree` array.\n\n    /* istanbul ignore if */\n\n\n    if (last) {\n      return {\n        overflow: true\n      };\n    }\n\n    return {\n      match: true,\n      matchedChars: characters.slice(0, characters.length - restCharacters.length)\n    };\n  }\n\n  switch (tree.op) {\n    case '|':\n      var partialMatch;\n\n      for (var _iterator = _createForOfIteratorHelperLoose(tree.args), _step; !(_step = _iterator()).done;) {\n        var branch = _step.value;\n\n        var _result = _match(characters, branch, last);\n\n        if (_result) {\n          if (_result.overflow) {\n            return _result;\n          } else if (_result.match) {\n            return {\n              match: true,\n              matchedChars: _result.matchedChars\n            };\n          } else {\n            /* istanbul ignore else */\n            if (_result.partialMatch) {\n              partialMatch = true;\n            } else {\n              throw new Error(\"Unsupported match result:\\n\".concat(JSON.stringify(_result, null, 2)));\n            }\n          }\n        }\n      }\n\n      if (partialMatch) {\n        return {\n          partialMatch: true // matchedChars: ...\n\n        };\n      } // Not even a partial match.\n\n\n      return;\n\n    case '[]':\n      for (var _iterator2 = _createForOfIteratorHelperLoose(tree.args), _step2; !(_step2 = _iterator2()).done;) {\n        var _char = _step2.value;\n\n        if (characters[0] === _char) {\n          if (characters.length === 1) {\n            return {\n              match: true,\n              matchedChars: characters\n            };\n          }\n\n          if (last) {\n            return {\n              overflow: true\n            };\n          }\n\n          return {\n            match: true,\n            matchedChars: [_char]\n          };\n        }\n      } // No character matches.\n\n\n      return;\n\n    /* istanbul ignore next */\n\n    default:\n      throw new Error(\"Unsupported instruction tree: \".concat(tree));\n  }\n}\n"], "mappings": "AAAA,SAASA,+BAA+BA,CAACC,CAAC,EAAEC,cAAc,EAAE;EAAE,IAAIC,EAAE,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAIH,CAAC,CAACG,MAAM,CAACC,QAAQ,CAAC,IAAIJ,CAAC,CAAC,YAAY,CAAC;EAAE,IAAIE,EAAE,EAAE,OAAO,CAACA,EAAE,GAAGA,EAAE,CAACG,IAAI,CAACL,CAAC,CAAC,EAAEM,IAAI,CAACC,IAAI,CAACL,EAAE,CAAC;EAAE,IAAIM,KAAK,CAACC,OAAO,CAACT,CAAC,CAAC,KAAKE,EAAE,GAAGQ,2BAA2B,CAACV,CAAC,CAAC,CAAC,IAAIC,cAAc,IAAID,CAAC,IAAI,OAAOA,CAAC,CAACW,MAAM,KAAK,QAAQ,EAAE;IAAE,IAAIT,EAAE,EAAEF,CAAC,GAAGE,EAAE;IAAE,IAAIU,CAAC,GAAG,CAAC;IAAE,OAAO,YAAY;MAAE,IAAIA,CAAC,IAAIZ,CAAC,CAACW,MAAM,EAAE,OAAO;QAAEE,IAAI,EAAE;MAAK,CAAC;MAAE,OAAO;QAAEA,IAAI,EAAE,KAAK;QAAEC,KAAK,EAAEd,CAAC,CAACY,CAAC,EAAE;MAAE,CAAC;IAAE,CAAC;EAAE;EAAE,MAAM,IAAIG,SAAS,CAAC,uIAAuI,CAAC;AAAE;AAE3lB,SAASL,2BAA2BA,CAACV,CAAC,EAAEgB,MAAM,EAAE;EAAE,IAAI,CAAChB,CAAC,EAAE;EAAQ,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOiB,iBAAiB,CAACjB,CAAC,EAAEgB,MAAM,CAAC;EAAE,IAAIE,CAAC,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAAChB,IAAI,CAACL,CAAC,CAAC,CAACsB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAE,IAAIJ,CAAC,KAAK,QAAQ,IAAIlB,CAAC,CAACuB,WAAW,EAAEL,CAAC,GAAGlB,CAAC,CAACuB,WAAW,CAACC,IAAI;EAAE,IAAIN,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOV,KAAK,CAACiB,IAAI,CAACzB,CAAC,CAAC;EAAE,IAAIkB,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACQ,IAAI,CAACR,CAAC,CAAC,EAAE,OAAOD,iBAAiB,CAACjB,CAAC,EAAEgB,MAAM,CAAC;AAAE;AAE/Z,SAASC,iBAAiBA,CAACU,GAAG,EAAEC,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGD,GAAG,CAAChB,MAAM,EAAEiB,GAAG,GAAGD,GAAG,CAAChB,MAAM;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEiB,IAAI,GAAG,IAAIrB,KAAK,CAACoB,GAAG,CAAC,EAAEhB,CAAC,GAAGgB,GAAG,EAAEhB,CAAC,EAAE,EAAE;IAAEiB,IAAI,CAACjB,CAAC,CAAC,GAAGe,GAAG,CAACf,CAAC,CAAC;EAAE;EAAE,OAAOiB,IAAI;AAAE;AAEtL,SAASC,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIjB,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AAExJ,SAASkB,iBAAiBA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAAE,KAAK,IAAIvB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuB,KAAK,CAACxB,MAAM,EAAEC,CAAC,EAAE,EAAE;IAAE,IAAIwB,UAAU,GAAGD,KAAK,CAACvB,CAAC,CAAC;IAAEwB,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;IAAED,UAAU,CAACE,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;IAAEpB,MAAM,CAACqB,cAAc,CAACN,MAAM,EAAEE,UAAU,CAACK,GAAG,EAAEL,UAAU,CAAC;EAAE;AAAE;AAE5T,SAASM,YAAYA,CAACV,WAAW,EAAEW,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEV,iBAAiB,CAACD,WAAW,CAACZ,SAAS,EAAEuB,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAEX,iBAAiB,CAACD,WAAW,EAAEY,WAAW,CAAC;EAAEzB,MAAM,CAACqB,cAAc,CAACR,WAAW,EAAE,WAAW,EAAE;IAAEO,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAOP,WAAW;AAAE;AAE5R,OAAOa,aAAa,MAAM,uCAAuC;AAEjE,IAAIC,cAAc,GAAG,aAAa,YAAY;EAC5C,SAASA,cAAcA,CAACC,OAAO,EAAE;IAC/BjB,eAAe,CAAC,IAAI,EAAEgB,cAAc,CAAC;IAErC,IAAI,CAACE,SAAS,GAAG,IAAIH,aAAa,CAAC,CAAC,CAACI,KAAK,CAACF,OAAO,CAAC;EACrD;EAEAL,YAAY,CAACI,cAAc,EAAE,CAAC;IAC5BL,GAAG,EAAE,OAAO;IACZ3B,KAAK,EAAE,SAASoC,KAAKA,CAACC,MAAM,EAAE;MAC5B,IAAIC,IAAI,GAAGC,SAAS,CAAC1C,MAAM,GAAG,CAAC,IAAI0C,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC7EE,aAAa,GAAGH,IAAI,CAACG,aAAa;MAEtC,IAAI,CAACJ,MAAM,EAAE;QACX,MAAM,IAAIK,KAAK,CAAC,oBAAoB,CAAC;MACvC;MAEA,IAAIC,MAAM,GAAGC,MAAM,CAACP,MAAM,CAACQ,KAAK,CAAC,EAAE,CAAC,EAAE,IAAI,CAACX,SAAS,EAAE,IAAI,CAAC;MAE3D,IAAIS,MAAM,IAAIA,MAAM,CAACP,KAAK,EAAE;QAC1B,OAAOO,MAAM,CAACG,YAAY;MAC5B;MAEA,IAAIH,MAAM,IAAIA,MAAM,CAACI,QAAQ,EAAE;QAC7B,IAAI,CAACN,aAAa,EAAE;UAClB;QACF;MACF;MAEA,OAAOE,MAAM;IACf;EACF,CAAC,CAAC,CAAC;EAEH,OAAOX,cAAc;AACvB,CAAC,CAAC,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,SAASA,cAAc,IAAIgB,OAAO;AAElC,SAASJ,MAAMA,CAACK,UAAU,EAAEC,IAAI,EAAEC,IAAI,EAAE;EACtC;EACA;EACA;EACA;EACA;EACA;EACA,IAAI,OAAOD,IAAI,KAAK,QAAQ,EAAE;IAC5B,IAAIE,eAAe,GAAGH,UAAU,CAACI,IAAI,CAAC,EAAE,CAAC;IAEzC,IAAIH,IAAI,CAACI,OAAO,CAACF,eAAe,CAAC,KAAK,CAAC,EAAE;MACvC;MACA;MACA;;MAEA;MACA,IAAIH,UAAU,CAACpD,MAAM,KAAKqD,IAAI,CAACrD,MAAM,EAAE;QACrC,OAAO;UACLuC,KAAK,EAAE,IAAI;UACXU,YAAY,EAAEG;QAChB,CAAC;MACH,CAAC,CAAC;MACF;MACA;;MAEA;;MAGA,OAAO;QACLM,YAAY,EAAE,IAAI,CAAC;MAErB,CAAC;IACH;IAEA,IAAIH,eAAe,CAACE,OAAO,CAACJ,IAAI,CAAC,KAAK,CAAC,EAAE;MACvC,IAAIC,IAAI,EAAE;QACR;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA,IAAIF,UAAU,CAACpD,MAAM,GAAGqD,IAAI,CAACrD,MAAM,EAAE;UACnC,OAAO;YACLkD,QAAQ,EAAE;UACZ,CAAC;QACH;MACF;MAEA,OAAO;QACLX,KAAK,EAAE,IAAI;QACXU,YAAY,EAAEG,UAAU,CAACzC,KAAK,CAAC,CAAC,EAAE0C,IAAI,CAACrD,MAAM;MAC/C,CAAC;IACH;IAEA;EACF;EAEA,IAAIH,KAAK,CAACC,OAAO,CAACuD,IAAI,CAAC,EAAE;IACvB,IAAIM,cAAc,GAAGP,UAAU,CAACzC,KAAK,CAAC,CAAC;IACvC,IAAIV,CAAC,GAAG,CAAC;IAET,OAAOA,CAAC,GAAGoD,IAAI,CAACrD,MAAM,EAAE;MACtB,IAAI4D,OAAO,GAAGP,IAAI,CAACpD,CAAC,CAAC;MAErB,IAAI6C,MAAM,GAAGC,MAAM,CAACY,cAAc,EAAEC,OAAO,EAAEN,IAAI,IAAIrD,CAAC,KAAKoD,IAAI,CAACrD,MAAM,GAAG,CAAC,CAAC;MAE3E,IAAI,CAAC8C,MAAM,EAAE;QACX;MACF,CAAC,MAAM,IAAIA,MAAM,CAACI,QAAQ,EAAE;QAC1B,OAAOJ,MAAM;MACf,CAAC,MAAM,IAAIA,MAAM,CAACP,KAAK,EAAE;QACvB;QACAoB,cAAc,GAAGA,cAAc,CAAChD,KAAK,CAACmC,MAAM,CAACG,YAAY,CAACjD,MAAM,CAAC;QAEjE,IAAI2D,cAAc,CAAC3D,MAAM,KAAK,CAAC,EAAE;UAC/B,IAAIC,CAAC,KAAKoD,IAAI,CAACrD,MAAM,GAAG,CAAC,EAAE;YACzB,OAAO;cACLuC,KAAK,EAAE,IAAI;cACXU,YAAY,EAAEG;YAChB,CAAC;UACH,CAAC,MAAM;YACL,OAAO;cACLM,YAAY,EAAE,IAAI,CAAC;YAErB,CAAC;UACH;QACF;MACF,CAAC,MAAM;QACL;QACA,IAAIZ,MAAM,CAACY,YAAY,EAAE;UACvB,OAAO;YACLA,YAAY,EAAE,IAAI,CAAC;UAErB,CAAC;QACH,CAAC,MAAM;UACL,MAAM,IAAIb,KAAK,CAAC,6BAA6B,CAACgB,MAAM,CAACC,IAAI,CAACC,SAAS,CAACjB,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QACxF;MACF;MAEA7C,CAAC,EAAE;IACL,CAAC,CAAC;IACF;;IAEA;;IAGA,IAAIqD,IAAI,EAAE;MACR,OAAO;QACLJ,QAAQ,EAAE;MACZ,CAAC;IACH;IAEA,OAAO;MACLX,KAAK,EAAE,IAAI;MACXU,YAAY,EAAEG,UAAU,CAACzC,KAAK,CAAC,CAAC,EAAEyC,UAAU,CAACpD,MAAM,GAAG2D,cAAc,CAAC3D,MAAM;IAC7E,CAAC;EACH;EAEA,QAAQqD,IAAI,CAACW,EAAE;IACb,KAAK,GAAG;MACN,IAAIN,YAAY;MAEhB,KAAK,IAAIO,SAAS,GAAG7E,+BAA+B,CAACiE,IAAI,CAACa,IAAI,CAAC,EAAEC,KAAK,EAAE,CAAC,CAACA,KAAK,GAAGF,SAAS,CAAC,CAAC,EAAE/D,IAAI,GAAG;QACpG,IAAIkE,MAAM,GAAGD,KAAK,CAAChE,KAAK;QAExB,IAAIkE,OAAO,GAAGtB,MAAM,CAACK,UAAU,EAAEgB,MAAM,EAAEd,IAAI,CAAC;QAE9C,IAAIe,OAAO,EAAE;UACX,IAAIA,OAAO,CAACnB,QAAQ,EAAE;YACpB,OAAOmB,OAAO;UAChB,CAAC,MAAM,IAAIA,OAAO,CAAC9B,KAAK,EAAE;YACxB,OAAO;cACLA,KAAK,EAAE,IAAI;cACXU,YAAY,EAAEoB,OAAO,CAACpB;YACxB,CAAC;UACH,CAAC,MAAM;YACL;YACA,IAAIoB,OAAO,CAACX,YAAY,EAAE;cACxBA,YAAY,GAAG,IAAI;YACrB,CAAC,MAAM;cACL,MAAM,IAAIb,KAAK,CAAC,6BAA6B,CAACgB,MAAM,CAACC,IAAI,CAACC,SAAS,CAACM,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;YACzF;UACF;QACF;MACF;MAEA,IAAIX,YAAY,EAAE;QAChB,OAAO;UACLA,YAAY,EAAE,IAAI,CAAC;QAErB,CAAC;MACH,CAAC,CAAC;;MAGF;IAEF,KAAK,IAAI;MACP,KAAK,IAAIY,UAAU,GAAGlF,+BAA+B,CAACiE,IAAI,CAACa,IAAI,CAAC,EAAEK,MAAM,EAAE,CAAC,CAACA,MAAM,GAAGD,UAAU,CAAC,CAAC,EAAEpE,IAAI,GAAG;QACxG,IAAIsE,KAAK,GAAGD,MAAM,CAACpE,KAAK;QAExB,IAAIiD,UAAU,CAAC,CAAC,CAAC,KAAKoB,KAAK,EAAE;UAC3B,IAAIpB,UAAU,CAACpD,MAAM,KAAK,CAAC,EAAE;YAC3B,OAAO;cACLuC,KAAK,EAAE,IAAI;cACXU,YAAY,EAAEG;YAChB,CAAC;UACH;UAEA,IAAIE,IAAI,EAAE;YACR,OAAO;cACLJ,QAAQ,EAAE;YACZ,CAAC;UACH;UAEA,OAAO;YACLX,KAAK,EAAE,IAAI;YACXU,YAAY,EAAE,CAACuB,KAAK;UACtB,CAAC;QACH;MACF,CAAC,CAAC;;MAGF;;IAEF;;IAEA;MACE,MAAM,IAAI3B,KAAK,CAAC,gCAAgC,CAACgB,MAAM,CAACR,IAAI,CAAC,CAAC;EAClE;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}