{"version": 3, "file": "semver-compare.test.js", "names": ["semverCompare", "describe", "it", "versions", "sort", "should", "deep", "equal"], "sources": ["../../source/tools/semver-compare.test.js"], "sourcesContent": ["import semverCompare from './semver-compare.js'\r\n\r\ndescribe('semver-compare', () => {\r\n\tit('should compare versions', () => {\r\n\t\tconst versions = [\r\n\t\t\t'1.2.3',\r\n\t\t\t'4.11.6',\r\n\t\t\t'4.2.0',\r\n\t\t\t'1.5.19',\r\n\t\t\t'1.5.6',\r\n\t\t\t'1.5.4',\r\n\t\t\t'1.5.5-alpha.beta',\r\n\t\t\t'1.5.5-alpha',\r\n\t\t\t'1.5.5',\r\n\t\t\t'1.5.5-rc.1',\r\n\t\t\t'1.5.5-beta.0',\r\n\t\t\t'4.1.3',\r\n\t\t\t'2.3.1',\r\n\t\t\t'10.5.5',\r\n\t\t\t'11.3.0'\r\n\t\t]\r\n\t\tversions.sort(semverCompare).should.deep.equal([\r\n\t\t\t'1.2.3',\r\n\t\t\t'1.5.4',\r\n\t\t\t'1.5.5-alpha',\r\n\t\t\t'1.5.5-alpha.beta',\r\n\t\t\t'1.5.5-beta.0',\r\n\t\t\t'1.5.5-rc.1',\r\n\t\t\t'1.5.5',\r\n\t\t\t'1.5.6',\r\n\t\t\t'1.5.19',\r\n\t\t\t'2.3.1',\r\n\t\t\t'4.1.3',\r\n\t\t\t'4.2.0',\r\n\t\t\t'4.11.6',\r\n\t\t\t'10.5.5',\r\n\t\t\t'11.3.0'\r\n\t\t])\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,aAAP,MAA0B,qBAA1B;AAEAC,QAAQ,CAAC,gBAAD,EAAmB,YAAM;EAChCC,EAAE,CAAC,yBAAD,EAA4B,YAAM;IACnC,IAAMC,QAAQ,GAAG,CAChB,OADgB,EAEhB,QAFgB,EAGhB,OAHgB,EAIhB,QAJgB,EAKhB,OALgB,EAMhB,OANgB,EAOhB,kBAPgB,EAQhB,aARgB,EAShB,OATgB,EAUhB,YAVgB,EAWhB,cAXgB,EAYhB,OAZgB,EAahB,OAbgB,EAchB,QAdgB,EAehB,QAfgB,CAAjB;IAiBAA,QAAQ,CAACC,IAAT,CAAcJ,aAAd,EAA6BK,MAA7B,CAAoCC,IAApC,CAAyCC,KAAzC,CAA+C,CAC9C,OAD8C,EAE9C,OAF8C,EAG9C,aAH8C,EAI9C,kBAJ8C,EAK9C,cAL8C,EAM9C,YAN8C,EAO9C,OAP8C,EAQ9C,OAR8C,EAS9C,QAT8C,EAU9C,OAV8C,EAW9C,OAX8C,EAY9C,OAZ8C,EAa9C,QAb8C,EAc9C,QAd8C,EAe9C,QAf8C,CAA/C;EAiBA,CAnCC,CAAF;AAoCA,CArCO,CAAR"}