{"ast": null, "code": "import AsYouType from './AsYouType.js';\n/**\r\n * Formats a (possibly incomplete) phone number.\r\n * The phone number can be either in E.164 format\r\n * or in a form of national number digits.\r\n * @param {string} value - A possibly incomplete phone number. Either in E.164 format or in a form of national number digits.\r\n * @param {string|object} [optionsOrDefaultCountry] - A two-letter (\"ISO 3166-1 alpha-2\") country code, or an object of shape `{ defaultCountry?: string, defaultCallingCode?: string }`.\r\n * @return {string} Formatted (possibly incomplete) phone number.\r\n */\n\nexport default function formatIncompletePhoneNumber(value, optionsOrDefaultCountry, metadata) {\n  if (!metadata) {\n    metadata = optionsOrDefaultCountry;\n    optionsOrDefaultCountry = undefined;\n  }\n  return new AsYouType(optionsOrDefaultCountry, metadata).input(value);\n}", "map": {"version": 3, "names": ["AsYouType", "formatIncompletePhoneNumber", "value", "optionsOrDefaultCountry", "metadata", "undefined", "input"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/formatIncompletePhoneNumber.js"], "sourcesContent": ["import AsYouType from './AsYouType.js';\n/**\r\n * Formats a (possibly incomplete) phone number.\r\n * The phone number can be either in E.164 format\r\n * or in a form of national number digits.\r\n * @param {string} value - A possibly incomplete phone number. Either in E.164 format or in a form of national number digits.\r\n * @param {string|object} [optionsOrDefaultCountry] - A two-letter (\"ISO 3166-1 alpha-2\") country code, or an object of shape `{ defaultCountry?: string, defaultCallingCode?: string }`.\r\n * @return {string} Formatted (possibly incomplete) phone number.\r\n */\n\nexport default function formatIncompletePhoneNumber(value, optionsOrDefaultCountry, metadata) {\n  if (!metadata) {\n    metadata = optionsOrDefaultCountry;\n    optionsOrDefaultCountry = undefined;\n  }\n\n  return new AsYouType(optionsOrDefaultCountry, metadata).input(value);\n}\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,gBAAgB;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,2BAA2BA,CAACC,KAAK,EAAEC,uBAAuB,EAAEC,QAAQ,EAAE;EAC5F,IAAI,CAACA,QAAQ,EAAE;IACbA,QAAQ,GAAGD,uBAAuB;IAClCA,uBAAuB,GAAGE,SAAS;EACrC;EAEA,OAAO,IAAIL,SAAS,CAACG,uBAAuB,EAAEC,QAAQ,CAAC,CAACE,KAAK,CAACJ,KAAK,CAAC;AACtE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}