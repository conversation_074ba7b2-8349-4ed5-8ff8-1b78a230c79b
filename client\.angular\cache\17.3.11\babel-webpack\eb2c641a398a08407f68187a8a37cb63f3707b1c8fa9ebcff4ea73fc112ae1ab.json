{"ast": null, "code": "// Importing from a \".js\" file is a workaround for Node.js \"ES Modules\"\n// importing system which is even uncapable of importing \"*.json\" files.\nimport metadata from '../metadata.min.json.js';\nimport { PhoneNumberSearch as _PhoneNumberSearch } from '../es6/legacy/findPhoneNumbersInitialImplementation.js';\nexport function PhoneNumberSearch(text, options) {\n  _PhoneNumberSearch.call(this, text, options, metadata);\n}\n\n// Deprecated.\nPhoneNumberSearch.prototype = Object.create(_PhoneNumberSearch.prototype, {});\nPhoneNumberSearch.prototype.constructor = PhoneNumberSearch;", "map": {"version": 3, "names": ["metadata", "PhoneNumberSearch", "_PhoneNumberSearch", "text", "options", "call", "prototype", "Object", "create", "constructor"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/index.es6.exports/PhoneNumberSearch.js"], "sourcesContent": ["// Importing from a \".js\" file is a workaround for Node.js \"ES Modules\"\r\n// importing system which is even uncapable of importing \"*.json\" files.\r\nimport metadata from '../metadata.min.json.js'\r\n\r\nimport { PhoneNumberSearch as _PhoneNumberSearch } from '../es6/legacy/findPhoneNumbersInitialImplementation.js'\r\n\r\nexport function PhoneNumberSearch(text, options) {\r\n\t_PhoneNumberSearch.call(this, text, options, metadata)\r\n}\r\n\r\n// Deprecated.\r\nPhoneNumberSearch.prototype = Object.create(_PhoneNumberSearch.prototype, {})\r\nPhoneNumberSearch.prototype.constructor = PhoneNumberSearch\r\n"], "mappings": "AAAA;AACA;AACA,OAAOA,QAAQ,MAAM,yBAAyB;AAE9C,SAASC,iBAAiB,IAAIC,kBAAkB,QAAQ,wDAAwD;AAEhH,OAAO,SAASD,iBAAiBA,CAACE,IAAI,EAAEC,OAAO,EAAE;EAChDF,kBAAkB,CAACG,IAAI,CAAC,IAAI,EAAEF,IAAI,EAAEC,OAAO,EAAEJ,QAAQ,CAAC;AACvD;;AAEA;AACAC,iBAAiB,CAACK,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACN,kBAAkB,CAACI,SAAS,EAAE,CAAC,CAAC,CAAC;AAC7EL,iBAAiB,CAACK,SAAS,CAACG,WAAW,GAAGR,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}