{"ast": null, "code": "function _createForOfIteratorHelperLoose(o, allowArrayLike) {\n  var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"];\n  if (it) return (it = it.call(o)).next.bind(it);\n  if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") {\n    if (it) o = it;\n    var i = 0;\n    return function () {\n      if (i >= o.length) return {\n        done: true\n      };\n      return {\n        done: false,\n        value: o[i++]\n      };\n    };\n  }\n  throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n  return arr2;\n}\n\n// These mappings map a character (key) to a specific digit that should\n// replace it for normalization purposes. Non-European digits that\n// may be used in phone numbers are mapped to a European equivalent.\n//\n// E.g. in Iraq they don't write `+442323234` but rather `+٤٤٢٣٢٣٢٣٤`.\n//\nexport var DIGITS = {\n  '0': '0',\n  '1': '1',\n  '2': '2',\n  '3': '3',\n  '4': '4',\n  '5': '5',\n  '6': '6',\n  '7': '7',\n  '8': '8',\n  '9': '9',\n  \"\\uFF10\": '0',\n  // Fullwidth digit 0\n  \"\\uFF11\": '1',\n  // Fullwidth digit 1\n  \"\\uFF12\": '2',\n  // Fullwidth digit 2\n  \"\\uFF13\": '3',\n  // Fullwidth digit 3\n  \"\\uFF14\": '4',\n  // Fullwidth digit 4\n  \"\\uFF15\": '5',\n  // Fullwidth digit 5\n  \"\\uFF16\": '6',\n  // Fullwidth digit 6\n  \"\\uFF17\": '7',\n  // Fullwidth digit 7\n  \"\\uFF18\": '8',\n  // Fullwidth digit 8\n  \"\\uFF19\": '9',\n  // Fullwidth digit 9\n  \"\\u0660\": '0',\n  // Arabic-indic digit 0\n  \"\\u0661\": '1',\n  // Arabic-indic digit 1\n  \"\\u0662\": '2',\n  // Arabic-indic digit 2\n  \"\\u0663\": '3',\n  // Arabic-indic digit 3\n  \"\\u0664\": '4',\n  // Arabic-indic digit 4\n  \"\\u0665\": '5',\n  // Arabic-indic digit 5\n  \"\\u0666\": '6',\n  // Arabic-indic digit 6\n  \"\\u0667\": '7',\n  // Arabic-indic digit 7\n  \"\\u0668\": '8',\n  // Arabic-indic digit 8\n  \"\\u0669\": '9',\n  // Arabic-indic digit 9\n  \"\\u06F0\": '0',\n  // Eastern-Arabic digit 0\n  \"\\u06F1\": '1',\n  // Eastern-Arabic digit 1\n  \"\\u06F2\": '2',\n  // Eastern-Arabic digit 2\n  \"\\u06F3\": '3',\n  // Eastern-Arabic digit 3\n  \"\\u06F4\": '4',\n  // Eastern-Arabic digit 4\n  \"\\u06F5\": '5',\n  // Eastern-Arabic digit 5\n  \"\\u06F6\": '6',\n  // Eastern-Arabic digit 6\n  \"\\u06F7\": '7',\n  // Eastern-Arabic digit 7\n  \"\\u06F8\": '8',\n  // Eastern-Arabic digit 8\n  \"\\u06F9\": '9' // Eastern-Arabic digit 9\n};\nexport function parseDigit(character) {\n  return DIGITS[character];\n}\n/**\r\n * Parses phone number digits from a string.\r\n * Drops all punctuation leaving only digits.\r\n * Also converts wide-ascii and arabic-indic numerals to conventional numerals.\r\n * E.g. in Iraq they don't write `+442323234` but rather `+٤٤٢٣٢٣٢٣٤`.\r\n * @param  {string} string\r\n * @return {string}\r\n * @example\r\n * ```js\r\n * parseDigits('8 (800) 555')\r\n * // Outputs '8800555'.\r\n * ```\r\n */\n\nexport default function parseDigits(string) {\n  var result = ''; // Using `.split('')` here instead of normal `for ... of`\n  // because the importing application doesn't neccessarily include an ES6 polyfill.\n  // The `.split('')` approach discards \"exotic\" UTF-8 characters\n  // (the ones consisting of four bytes) but digits\n  // (including non-European ones) don't fall into that range\n  // so such \"exotic\" characters would be discarded anyway.\n\n  for (var _iterator = _createForOfIteratorHelperLoose(string.split('')), _step; !(_step = _iterator()).done;) {\n    var character = _step.value;\n    var digit = parseDigit(character);\n    if (digit) {\n      result += digit;\n    }\n  }\n  return result;\n}", "map": {"version": 3, "names": ["_createForOfIteratorHelperLoose", "o", "allowArrayLike", "it", "Symbol", "iterator", "call", "next", "bind", "Array", "isArray", "_unsupportedIterableToArray", "length", "i", "done", "value", "TypeError", "minLen", "_arrayLikeToArray", "n", "Object", "prototype", "toString", "slice", "constructor", "name", "from", "test", "arr", "len", "arr2", "DIGITS", "parseDigit", "character", "parseDigits", "string", "result", "_iterator", "split", "_step", "digit"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/helpers/parseDigits.js"], "sourcesContent": ["function _createForOfIteratorHelperLoose(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (it) return (it = it.call(o)).next.bind(it); if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; return function () { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\n// These mappings map a character (key) to a specific digit that should\n// replace it for normalization purposes. Non-European digits that\n// may be used in phone numbers are mapped to a European equivalent.\n//\n// E.g. in Iraq they don't write `+442323234` but rather `+٤٤٢٣٢٣٢٣٤`.\n//\nexport var DIGITS = {\n  '0': '0',\n  '1': '1',\n  '2': '2',\n  '3': '3',\n  '4': '4',\n  '5': '5',\n  '6': '6',\n  '7': '7',\n  '8': '8',\n  '9': '9',\n  \"\\uFF10\": '0',\n  // Fullwidth digit 0\n  \"\\uFF11\": '1',\n  // Fullwidth digit 1\n  \"\\uFF12\": '2',\n  // Fullwidth digit 2\n  \"\\uFF13\": '3',\n  // Fullwidth digit 3\n  \"\\uFF14\": '4',\n  // Fullwidth digit 4\n  \"\\uFF15\": '5',\n  // Fullwidth digit 5\n  \"\\uFF16\": '6',\n  // Fullwidth digit 6\n  \"\\uFF17\": '7',\n  // Fullwidth digit 7\n  \"\\uFF18\": '8',\n  // Fullwidth digit 8\n  \"\\uFF19\": '9',\n  // Fullwidth digit 9\n  \"\\u0660\": '0',\n  // Arabic-indic digit 0\n  \"\\u0661\": '1',\n  // Arabic-indic digit 1\n  \"\\u0662\": '2',\n  // Arabic-indic digit 2\n  \"\\u0663\": '3',\n  // Arabic-indic digit 3\n  \"\\u0664\": '4',\n  // Arabic-indic digit 4\n  \"\\u0665\": '5',\n  // Arabic-indic digit 5\n  \"\\u0666\": '6',\n  // Arabic-indic digit 6\n  \"\\u0667\": '7',\n  // Arabic-indic digit 7\n  \"\\u0668\": '8',\n  // Arabic-indic digit 8\n  \"\\u0669\": '9',\n  // Arabic-indic digit 9\n  \"\\u06F0\": '0',\n  // Eastern-Arabic digit 0\n  \"\\u06F1\": '1',\n  // Eastern-Arabic digit 1\n  \"\\u06F2\": '2',\n  // Eastern-Arabic digit 2\n  \"\\u06F3\": '3',\n  // Eastern-Arabic digit 3\n  \"\\u06F4\": '4',\n  // Eastern-Arabic digit 4\n  \"\\u06F5\": '5',\n  // Eastern-Arabic digit 5\n  \"\\u06F6\": '6',\n  // Eastern-Arabic digit 6\n  \"\\u06F7\": '7',\n  // Eastern-Arabic digit 7\n  \"\\u06F8\": '8',\n  // Eastern-Arabic digit 8\n  \"\\u06F9\": '9' // Eastern-Arabic digit 9\n\n};\nexport function parseDigit(character) {\n  return DIGITS[character];\n}\n/**\r\n * Parses phone number digits from a string.\r\n * Drops all punctuation leaving only digits.\r\n * Also converts wide-ascii and arabic-indic numerals to conventional numerals.\r\n * E.g. in Iraq they don't write `+442323234` but rather `+٤٤٢٣٢٣٢٣٤`.\r\n * @param  {string} string\r\n * @return {string}\r\n * @example\r\n * ```js\r\n * parseDigits('8 (800) 555')\r\n * // Outputs '8800555'.\r\n * ```\r\n */\n\nexport default function parseDigits(string) {\n  var result = ''; // Using `.split('')` here instead of normal `for ... of`\n  // because the importing application doesn't neccessarily include an ES6 polyfill.\n  // The `.split('')` approach discards \"exotic\" UTF-8 characters\n  // (the ones consisting of four bytes) but digits\n  // (including non-European ones) don't fall into that range\n  // so such \"exotic\" characters would be discarded anyway.\n\n  for (var _iterator = _createForOfIteratorHelperLoose(string.split('')), _step; !(_step = _iterator()).done;) {\n    var character = _step.value;\n    var digit = parseDigit(character);\n\n    if (digit) {\n      result += digit;\n    }\n  }\n\n  return result;\n}\n"], "mappings": "AAAA,SAASA,+BAA+BA,CAACC,CAAC,EAAEC,cAAc,EAAE;EAAE,IAAIC,EAAE,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAIH,CAAC,CAACG,MAAM,CAACC,QAAQ,CAAC,IAAIJ,CAAC,CAAC,YAAY,CAAC;EAAE,IAAIE,EAAE,EAAE,OAAO,CAACA,EAAE,GAAGA,EAAE,CAACG,IAAI,CAACL,CAAC,CAAC,EAAEM,IAAI,CAACC,IAAI,CAACL,EAAE,CAAC;EAAE,IAAIM,KAAK,CAACC,OAAO,CAACT,CAAC,CAAC,KAAKE,EAAE,GAAGQ,2BAA2B,CAACV,CAAC,CAAC,CAAC,IAAIC,cAAc,IAAID,CAAC,IAAI,OAAOA,CAAC,CAACW,MAAM,KAAK,QAAQ,EAAE;IAAE,IAAIT,EAAE,EAAEF,CAAC,GAAGE,EAAE;IAAE,IAAIU,CAAC,GAAG,CAAC;IAAE,OAAO,YAAY;MAAE,IAAIA,CAAC,IAAIZ,CAAC,CAACW,MAAM,EAAE,OAAO;QAAEE,IAAI,EAAE;MAAK,CAAC;MAAE,OAAO;QAAEA,IAAI,EAAE,KAAK;QAAEC,KAAK,EAAEd,CAAC,CAACY,CAAC,EAAE;MAAE,CAAC;IAAE,CAAC;EAAE;EAAE,MAAM,IAAIG,SAAS,CAAC,uIAAuI,CAAC;AAAE;AAE3lB,SAASL,2BAA2BA,CAACV,CAAC,EAAEgB,MAAM,EAAE;EAAE,IAAI,CAAChB,CAAC,EAAE;EAAQ,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOiB,iBAAiB,CAACjB,CAAC,EAAEgB,MAAM,CAAC;EAAE,IAAIE,CAAC,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAAChB,IAAI,CAACL,CAAC,CAAC,CAACsB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAE,IAAIJ,CAAC,KAAK,QAAQ,IAAIlB,CAAC,CAACuB,WAAW,EAAEL,CAAC,GAAGlB,CAAC,CAACuB,WAAW,CAACC,IAAI;EAAE,IAAIN,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOV,KAAK,CAACiB,IAAI,CAACzB,CAAC,CAAC;EAAE,IAAIkB,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACQ,IAAI,CAACR,CAAC,CAAC,EAAE,OAAOD,iBAAiB,CAACjB,CAAC,EAAEgB,MAAM,CAAC;AAAE;AAE/Z,SAASC,iBAAiBA,CAACU,GAAG,EAAEC,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGD,GAAG,CAAChB,MAAM,EAAEiB,GAAG,GAAGD,GAAG,CAAChB,MAAM;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEiB,IAAI,GAAG,IAAIrB,KAAK,CAACoB,GAAG,CAAC,EAAEhB,CAAC,GAAGgB,GAAG,EAAEhB,CAAC,EAAE,EAAE;IAAEiB,IAAI,CAACjB,CAAC,CAAC,GAAGe,GAAG,CAACf,CAAC,CAAC;EAAE;EAAE,OAAOiB,IAAI;AAAE;;AAEtL;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,MAAM,GAAG;EAClB,GAAG,EAAE,GAAG;EACR,GAAG,EAAE,GAAG;EACR,GAAG,EAAE,GAAG;EACR,GAAG,EAAE,GAAG;EACR,GAAG,EAAE,GAAG;EACR,GAAG,EAAE,GAAG;EACR,GAAG,EAAE,GAAG;EACR,GAAG,EAAE,GAAG;EACR,GAAG,EAAE,GAAG;EACR,GAAG,EAAE,GAAG;EACR,QAAQ,EAAE,GAAG;EACb;EACA,QAAQ,EAAE,GAAG;EACb;EACA,QAAQ,EAAE,GAAG;EACb;EACA,QAAQ,EAAE,GAAG;EACb;EACA,QAAQ,EAAE,GAAG;EACb;EACA,QAAQ,EAAE,GAAG;EACb;EACA,QAAQ,EAAE,GAAG;EACb;EACA,QAAQ,EAAE,GAAG;EACb;EACA,QAAQ,EAAE,GAAG;EACb;EACA,QAAQ,EAAE,GAAG;EACb;EACA,QAAQ,EAAE,GAAG;EACb;EACA,QAAQ,EAAE,GAAG;EACb;EACA,QAAQ,EAAE,GAAG;EACb;EACA,QAAQ,EAAE,GAAG;EACb;EACA,QAAQ,EAAE,GAAG;EACb;EACA,QAAQ,EAAE,GAAG;EACb;EACA,QAAQ,EAAE,GAAG;EACb;EACA,QAAQ,EAAE,GAAG;EACb;EACA,QAAQ,EAAE,GAAG;EACb;EACA,QAAQ,EAAE,GAAG;EACb;EACA,QAAQ,EAAE,GAAG;EACb;EACA,QAAQ,EAAE,GAAG;EACb;EACA,QAAQ,EAAE,GAAG;EACb;EACA,QAAQ,EAAE,GAAG;EACb;EACA,QAAQ,EAAE,GAAG;EACb;EACA,QAAQ,EAAE,GAAG;EACb;EACA,QAAQ,EAAE,GAAG;EACb;EACA,QAAQ,EAAE,GAAG;EACb;EACA,QAAQ,EAAE,GAAG;EACb;EACA,QAAQ,EAAE,GAAG,CAAC;AAEhB,CAAC;AACD,OAAO,SAASC,UAAUA,CAACC,SAAS,EAAE;EACpC,OAAOF,MAAM,CAACE,SAAS,CAAC;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,WAAWA,CAACC,MAAM,EAAE;EAC1C,IAAIC,MAAM,GAAG,EAAE,CAAC,CAAC;EACjB;EACA;EACA;EACA;EACA;;EAEA,KAAK,IAAIC,SAAS,GAAGrC,+BAA+B,CAACmC,MAAM,CAACG,KAAK,CAAC,EAAE,CAAC,CAAC,EAAEC,KAAK,EAAE,CAAC,CAACA,KAAK,GAAGF,SAAS,CAAC,CAAC,EAAEvB,IAAI,GAAG;IAC3G,IAAImB,SAAS,GAAGM,KAAK,CAACxB,KAAK;IAC3B,IAAIyB,KAAK,GAAGR,UAAU,CAACC,SAAS,CAAC;IAEjC,IAAIO,KAAK,EAAE;MACTJ,MAAM,IAAII,KAAK;IACjB;EACF;EAEA,OAAOJ,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}