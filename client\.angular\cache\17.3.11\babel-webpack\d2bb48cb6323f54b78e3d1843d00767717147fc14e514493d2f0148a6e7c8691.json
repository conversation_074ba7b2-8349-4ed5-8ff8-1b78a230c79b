{"ast": null, "code": "// Copy-pasted from:\n// https://github.com/substack/semver-compare/blob/master/index.js\n//\n// Inlining this function because some users reported issues with\n// importing from `semver-compare` in a browser with ES6 \"native\" modules.\n//\n// Fixes `semver-compare` not being able to compare versions with alpha/beta/etc \"tags\".\n// https://github.com/catamphetamine/libphonenumber-js/issues/381\nexport default function (a, b) {\n  a = a.split('-');\n  b = b.split('-');\n  var pa = a[0].split('.');\n  var pb = b[0].split('.');\n  for (var i = 0; i < 3; i++) {\n    var na = Number(pa[i]);\n    var nb = Number(pb[i]);\n    if (na > nb) return 1;\n    if (nb > na) return -1;\n    if (!isNaN(na) && isNaN(nb)) return 1;\n    if (isNaN(na) && !isNaN(nb)) return -1;\n  }\n  if (a[1] && b[1]) {\n    return a[1] > b[1] ? 1 : a[1] < b[1] ? -1 : 0;\n  }\n  return !a[1] && b[1] ? 1 : a[1] && !b[1] ? -1 : 0;\n}", "map": {"version": 3, "names": ["a", "b", "split", "pa", "pb", "i", "na", "Number", "nb", "isNaN"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/tools/semver-compare.js"], "sourcesContent": ["// Copy-pasted from:\n// https://github.com/substack/semver-compare/blob/master/index.js\n//\n// Inlining this function because some users reported issues with\n// importing from `semver-compare` in a browser with ES6 \"native\" modules.\n//\n// Fixes `semver-compare` not being able to compare versions with alpha/beta/etc \"tags\".\n// https://github.com/catamphetamine/libphonenumber-js/issues/381\nexport default function (a, b) {\n  a = a.split('-');\n  b = b.split('-');\n  var pa = a[0].split('.');\n  var pb = b[0].split('.');\n\n  for (var i = 0; i < 3; i++) {\n    var na = Number(pa[i]);\n    var nb = Number(pb[i]);\n    if (na > nb) return 1;\n    if (nb > na) return -1;\n    if (!isNaN(na) && isNaN(nb)) return 1;\n    if (isNaN(na) && !isNaN(nb)) return -1;\n  }\n\n  if (a[1] && b[1]) {\n    return a[1] > b[1] ? 1 : a[1] < b[1] ? -1 : 0;\n  }\n\n  return !a[1] && b[1] ? 1 : a[1] && !b[1] ? -1 : 0;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,UAAUA,CAAC,EAAEC,CAAC,EAAE;EAC7BD,CAAC,GAAGA,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC;EAChBD,CAAC,GAAGA,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;EAChB,IAAIC,EAAE,GAAGH,CAAC,CAAC,CAAC,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC;EACxB,IAAIE,EAAE,GAAGH,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;EAExB,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC1B,IAAIC,EAAE,GAAGC,MAAM,CAACJ,EAAE,CAACE,CAAC,CAAC,CAAC;IACtB,IAAIG,EAAE,GAAGD,MAAM,CAACH,EAAE,CAACC,CAAC,CAAC,CAAC;IACtB,IAAIC,EAAE,GAAGE,EAAE,EAAE,OAAO,CAAC;IACrB,IAAIA,EAAE,GAAGF,EAAE,EAAE,OAAO,CAAC,CAAC;IACtB,IAAI,CAACG,KAAK,CAACH,EAAE,CAAC,IAAIG,KAAK,CAACD,EAAE,CAAC,EAAE,OAAO,CAAC;IACrC,IAAIC,KAAK,CAACH,EAAE,CAAC,IAAI,CAACG,KAAK,CAACD,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;EACxC;EAEA,IAAIR,CAAC,CAAC,CAAC,CAAC,IAAIC,CAAC,CAAC,CAAC,CAAC,EAAE;IAChB,OAAOD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;EAC/C;EAEA,OAAO,CAACD,CAAC,CAAC,CAAC,CAAC,IAAIC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,IAAI,CAACC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;AACnD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}