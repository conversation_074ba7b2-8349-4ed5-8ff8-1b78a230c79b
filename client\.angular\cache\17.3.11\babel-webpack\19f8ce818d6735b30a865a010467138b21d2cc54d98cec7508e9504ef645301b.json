{"ast": null, "code": "// Deprecated.\n\nimport withMetadataArgument from '../min/exports/withMetadataArgument.js';\nimport _isValidNumber from '../es6/legacy/isValidNumber.js';\nexport function isValidNumber() {\n  return withMetadataArgument(_isValidNumber, arguments);\n}", "map": {"version": 3, "names": ["withMetadataArgument", "_isValidNumber", "isValidNumber", "arguments"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/index.es6.exports/isValidNumber.js"], "sourcesContent": ["// Deprecated.\r\n\r\nimport withMetadataArgument from '../min/exports/withMetadataArgument.js'\r\n\r\nimport _isValidNumber from '../es6/legacy/isValidNumber.js'\r\n\r\nexport function isValidNumber() {\r\n\treturn withMetadataArgument(_isValidNumber, arguments)\r\n}\r\n"], "mappings": "AAAA;;AAEA,OAAOA,oBAAoB,MAAM,wCAAwC;AAEzE,OAAOC,cAAc,MAAM,gCAAgC;AAE3D,OAAO,SAASC,aAAaA,CAAA,EAAG;EAC/B,OAAOF,oBAAoB,CAACC,cAAc,EAAEE,SAAS,CAAC;AACvD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}