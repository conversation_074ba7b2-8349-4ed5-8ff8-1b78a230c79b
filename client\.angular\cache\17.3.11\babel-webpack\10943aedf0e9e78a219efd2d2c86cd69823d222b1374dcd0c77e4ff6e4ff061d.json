{"ast": null, "code": "import Metadata from '../metadata.js';\nimport matchesEntirely from './matchesEntirely.js';\nimport extractNationalNumber from './extractNationalNumber.js';\nimport checkNumberLength from './checkNumberLength.js';\nimport getCountryCallingCode from '../getCountryCallingCode.js';\n/**\r\n * Sometimes some people incorrectly input international phone numbers\r\n * without the leading `+`. This function corrects such input.\r\n * @param  {string} number — Phone number digits.\r\n * @param  {string?} country\r\n * @param  {string?} callingCode\r\n * @param  {object} metadata\r\n * @return {object} `{ countryCallingCode: string?, number: string }`.\r\n */\n\nexport default function extractCountryCallingCodeFromInternationalNumberWithoutPlusSign(number, country, callingCode, metadata) {\n  var countryCallingCode = country ? getCountryCallingCode(country, metadata) : callingCode;\n  if (number.indexOf(countryCallingCode) === 0) {\n    metadata = new Metadata(metadata);\n    metadata.selectNumberingPlan(country, callingCode);\n    var possibleShorterNumber = number.slice(countryCallingCode.length);\n    var _extractNationalNumbe = extractNationalNumber(possibleShorterNumber, metadata),\n      possibleShorterNationalNumber = _extractNationalNumbe.nationalNumber;\n    var _extractNationalNumbe2 = extractNationalNumber(number, metadata),\n      nationalNumber = _extractNationalNumbe2.nationalNumber; // If the number was not valid before but is valid now,\n    // or if it was too long before, we consider the number\n    // with the country calling code stripped to be a better result\n    // and keep that instead.\n    // For example, in Germany (+49), `49` is a valid area code,\n    // so if a number starts with `49`, it could be both a valid\n    // national German number or an international number without\n    // a leading `+`.\n\n    if (!matchesEntirely(nationalNumber, metadata.nationalNumberPattern()) && matchesEntirely(possibleShorterNationalNumber, metadata.nationalNumberPattern()) || checkNumberLength(nationalNumber, metadata) === 'TOO_LONG') {\n      return {\n        countryCallingCode: countryCallingCode,\n        number: possibleShorterNumber\n      };\n    }\n  }\n  return {\n    number: number\n  };\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "matchesEntirely", "extractNationalNumber", "checkNumberLength", "getCountryCallingCode", "extractCountryCallingCodeFromInternationalNumberWithoutPlusSign", "number", "country", "callingCode", "metadata", "countryCallingCode", "indexOf", "selectNumberingPlan", "possibleShorterNumber", "slice", "length", "_extractNationalNumbe", "possibleShorterNationalNumber", "nationalNumber", "_extractNationalNumbe2", "nationalNumberPattern"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/helpers/extractCountryCallingCodeFromInternationalNumberWithoutPlusSign.js"], "sourcesContent": ["import Metadata from '../metadata.js';\nimport matchesEntirely from './matchesEntirely.js';\nimport extractNationalNumber from './extractNationalNumber.js';\nimport checkNumberLength from './checkNumberLength.js';\nimport getCountryCallingCode from '../getCountryCallingCode.js';\n/**\r\n * Sometimes some people incorrectly input international phone numbers\r\n * without the leading `+`. This function corrects such input.\r\n * @param  {string} number — Phone number digits.\r\n * @param  {string?} country\r\n * @param  {string?} callingCode\r\n * @param  {object} metadata\r\n * @return {object} `{ countryCallingCode: string?, number: string }`.\r\n */\n\nexport default function extractCountryCallingCodeFromInternationalNumberWithoutPlusSign(number, country, callingCode, metadata) {\n  var countryCallingCode = country ? getCountryCallingCode(country, metadata) : callingCode;\n\n  if (number.indexOf(countryCallingCode) === 0) {\n    metadata = new Metadata(metadata);\n    metadata.selectNumberingPlan(country, callingCode);\n    var possibleShorterNumber = number.slice(countryCallingCode.length);\n\n    var _extractNationalNumbe = extractNationalNumber(possibleShorterNumber, metadata),\n        possibleShorterNationalNumber = _extractNationalNumbe.nationalNumber;\n\n    var _extractNationalNumbe2 = extractNationalNumber(number, metadata),\n        nationalNumber = _extractNationalNumbe2.nationalNumber; // If the number was not valid before but is valid now,\n    // or if it was too long before, we consider the number\n    // with the country calling code stripped to be a better result\n    // and keep that instead.\n    // For example, in Germany (+49), `49` is a valid area code,\n    // so if a number starts with `49`, it could be both a valid\n    // national German number or an international number without\n    // a leading `+`.\n\n\n    if (!matchesEntirely(nationalNumber, metadata.nationalNumberPattern()) && matchesEntirely(possibleShorterNationalNumber, metadata.nationalNumberPattern()) || checkNumberLength(nationalNumber, metadata) === 'TOO_LONG') {\n      return {\n        countryCallingCode: countryCallingCode,\n        number: possibleShorterNumber\n      };\n    }\n  }\n\n  return {\n    number: number\n  };\n}\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,eAAe,MAAM,sBAAsB;AAClD,OAAOC,qBAAqB,MAAM,4BAA4B;AAC9D,OAAOC,iBAAiB,MAAM,wBAAwB;AACtD,OAAOC,qBAAqB,MAAM,6BAA6B;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,+DAA+DA,CAACC,MAAM,EAAEC,OAAO,EAAEC,WAAW,EAAEC,QAAQ,EAAE;EAC9H,IAAIC,kBAAkB,GAAGH,OAAO,GAAGH,qBAAqB,CAACG,OAAO,EAAEE,QAAQ,CAAC,GAAGD,WAAW;EAEzF,IAAIF,MAAM,CAACK,OAAO,CAACD,kBAAkB,CAAC,KAAK,CAAC,EAAE;IAC5CD,QAAQ,GAAG,IAAIT,QAAQ,CAACS,QAAQ,CAAC;IACjCA,QAAQ,CAACG,mBAAmB,CAACL,OAAO,EAAEC,WAAW,CAAC;IAClD,IAAIK,qBAAqB,GAAGP,MAAM,CAACQ,KAAK,CAACJ,kBAAkB,CAACK,MAAM,CAAC;IAEnE,IAAIC,qBAAqB,GAAGd,qBAAqB,CAACW,qBAAqB,EAAEJ,QAAQ,CAAC;MAC9EQ,6BAA6B,GAAGD,qBAAqB,CAACE,cAAc;IAExE,IAAIC,sBAAsB,GAAGjB,qBAAqB,CAACI,MAAM,EAAEG,QAAQ,CAAC;MAChES,cAAc,GAAGC,sBAAsB,CAACD,cAAc,CAAC,CAAC;IAC5D;IACA;IACA;IACA;IACA;IACA;IACA;;IAGA,IAAI,CAACjB,eAAe,CAACiB,cAAc,EAAET,QAAQ,CAACW,qBAAqB,CAAC,CAAC,CAAC,IAAInB,eAAe,CAACgB,6BAA6B,EAAER,QAAQ,CAACW,qBAAqB,CAAC,CAAC,CAAC,IAAIjB,iBAAiB,CAACe,cAAc,EAAET,QAAQ,CAAC,KAAK,UAAU,EAAE;MACxN,OAAO;QACLC,kBAAkB,EAAEA,kBAAkB;QACtCJ,MAAM,EAAEO;MACV,CAAC;IACH;EACF;EAEA,OAAO;IACLP,MAAM,EAAEA;EACV,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}