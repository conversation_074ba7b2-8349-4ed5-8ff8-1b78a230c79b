{"ast": null, "code": "import withMetadataArgument from '../min/exports/withMetadataArgument.js';\nimport _format from '../es6/legacy/format.js';\nexport function format() {\n  return withMetadataArgument(_format, arguments);\n}", "map": {"version": 3, "names": ["withMetadataArgument", "_format", "format", "arguments"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/index.es6.exports/format.js"], "sourcesContent": ["import withMetadataArgument from '../min/exports/withMetadataArgument.js'\r\n\r\nimport _format from '../es6/legacy/format.js'\r\n\r\nexport function format() {\r\n\treturn withMetadataArgument(_format, arguments)\r\n}\r\n"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,wCAAwC;AAEzE,OAAOC,OAAO,MAAM,yBAAyB;AAE7C,OAAO,SAASC,MAAMA,CAAA,EAAG;EACxB,OAAOF,oBAAoB,CAACC,OAAO,EAAEE,SAAS,CAAC;AAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}