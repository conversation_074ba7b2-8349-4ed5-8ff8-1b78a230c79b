{"ast": null, "code": "// Matches strings that look like dates using \"/\" as a separator.\n// Examples: 3/10/2011, 31/10/96 or 08/31/95.\nvar SLASH_SEPARATED_DATES = /(?:(?:[0-3]?\\d\\/[01]?\\d)|(?:[01]?\\d\\/[0-3]?\\d))\\/(?:[12]\\d)?\\d{2}/; // Matches timestamps.\n// Examples: \"2012-01-02 08:00\".\n// Note that the reg-ex does not include the\n// trailing \":\\d\\d\" -- that is covered by TIME_STAMPS_SUFFIX.\n\nvar TIME_STAMPS = /[12]\\d{3}[-/]?[01]\\d[-/]?[0-3]\\d +[0-2]\\d$/;\nvar TIME_STAMPS_SUFFIX_LEADING = /^:[0-5]\\d/;\nexport default function isValidPreCandidate(candidate, offset, text) {\n  // Skip a match that is more likely to be a date.\n  if (SLASH_SEPARATED_DATES.test(candidate)) {\n    return false;\n  } // Skip potential time-stamps.\n\n  if (TIME_STAMPS.test(candidate)) {\n    var followingText = text.slice(offset + candidate.length);\n    if (TIME_STAMPS_SUFFIX_LEADING.test(followingText)) {\n      return false;\n    }\n  }\n  return true;\n}", "map": {"version": 3, "names": ["SLASH_SEPARATED_DATES", "TIME_STAMPS", "TIME_STAMPS_SUFFIX_LEADING", "isValidPreCandidate", "candidate", "offset", "text", "test", "followingText", "slice", "length"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/findNumbers/isValidPreCandidate.js"], "sourcesContent": ["// Matches strings that look like dates using \"/\" as a separator.\n// Examples: 3/10/2011, 31/10/96 or 08/31/95.\nvar SLASH_SEPARATED_DATES = /(?:(?:[0-3]?\\d\\/[01]?\\d)|(?:[01]?\\d\\/[0-3]?\\d))\\/(?:[12]\\d)?\\d{2}/; // Matches timestamps.\n// Examples: \"2012-01-02 08:00\".\n// Note that the reg-ex does not include the\n// trailing \":\\d\\d\" -- that is covered by TIME_STAMPS_SUFFIX.\n\nvar TIME_STAMPS = /[12]\\d{3}[-/]?[01]\\d[-/]?[0-3]\\d +[0-2]\\d$/;\nvar TIME_STAMPS_SUFFIX_LEADING = /^:[0-5]\\d/;\nexport default function isValidPreCandidate(candidate, offset, text) {\n  // Skip a match that is more likely to be a date.\n  if (SLASH_SEPARATED_DATES.test(candidate)) {\n    return false;\n  } // Skip potential time-stamps.\n\n\n  if (TIME_STAMPS.test(candidate)) {\n    var followingText = text.slice(offset + candidate.length);\n\n    if (TIME_STAMPS_SUFFIX_LEADING.test(followingText)) {\n      return false;\n    }\n  }\n\n  return true;\n}\n"], "mappings": "AAAA;AACA;AACA,IAAIA,qBAAqB,GAAG,mEAAmE,CAAC,CAAC;AACjG;AACA;AACA;;AAEA,IAAIC,WAAW,GAAG,4CAA4C;AAC9D,IAAIC,0BAA0B,GAAG,WAAW;AAC5C,eAAe,SAASC,mBAAmBA,CAACC,SAAS,EAAEC,MAAM,EAAEC,IAAI,EAAE;EACnE;EACA,IAAIN,qBAAqB,CAACO,IAAI,CAACH,SAAS,CAAC,EAAE;IACzC,OAAO,KAAK;EACd,CAAC,CAAC;;EAGF,IAAIH,WAAW,CAACM,IAAI,CAACH,SAAS,CAAC,EAAE;IAC/B,IAAII,aAAa,GAAGF,IAAI,CAACG,KAAK,CAACJ,MAAM,GAAGD,SAAS,CAACM,MAAM,CAAC;IAEzD,IAAIR,0BAA0B,CAACK,IAAI,CAACC,aAAa,CAAC,EAAE;MAClD,OAAO,KAAK;IACd;EACF;EAEA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}