{"ast": null, "code": "// `parsePhoneNumber()` named export has been renamed to `parsePhoneNumberWithError()`.\nexport { parsePhoneNumberWithError, parsePhoneNumberWithError as parsePhoneNumber } from './min/exports/parsePhoneNumberWithError.js';\n// `parsePhoneNumberFromString()` named export is now considered legacy:\n// it has been promoted to a default export due to being too verbose.\nexport { parsePhoneNumber as parsePhoneNumberFromString, parsePhoneNumber as default } from './min/exports/parsePhoneNumber.js';\nexport { isValidPhoneNumber } from './min/exports/isValidPhoneNumber.js';\nexport { isPossiblePhoneNumber } from './min/exports/isPossiblePhoneNumber.js';\nexport { validatePhoneNumberLength } from './min/exports/validatePhoneNumberLength.js';\n\n// Deprecated.\nexport { findNumbers } from './min/exports/findNumbers.js';\nexport { searchNumbers } from './min/exports/searchNumbers.js';\nexport { findPhoneNumbersInText } from './min/exports/findPhoneNumbersInText.js';\nexport { searchPhoneNumbersInText } from './min/exports/searchPhoneNumbersInText.js';\nexport { PhoneNumberMatcher } from './min/exports/PhoneNumberMatcher.js';\nexport { AsYouType } from './min/exports/AsYouType.js';\nexport { DIGIT_PLACEHOLDER } from './es6/AsYouTypeFormatter.js';\nexport { isSupportedCountry } from './min/exports/isSupportedCountry.js';\nexport { getCountries } from './min/exports/getCountries.js';\n// `getPhoneCode` name is deprecated, use `getCountryCallingCode` instead.\nexport { getCountryCallingCode, getCountryCallingCode as getPhoneCode } from './min/exports/getCountryCallingCode.js';\nexport { getExtPrefix } from './min/exports/getExtPrefix.js';\nexport { Metadata } from './min/exports/Metadata.js';\nexport { getExampleNumber } from './min/exports/getExampleNumber.js';\nexport { formatIncompletePhoneNumber } from './min/exports/formatIncompletePhoneNumber.js';\nexport { PhoneNumber } from './min/exports/PhoneNumber.js';\nexport { ParseError, parseIncompletePhoneNumber, parsePhoneNumberCharacter, parseDigits, parseRFC3966, formatRFC3966 } from './core/index.js';\n\n// Deprecated (old) exports.\nexport { parse as parseNumber, parse } from './index.es6.exports/parse.js';\nexport { format as formatNumber, format } from './index.es6.exports/format.js';\nexport { getNumberType } from './index.es6.exports/getNumberType.js';\nexport { isPossibleNumber } from './index.es6.exports/isPossibleNumber.js';\nexport { isValidNumber } from './index.es6.exports/isValidNumber.js';\nexport { isValidNumberForRegion } from './index.es6.exports/isValidNumberForRegion.js';\nexport { findPhoneNumbers } from './index.es6.exports/findPhoneNumbers.js';\nexport { searchPhoneNumbers } from './index.es6.exports/searchPhoneNumbers.js';\nexport { PhoneNumberSearch } from './index.es6.exports/PhoneNumberSearch.js';\n\n// Deprecated DIGITS export.\n// (it was used in `react-phone-number-input`)\nexport { DIGITS } from './es6/helpers/parseDigits.js';\n\n// Deprecated \"custom\" exports.\nexport { default as parseCustom } from './es6/legacy/parse.js';\nexport { default as formatCustom } from './es6/legacy/format.js';\nexport { default as isValidNumberCustom } from './es6/legacy/isValidNumber.js';\nexport { default as findPhoneNumbersCustom } from './es6/legacy/findPhoneNumbers.js';\nexport { searchPhoneNumbers as searchPhoneNumbersCustom } from './es6/legacy/findPhoneNumbers.js';\nexport { PhoneNumberSearch as PhoneNumberSearchCustom } from './es6/legacy/findPhoneNumbersInitialImplementation.js';\nexport { default as getNumberTypeCustom } from './es6/legacy/getNumberType.js';\nexport { default as getCountryCallingCodeCustom, default as getPhoneCodeCustom } from './es6/getCountryCallingCode.js';\nexport { default as AsYouTypeCustom } from './es6/AsYouType.js';", "map": {"version": 3, "names": ["parsePhoneNumberWithError", "parsePhoneNumber", "parsePhoneNumberFromString", "default", "isValidPhoneNumber", "isPossiblePhoneNumber", "validatePhoneNumberLength", "findNumbers", "searchNumbers", "findPhoneNumbersInText", "searchPhoneNumbersInText", "PhoneNumberMatcher", "AsYouType", "DIGIT_PLACEHOLDER", "isSupportedCountry", "getCountries", "getCountryCallingCode", "getPhoneCode", "getExtPrefix", "<PERSON><PERSON><PERSON>", "getExampleNumber", "formatIncompletePhoneNumber", "PhoneNumber", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseIncompletePhoneNumber", "parsePhoneNumberCharacter", "parseDigits", "parseRFC3966", "formatRFC3966", "parse", "parseNumber", "format", "formatNumber", "getNumberType", "isPossibleNumber", "isValidNumber", "isValidNumberForRegion", "findPhoneNumbers", "searchPhoneNumbers", "PhoneNumberSearch", "DIGITS", "parseCustom", "formatCustom", "isValidNumberCustom", "findPhoneNumbersCustom", "searchPhoneNumbersCustom", "PhoneNumberSearchCustom", "getNumberTypeCustom", "getCountryCallingCodeCustom", "getPhoneCodeCustom", "AsYouTypeCustom"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/index.js"], "sourcesContent": ["// `parsePhoneNumber()` named export has been renamed to `parsePhoneNumberWithError()`.\r\nexport { parsePhoneNumberWithError, parsePhoneNumberWithError as parsePhoneNumber } from './min/exports/parsePhoneNumberWithError.js'\r\n// `parsePhoneNumberFromString()` named export is now considered legacy:\r\n// it has been promoted to a default export due to being too verbose.\r\nexport { parsePhoneNumber as parsePhoneNumberFromString, parsePhoneNumber as default } from './min/exports/parsePhoneNumber.js'\r\n\r\nexport { isValidPhoneNumber } from './min/exports/isValidPhoneNumber.js'\r\nexport { isPossiblePhoneNumber } from './min/exports/isPossiblePhoneNumber.js'\r\nexport { validatePhoneNumberLength } from './min/exports/validatePhoneNumberLength.js'\r\n\r\n// Deprecated.\r\nexport { findNumbers } from './min/exports/findNumbers.js'\r\nexport { searchNumbers } from './min/exports/searchNumbers.js'\r\n\r\nexport { findPhoneNumbersInText } from './min/exports/findPhoneNumbersInText.js'\r\nexport { searchPhoneNumbersInText } from './min/exports/searchPhoneNumbersInText.js'\r\nexport { PhoneNumberMatcher } from './min/exports/PhoneNumberMatcher.js'\r\n\r\nexport { AsYouType } from './min/exports/AsYouType.js'\r\nexport { DIGIT_PLACEHOLDER } from './es6/AsYouTypeFormatter.js'\r\n\r\nexport { isSupportedCountry } from './min/exports/isSupportedCountry.js'\r\nexport { getCountries } from './min/exports/getCountries.js'\r\n// `getPhoneCode` name is deprecated, use `getCountryCallingCode` instead.\r\nexport { getCountryCallingCode, getCountryCallingCode as getPhoneCode } from './min/exports/getCountryCallingCode.js'\r\nexport { getExtPrefix } from './min/exports/getExtPrefix.js'\r\n\r\nexport { Metadata } from './min/exports/Metadata.js'\r\nexport { getExampleNumber } from './min/exports/getExampleNumber.js'\r\n\r\nexport { formatIncompletePhoneNumber } from './min/exports/formatIncompletePhoneNumber.js'\r\nexport { PhoneNumber } from './min/exports/PhoneNumber.js'\r\n\r\nexport {\r\n\tParseError,\r\n\tparseIncompletePhoneNumber,\r\n\tparsePhoneNumberCharacter,\r\n\tparseDigits,\r\n\tparseRFC3966,\r\n\tformatRFC3966\r\n} from './core/index.js'\r\n\r\n// Deprecated (old) exports.\r\nexport { parse as parseNumber, parse } from './index.es6.exports/parse.js'\r\nexport { format as formatNumber, format } from './index.es6.exports/format.js'\r\nexport { getNumberType } from './index.es6.exports/getNumberType.js'\r\nexport { isPossibleNumber } from './index.es6.exports/isPossibleNumber.js'\r\nexport { isValidNumber } from './index.es6.exports/isValidNumber.js'\r\nexport { isValidNumberForRegion } from './index.es6.exports/isValidNumberForRegion.js'\r\nexport { findPhoneNumbers } from './index.es6.exports/findPhoneNumbers.js'\r\nexport { searchPhoneNumbers } from './index.es6.exports/searchPhoneNumbers.js'\r\nexport { PhoneNumberSearch } from './index.es6.exports/PhoneNumberSearch.js'\r\n\r\n// Deprecated DIGITS export.\r\n// (it was used in `react-phone-number-input`)\r\nexport { DIGITS } from './es6/helpers/parseDigits.js'\r\n\r\n// Deprecated \"custom\" exports.\r\nexport { default as parseCustom } from './es6/legacy/parse.js'\r\nexport { default as formatCustom } from './es6/legacy/format.js'\r\nexport { default as isValidNumberCustom } from './es6/legacy/isValidNumber.js'\r\nexport { default as findPhoneNumbersCustom } from './es6/legacy/findPhoneNumbers.js'\r\nexport { searchPhoneNumbers as searchPhoneNumbersCustom } from './es6/legacy/findPhoneNumbers.js'\r\nexport { PhoneNumberSearch as PhoneNumberSearchCustom } from './es6/legacy/findPhoneNumbersInitialImplementation.js'\r\nexport { default as getNumberTypeCustom } from './es6/legacy/getNumberType.js'\r\nexport { default as getCountryCallingCodeCustom, default as getPhoneCodeCustom } from './es6/getCountryCallingCode.js'\r\nexport { default as AsYouTypeCustom } from './es6/AsYouType.js'\r\n"], "mappings": "AAAA;AACA,SAASA,yBAAyB,EAAEA,yBAAyB,IAAIC,gBAAgB,QAAQ,4CAA4C;AACrI;AACA;AACA,SAASA,gBAAgB,IAAIC,0BAA0B,EAAED,gBAAgB,IAAIE,OAAO,QAAQ,mCAAmC;AAE/H,SAASC,kBAAkB,QAAQ,qCAAqC;AACxE,SAASC,qBAAqB,QAAQ,wCAAwC;AAC9E,SAASC,yBAAyB,QAAQ,4CAA4C;;AAEtF;AACA,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,aAAa,QAAQ,gCAAgC;AAE9D,SAASC,sBAAsB,QAAQ,yCAAyC;AAChF,SAASC,wBAAwB,QAAQ,2CAA2C;AACpF,SAASC,kBAAkB,QAAQ,qCAAqC;AAExE,SAASC,SAAS,QAAQ,4BAA4B;AACtD,SAASC,iBAAiB,QAAQ,6BAA6B;AAE/D,SAASC,kBAAkB,QAAQ,qCAAqC;AACxE,SAASC,YAAY,QAAQ,+BAA+B;AAC5D;AACA,SAASC,qBAAqB,EAAEA,qBAAqB,IAAIC,YAAY,QAAQ,wCAAwC;AACrH,SAASC,YAAY,QAAQ,+BAA+B;AAE5D,SAASC,QAAQ,QAAQ,2BAA2B;AACpD,SAASC,gBAAgB,QAAQ,mCAAmC;AAEpE,SAASC,2BAA2B,QAAQ,8CAA8C;AAC1F,SAASC,WAAW,QAAQ,8BAA8B;AAE1D,SACCC,UAAU,EACVC,0BAA0B,EAC1BC,yBAAyB,EACzBC,WAAW,EACXC,YAAY,EACZC,aAAa,QACP,iBAAiB;;AAExB;AACA,SAASC,KAAK,IAAIC,WAAW,EAAED,KAAK,QAAQ,8BAA8B;AAC1E,SAASE,MAAM,IAAIC,YAAY,EAAED,MAAM,QAAQ,+BAA+B;AAC9E,SAASE,aAAa,QAAQ,sCAAsC;AACpE,SAASC,gBAAgB,QAAQ,yCAAyC;AAC1E,SAASC,aAAa,QAAQ,sCAAsC;AACpE,SAASC,sBAAsB,QAAQ,+CAA+C;AACtF,SAASC,gBAAgB,QAAQ,yCAAyC;AAC1E,SAASC,kBAAkB,QAAQ,2CAA2C;AAC9E,SAASC,iBAAiB,QAAQ,0CAA0C;;AAE5E;AACA;AACA,SAASC,MAAM,QAAQ,8BAA8B;;AAErD;AACA,SAASrC,OAAO,IAAIsC,WAAW,QAAQ,uBAAuB;AAC9D,SAAStC,OAAO,IAAIuC,YAAY,QAAQ,wBAAwB;AAChE,SAASvC,OAAO,IAAIwC,mBAAmB,QAAQ,+BAA+B;AAC9E,SAASxC,OAAO,IAAIyC,sBAAsB,QAAQ,kCAAkC;AACpF,SAASN,kBAAkB,IAAIO,wBAAwB,QAAQ,kCAAkC;AACjG,SAASN,iBAAiB,IAAIO,uBAAuB,QAAQ,uDAAuD;AACpH,SAAS3C,OAAO,IAAI4C,mBAAmB,QAAQ,+BAA+B;AAC9E,SAAS5C,OAAO,IAAI6C,2BAA2B,EAAE7C,OAAO,IAAI8C,kBAAkB,QAAQ,gCAAgC;AACtH,SAAS9C,OAAO,IAAI+C,eAAe,QAAQ,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}