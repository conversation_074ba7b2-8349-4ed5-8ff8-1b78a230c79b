{"ast": null, "code": "import parsePhoneNumber from '../parsePhoneNumber.js';\n/**\r\n * Matches a phone number object against a phone number string.\r\n * @param  {string} phoneNumberString\r\n * @param  {PhoneNumber} phoneNumber\r\n * @param  {object} metadata — Metadata JSON\r\n * @return {'INVALID_NUMBER'|'NO_MATCH'|'SHORT_NSN_MATCH'|'NSN_MATCH'|'EXACT_MATCH'}\r\n */\n\nexport default function matchPhoneNumberStringAgainstPhoneNumber(phoneNumberString, phoneNumber, metadata) {\n  // Parse `phoneNumberString`.\n  var phoneNumberStringContainsCallingCode = true;\n  var parsedPhoneNumber = parsePhoneNumber(phoneNumberString, metadata);\n  if (!parsedPhoneNumber) {\n    // If `phoneNumberString` didn't contain a country calling code\n    // then substitute it with the `phoneNumber`'s country calling code.\n    phoneNumberStringContainsCallingCode = false;\n    parsedPhoneNumber = parsePhoneNumber(phoneNumberString, {\n      defaultCallingCode: phoneNumber.countryCallingCode\n    }, metadata);\n  }\n  if (!parsedPhoneNumber) {\n    return 'INVALID_NUMBER';\n  } // Check that the extensions match.\n\n  if (phoneNumber.ext) {\n    if (parsedPhoneNumber.ext !== phoneNumber.ext) {\n      return 'NO_MATCH';\n    }\n  } else {\n    if (parsedPhoneNumber.ext) {\n      return 'NO_MATCH';\n    }\n  } // Check that country calling codes match.\n\n  if (phoneNumberStringContainsCallingCode) {\n    if (phoneNumber.countryCallingCode !== parsedPhoneNumber.countryCallingCode) {\n      return 'NO_MATCH';\n    }\n  } // Check if the whole numbers match.\n\n  if (phoneNumber.number === parsedPhoneNumber.number) {\n    if (phoneNumberStringContainsCallingCode) {\n      return 'EXACT_MATCH';\n    } else {\n      return 'NSN_MATCH';\n    }\n  } // Check if one national number is a \"suffix\" of the other.\n\n  if (phoneNumber.nationalNumber.indexOf(parsedPhoneNumber.nationalNumber) === 0 || parsedPhoneNumber.nationalNumber.indexOf(phoneNumber.nationalNumber) === 0) {\n    // \"A SHORT_NSN_MATCH occurs if there is a difference because of the\n    //  presence or absence of an 'Italian leading zero', the presence or\n    //  absence of an extension, or one NSN being a shorter variant of the\n    //  other.\"\n    return 'SHORT_NSN_MATCH';\n  }\n  return 'NO_MATCH';\n}", "map": {"version": 3, "names": ["parsePhoneNumber", "matchPhoneNumberStringAgainstPhoneNumber", "phoneNumberString", "phoneNumber", "metadata", "phoneNumberStringContainsCallingCode", "parsedPhoneNumber", "defaultCallingCode", "countryCallingCode", "ext", "number", "nationalNumber", "indexOf"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/findNumbers/matchPhoneNumberStringAgainstPhoneNumber.js"], "sourcesContent": ["import parsePhoneNumber from '../parsePhoneNumber.js';\n/**\r\n * Matches a phone number object against a phone number string.\r\n * @param  {string} phoneNumberString\r\n * @param  {PhoneNumber} phoneNumber\r\n * @param  {object} metadata — Metadata JSON\r\n * @return {'INVALID_NUMBER'|'NO_MATCH'|'SHORT_NSN_MATCH'|'NSN_MATCH'|'EXACT_MATCH'}\r\n */\n\nexport default function matchPhoneNumberStringAgainstPhoneNumber(phoneNumberString, phoneNumber, metadata) {\n  // Parse `phoneNumberString`.\n  var phoneNumberStringContainsCallingCode = true;\n  var parsedPhoneNumber = parsePhoneNumber(phoneNumberString, metadata);\n\n  if (!parsedPhoneNumber) {\n    // If `phoneNumberString` didn't contain a country calling code\n    // then substitute it with the `phoneNumber`'s country calling code.\n    phoneNumberStringContainsCallingCode = false;\n    parsedPhoneNumber = parsePhoneNumber(phoneNumberString, {\n      defaultCallingCode: phoneNumber.countryCallingCode\n    }, metadata);\n  }\n\n  if (!parsedPhoneNumber) {\n    return 'INVALID_NUMBER';\n  } // Check that the extensions match.\n\n\n  if (phoneNumber.ext) {\n    if (parsedPhoneNumber.ext !== phoneNumber.ext) {\n      return 'NO_MATCH';\n    }\n  } else {\n    if (parsedPhoneNumber.ext) {\n      return 'NO_MATCH';\n    }\n  } // Check that country calling codes match.\n\n\n  if (phoneNumberStringContainsCallingCode) {\n    if (phoneNumber.countryCallingCode !== parsedPhoneNumber.countryCallingCode) {\n      return 'NO_MATCH';\n    }\n  } // Check if the whole numbers match.\n\n\n  if (phoneNumber.number === parsedPhoneNumber.number) {\n    if (phoneNumberStringContainsCallingCode) {\n      return 'EXACT_MATCH';\n    } else {\n      return 'NSN_MATCH';\n    }\n  } // Check if one national number is a \"suffix\" of the other.\n\n\n  if (phoneNumber.nationalNumber.indexOf(parsedPhoneNumber.nationalNumber) === 0 || parsedPhoneNumber.nationalNumber.indexOf(phoneNumber.nationalNumber) === 0) {\n    // \"A SHORT_NSN_MATCH occurs if there is a difference because of the\n    //  presence or absence of an 'Italian leading zero', the presence or\n    //  absence of an extension, or one NSN being a shorter variant of the\n    //  other.\"\n    return 'SHORT_NSN_MATCH';\n  }\n\n  return 'NO_MATCH';\n}\n"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,wBAAwB;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,wCAAwCA,CAACC,iBAAiB,EAAEC,WAAW,EAAEC,QAAQ,EAAE;EACzG;EACA,IAAIC,oCAAoC,GAAG,IAAI;EAC/C,IAAIC,iBAAiB,GAAGN,gBAAgB,CAACE,iBAAiB,EAAEE,QAAQ,CAAC;EAErE,IAAI,CAACE,iBAAiB,EAAE;IACtB;IACA;IACAD,oCAAoC,GAAG,KAAK;IAC5CC,iBAAiB,GAAGN,gBAAgB,CAACE,iBAAiB,EAAE;MACtDK,kBAAkB,EAAEJ,WAAW,CAACK;IAClC,CAAC,EAAEJ,QAAQ,CAAC;EACd;EAEA,IAAI,CAACE,iBAAiB,EAAE;IACtB,OAAO,gBAAgB;EACzB,CAAC,CAAC;;EAGF,IAAIH,WAAW,CAACM,GAAG,EAAE;IACnB,IAAIH,iBAAiB,CAACG,GAAG,KAAKN,WAAW,CAACM,GAAG,EAAE;MAC7C,OAAO,UAAU;IACnB;EACF,CAAC,MAAM;IACL,IAAIH,iBAAiB,CAACG,GAAG,EAAE;MACzB,OAAO,UAAU;IACnB;EACF,CAAC,CAAC;;EAGF,IAAIJ,oCAAoC,EAAE;IACxC,IAAIF,WAAW,CAACK,kBAAkB,KAAKF,iBAAiB,CAACE,kBAAkB,EAAE;MAC3E,OAAO,UAAU;IACnB;EACF,CAAC,CAAC;;EAGF,IAAIL,WAAW,CAACO,MAAM,KAAKJ,iBAAiB,CAACI,MAAM,EAAE;IACnD,IAAIL,oCAAoC,EAAE;MACxC,OAAO,aAAa;IACtB,CAAC,MAAM;MACL,OAAO,WAAW;IACpB;EACF,CAAC,CAAC;;EAGF,IAAIF,WAAW,CAACQ,cAAc,CAACC,OAAO,CAACN,iBAAiB,CAACK,cAAc,CAAC,KAAK,CAAC,IAAIL,iBAAiB,CAACK,cAAc,CAACC,OAAO,CAACT,WAAW,CAACQ,cAAc,CAAC,KAAK,CAAC,EAAE;IAC5J;IACA;IACA;IACA;IACA,OAAO,iBAAiB;EAC1B;EAEA,OAAO,UAAU;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}