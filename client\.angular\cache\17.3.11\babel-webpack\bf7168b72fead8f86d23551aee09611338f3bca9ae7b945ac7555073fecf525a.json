{"ast": null, "code": "function _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\n\n// This \"state\" object simply holds the state of the \"AsYouType\" parser:\n//\n// * `country?: string`\n// * `callingCode?: string`\n// * `digits: string`\n// * `international: boolean`\n// * `missingPlus: boolean`\n// * `IDDPrefix?: string`\n// * `carrierCode?: string`\n// * `nationalPrefix?: string`\n// * `nationalSignificantNumber?: string`\n// * `nationalSignificantNumberMatchesInput: boolean`\n// * `complexPrefixBeforeNationalSignificantNumber?: string`\n//\n// `state.country` and `state.callingCode` aren't required to be in sync.\n// For example, `state.country` could be `\"AR\"` and `state.callingCode` could be `undefined`.\n// So `state.country` and `state.callingCode` are totally independent.\n//\nvar AsYouTypeState = /*#__PURE__*/function () {\n  function AsYouTypeState(_ref) {\n    var onCountryChange = _ref.onCountryChange,\n      onCallingCodeChange = _ref.onCallingCodeChange;\n    _classCallCheck(this, AsYouTypeState);\n    this.onCountryChange = onCountryChange;\n    this.onCallingCodeChange = onCallingCodeChange;\n  }\n  _createClass(AsYouTypeState, [{\n    key: \"reset\",\n    value: function reset(_ref2) {\n      var country = _ref2.country,\n        callingCode = _ref2.callingCode;\n      this.international = false;\n      this.missingPlus = false;\n      this.IDDPrefix = undefined;\n      this.callingCode = undefined;\n      this.digits = '';\n      this.resetNationalSignificantNumber();\n      this.initCountryAndCallingCode(country, callingCode);\n    }\n  }, {\n    key: \"resetNationalSignificantNumber\",\n    value: function resetNationalSignificantNumber() {\n      this.nationalSignificantNumber = this.getNationalDigits();\n      this.nationalSignificantNumberMatchesInput = true;\n      this.nationalPrefix = undefined;\n      this.carrierCode = undefined;\n      this.complexPrefixBeforeNationalSignificantNumber = undefined;\n    }\n  }, {\n    key: \"update\",\n    value: function update(properties) {\n      for (var _i = 0, _Object$keys = Object.keys(properties); _i < _Object$keys.length; _i++) {\n        var key = _Object$keys[_i];\n        this[key] = properties[key];\n      }\n    }\n  }, {\n    key: \"initCountryAndCallingCode\",\n    value: function initCountryAndCallingCode(country, callingCode) {\n      this.setCountry(country);\n      this.setCallingCode(callingCode);\n    }\n  }, {\n    key: \"setCountry\",\n    value: function setCountry(country) {\n      this.country = country;\n      this.onCountryChange(country);\n    }\n  }, {\n    key: \"setCallingCode\",\n    value: function setCallingCode(callingCode) {\n      this.callingCode = callingCode;\n      this.onCallingCodeChange(callingCode, this.country);\n    }\n  }, {\n    key: \"startInternationalNumber\",\n    value: function startInternationalNumber(country, callingCode) {\n      // Prepend the `+` to parsed input.\n      this.international = true; // If a default country was set then reset it\n      // because an explicitly international phone\n      // number is being entered.\n\n      this.initCountryAndCallingCode(country, callingCode);\n    }\n  }, {\n    key: \"appendDigits\",\n    value: function appendDigits(nextDigits) {\n      this.digits += nextDigits;\n    }\n  }, {\n    key: \"appendNationalSignificantNumberDigits\",\n    value: function appendNationalSignificantNumberDigits(nextDigits) {\n      this.nationalSignificantNumber += nextDigits;\n    }\n    /**\r\n     * Returns the part of `this.digits` that corresponds to the national number.\r\n     * Basically, all digits that have been input by the user, except for the\r\n     * international prefix and the country calling code part\r\n     * (if the number is an international one).\r\n     * @return {string}\r\n     */\n  }, {\n    key: \"getNationalDigits\",\n    value: function getNationalDigits() {\n      if (this.international) {\n        return this.digits.slice((this.IDDPrefix ? this.IDDPrefix.length : 0) + (this.callingCode ? this.callingCode.length : 0));\n      }\n      return this.digits;\n    }\n  }, {\n    key: \"getDigitsWithoutInternationalPrefix\",\n    value: function getDigitsWithoutInternationalPrefix() {\n      if (this.international) {\n        if (this.IDDPrefix) {\n          return this.digits.slice(this.IDDPrefix.length);\n        }\n      }\n      return this.digits;\n    }\n  }]);\n  return AsYouTypeState;\n}();\nexport { AsYouTypeState as default };", "map": {"version": 3, "names": ["_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "Object", "defineProperty", "key", "_createClass", "protoProps", "staticProps", "prototype", "AsYouTypeState", "_ref", "onCountryChange", "onCallingCodeChange", "value", "reset", "_ref2", "country", "callingCode", "international", "missingPlus", "IDDPrefix", "undefined", "digits", "resetNationalSignificantNumber", "initCountryAndCallingCode", "nationalSignificantNumber", "getNationalDigits", "nationalSignificantNumberMatchesInput", "nationalPrefix", "carrierCode", "complexPrefixBeforeNationalSignificantNumber", "update", "properties", "_i", "_Object$keys", "keys", "setCountry", "setCallingCode", "startInternationalNumber", "appendDigits", "nextDigits", "appendNationalSignificantNumberDigits", "slice", "getDigitsWithoutInternationalPrefix", "default"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/AsYouTypeState.js"], "sourcesContent": ["function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\n\n// This \"state\" object simply holds the state of the \"AsYouType\" parser:\n//\n// * `country?: string`\n// * `callingCode?: string`\n// * `digits: string`\n// * `international: boolean`\n// * `missingPlus: boolean`\n// * `IDDPrefix?: string`\n// * `carrierCode?: string`\n// * `nationalPrefix?: string`\n// * `nationalSignificantNumber?: string`\n// * `nationalSignificantNumberMatchesInput: boolean`\n// * `complexPrefixBeforeNationalSignificantNumber?: string`\n//\n// `state.country` and `state.callingCode` aren't required to be in sync.\n// For example, `state.country` could be `\"AR\"` and `state.callingCode` could be `undefined`.\n// So `state.country` and `state.callingCode` are totally independent.\n//\nvar AsYouTypeState = /*#__PURE__*/function () {\n  function AsYouTypeState(_ref) {\n    var onCountryChange = _ref.onCountryChange,\n        onCallingCodeChange = _ref.onCallingCodeChange;\n\n    _classCallCheck(this, AsYouTypeState);\n\n    this.onCountryChange = onCountryChange;\n    this.onCallingCodeChange = onCallingCodeChange;\n  }\n\n  _createClass(AsYouTypeState, [{\n    key: \"reset\",\n    value: function reset(_ref2) {\n      var country = _ref2.country,\n          callingCode = _ref2.callingCode;\n      this.international = false;\n      this.missingPlus = false;\n      this.IDDPrefix = undefined;\n      this.callingCode = undefined;\n      this.digits = '';\n      this.resetNationalSignificantNumber();\n      this.initCountryAndCallingCode(country, callingCode);\n    }\n  }, {\n    key: \"resetNationalSignificantNumber\",\n    value: function resetNationalSignificantNumber() {\n      this.nationalSignificantNumber = this.getNationalDigits();\n      this.nationalSignificantNumberMatchesInput = true;\n      this.nationalPrefix = undefined;\n      this.carrierCode = undefined;\n      this.complexPrefixBeforeNationalSignificantNumber = undefined;\n    }\n  }, {\n    key: \"update\",\n    value: function update(properties) {\n      for (var _i = 0, _Object$keys = Object.keys(properties); _i < _Object$keys.length; _i++) {\n        var key = _Object$keys[_i];\n        this[key] = properties[key];\n      }\n    }\n  }, {\n    key: \"initCountryAndCallingCode\",\n    value: function initCountryAndCallingCode(country, callingCode) {\n      this.setCountry(country);\n      this.setCallingCode(callingCode);\n    }\n  }, {\n    key: \"setCountry\",\n    value: function setCountry(country) {\n      this.country = country;\n      this.onCountryChange(country);\n    }\n  }, {\n    key: \"setCallingCode\",\n    value: function setCallingCode(callingCode) {\n      this.callingCode = callingCode;\n      this.onCallingCodeChange(callingCode, this.country);\n    }\n  }, {\n    key: \"startInternationalNumber\",\n    value: function startInternationalNumber(country, callingCode) {\n      // Prepend the `+` to parsed input.\n      this.international = true; // If a default country was set then reset it\n      // because an explicitly international phone\n      // number is being entered.\n\n      this.initCountryAndCallingCode(country, callingCode);\n    }\n  }, {\n    key: \"appendDigits\",\n    value: function appendDigits(nextDigits) {\n      this.digits += nextDigits;\n    }\n  }, {\n    key: \"appendNationalSignificantNumberDigits\",\n    value: function appendNationalSignificantNumberDigits(nextDigits) {\n      this.nationalSignificantNumber += nextDigits;\n    }\n    /**\r\n     * Returns the part of `this.digits` that corresponds to the national number.\r\n     * Basically, all digits that have been input by the user, except for the\r\n     * international prefix and the country calling code part\r\n     * (if the number is an international one).\r\n     * @return {string}\r\n     */\n\n  }, {\n    key: \"getNationalDigits\",\n    value: function getNationalDigits() {\n      if (this.international) {\n        return this.digits.slice((this.IDDPrefix ? this.IDDPrefix.length : 0) + (this.callingCode ? this.callingCode.length : 0));\n      }\n\n      return this.digits;\n    }\n  }, {\n    key: \"getDigitsWithoutInternationalPrefix\",\n    value: function getDigitsWithoutInternationalPrefix() {\n      if (this.international) {\n        if (this.IDDPrefix) {\n          return this.digits.slice(this.IDDPrefix.length);\n        }\n      }\n\n      return this.digits;\n    }\n  }]);\n\n  return AsYouTypeState;\n}();\n\nexport { AsYouTypeState as default };\n"], "mappings": "AAAA,SAASA,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AAExJ,SAASC,iBAAiBA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IAAE,IAAIE,UAAU,GAAGH,KAAK,CAACC,CAAC,CAAC;IAAEE,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;IAAED,UAAU,CAACE,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;IAAEC,MAAM,CAACC,cAAc,CAACT,MAAM,EAAEI,UAAU,CAACM,GAAG,EAAEN,UAAU,CAAC;EAAE;AAAE;AAE5T,SAASO,YAAYA,CAACd,WAAW,EAAEe,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEb,iBAAiB,CAACF,WAAW,CAACiB,SAAS,EAAEF,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAEd,iBAAiB,CAACF,WAAW,EAAEgB,WAAW,CAAC;EAAEL,MAAM,CAACC,cAAc,CAACZ,WAAW,EAAE,WAAW,EAAE;IAAEU,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAOV,WAAW;AAAE;;AAE5R;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIkB,cAAc,GAAG,aAAa,YAAY;EAC5C,SAASA,cAAcA,CAACC,IAAI,EAAE;IAC5B,IAAIC,eAAe,GAAGD,IAAI,CAACC,eAAe;MACtCC,mBAAmB,GAAGF,IAAI,CAACE,mBAAmB;IAElDvB,eAAe,CAAC,IAAI,EAAEoB,cAAc,CAAC;IAErC,IAAI,CAACE,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;EAChD;EAEAP,YAAY,CAACI,cAAc,EAAE,CAAC;IAC5BL,GAAG,EAAE,OAAO;IACZS,KAAK,EAAE,SAASC,KAAKA,CAACC,KAAK,EAAE;MAC3B,IAAIC,OAAO,GAAGD,KAAK,CAACC,OAAO;QACvBC,WAAW,GAAGF,KAAK,CAACE,WAAW;MACnC,IAAI,CAACC,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACC,WAAW,GAAG,KAAK;MACxB,IAAI,CAACC,SAAS,GAAGC,SAAS;MAC1B,IAAI,CAACJ,WAAW,GAAGI,SAAS;MAC5B,IAAI,CAACC,MAAM,GAAG,EAAE;MAChB,IAAI,CAACC,8BAA8B,CAAC,CAAC;MACrC,IAAI,CAACC,yBAAyB,CAACR,OAAO,EAAEC,WAAW,CAAC;IACtD;EACF,CAAC,EAAE;IACDb,GAAG,EAAE,gCAAgC;IACrCS,KAAK,EAAE,SAASU,8BAA8BA,CAAA,EAAG;MAC/C,IAAI,CAACE,yBAAyB,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;MACzD,IAAI,CAACC,qCAAqC,GAAG,IAAI;MACjD,IAAI,CAACC,cAAc,GAAGP,SAAS;MAC/B,IAAI,CAACQ,WAAW,GAAGR,SAAS;MAC5B,IAAI,CAACS,4CAA4C,GAAGT,SAAS;IAC/D;EACF,CAAC,EAAE;IACDjB,GAAG,EAAE,QAAQ;IACbS,KAAK,EAAE,SAASkB,MAAMA,CAACC,UAAU,EAAE;MACjC,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEC,YAAY,GAAGhC,MAAM,CAACiC,IAAI,CAACH,UAAU,CAAC,EAAEC,EAAE,GAAGC,YAAY,CAACrC,MAAM,EAAEoC,EAAE,EAAE,EAAE;QACvF,IAAI7B,GAAG,GAAG8B,YAAY,CAACD,EAAE,CAAC;QAC1B,IAAI,CAAC7B,GAAG,CAAC,GAAG4B,UAAU,CAAC5B,GAAG,CAAC;MAC7B;IACF;EACF,CAAC,EAAE;IACDA,GAAG,EAAE,2BAA2B;IAChCS,KAAK,EAAE,SAASW,yBAAyBA,CAACR,OAAO,EAAEC,WAAW,EAAE;MAC9D,IAAI,CAACmB,UAAU,CAACpB,OAAO,CAAC;MACxB,IAAI,CAACqB,cAAc,CAACpB,WAAW,CAAC;IAClC;EACF,CAAC,EAAE;IACDb,GAAG,EAAE,YAAY;IACjBS,KAAK,EAAE,SAASuB,UAAUA,CAACpB,OAAO,EAAE;MAClC,IAAI,CAACA,OAAO,GAAGA,OAAO;MACtB,IAAI,CAACL,eAAe,CAACK,OAAO,CAAC;IAC/B;EACF,CAAC,EAAE;IACDZ,GAAG,EAAE,gBAAgB;IACrBS,KAAK,EAAE,SAASwB,cAAcA,CAACpB,WAAW,EAAE;MAC1C,IAAI,CAACA,WAAW,GAAGA,WAAW;MAC9B,IAAI,CAACL,mBAAmB,CAACK,WAAW,EAAE,IAAI,CAACD,OAAO,CAAC;IACrD;EACF,CAAC,EAAE;IACDZ,GAAG,EAAE,0BAA0B;IAC/BS,KAAK,EAAE,SAASyB,wBAAwBA,CAACtB,OAAO,EAAEC,WAAW,EAAE;MAC7D;MACA,IAAI,CAACC,aAAa,GAAG,IAAI,CAAC,CAAC;MAC3B;MACA;;MAEA,IAAI,CAACM,yBAAyB,CAACR,OAAO,EAAEC,WAAW,CAAC;IACtD;EACF,CAAC,EAAE;IACDb,GAAG,EAAE,cAAc;IACnBS,KAAK,EAAE,SAAS0B,YAAYA,CAACC,UAAU,EAAE;MACvC,IAAI,CAAClB,MAAM,IAAIkB,UAAU;IAC3B;EACF,CAAC,EAAE;IACDpC,GAAG,EAAE,uCAAuC;IAC5CS,KAAK,EAAE,SAAS4B,qCAAqCA,CAACD,UAAU,EAAE;MAChE,IAAI,CAACf,yBAAyB,IAAIe,UAAU;IAC9C;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;EAEE,CAAC,EAAE;IACDpC,GAAG,EAAE,mBAAmB;IACxBS,KAAK,EAAE,SAASa,iBAAiBA,CAAA,EAAG;MAClC,IAAI,IAAI,CAACR,aAAa,EAAE;QACtB,OAAO,IAAI,CAACI,MAAM,CAACoB,KAAK,CAAC,CAAC,IAAI,CAACtB,SAAS,GAAG,IAAI,CAACA,SAAS,CAACvB,MAAM,GAAG,CAAC,KAAK,IAAI,CAACoB,WAAW,GAAG,IAAI,CAACA,WAAW,CAACpB,MAAM,GAAG,CAAC,CAAC,CAAC;MAC3H;MAEA,OAAO,IAAI,CAACyB,MAAM;IACpB;EACF,CAAC,EAAE;IACDlB,GAAG,EAAE,qCAAqC;IAC1CS,KAAK,EAAE,SAAS8B,mCAAmCA,CAAA,EAAG;MACpD,IAAI,IAAI,CAACzB,aAAa,EAAE;QACtB,IAAI,IAAI,CAACE,SAAS,EAAE;UAClB,OAAO,IAAI,CAACE,MAAM,CAACoB,KAAK,CAAC,IAAI,CAACtB,SAAS,CAACvB,MAAM,CAAC;QACjD;MACF;MAEA,OAAO,IAAI,CAACyB,MAAM;IACpB;EACF,CAAC,CAAC,CAAC;EAEH,OAAOb,cAAc;AACvB,CAAC,CAAC,CAAC;AAEH,SAASA,cAAc,IAAImC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}