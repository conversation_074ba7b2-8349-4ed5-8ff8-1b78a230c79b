{"ast": null, "code": "import isViablePhoneNumber from '../helpers/isViablePhoneNumber.js';\nimport parseNumber from '../parse.js';\nimport _isValidNumberForRegion from './isValidNumberForRegion_.js'; // This function has been deprecated and is not exported as\n// `isValidPhoneNumberForCountry()` or `isValidPhoneNumberForRegion()`.\n//\n// The rationale is:\n//\n// * We don't use the \"region\" word, so \"country\" would be better.\n//\n// * It could be substituted with:\n//\n// ```js\n// export default function isValidPhoneNumberForCountry(phoneNumberString, country) {\n// \tconst phoneNumber = parsePhoneNumber(phoneNumberString, {\n// \t\tdefaultCountry: country,\n// \t\t// Demand that the entire input string must be a phone number.\n// \t\t// Otherwise, it would \"extract\" a phone number from an input string.\n// \t\textract: false\n// \t})\n// \tif (!phoneNumber) {\n// \t\treturn false\n// \t}\n// \tif (phoneNumber.country !== country) {\n// \t\treturn false\n// \t}\n// \treturn phoneNumber.isValid()\n// }\n// ```\n//\n// * Same function could be used for `isPossiblePhoneNumberForCountry()`\n//   by replacing `isValid()` with `isPossible()`.\n//\n// * The reason why this function is not exported is because its result is ambiguous.\n//   Suppose `false` is returned. It could mean any of:\n//   * Not a phone number.\n//   * The phone number is valid but belongs to another country or another calling code.\n//   * The phone number belongs to the correct country but is not valid digit-wise.\n//   All those three cases should be handled separately from a \"User Experience\" standpoint.\n//   Simply showing \"Invalid phone number\" error in all of those cases would be lazy UX.\n\nexport default function isValidNumberForRegion(number, country, metadata) {\n  if (typeof number !== 'string') {\n    throw new TypeError('number must be a string');\n  }\n  if (typeof country !== 'string') {\n    throw new TypeError('country must be a string');\n  } // `parse` extracts phone numbers from raw text,\n  // therefore it will cut off all \"garbage\" characters,\n  // while this `validate` function needs to verify\n  // that the phone number contains no \"garbage\"\n  // therefore the explicit `isViablePhoneNumber` check.\n\n  var input;\n  if (isViablePhoneNumber(number)) {\n    input = parseNumber(number, {\n      defaultCountry: country\n    }, metadata);\n  } else {\n    input = {};\n  }\n  return _isValidNumberForRegion(input, country, undefined, metadata);\n}", "map": {"version": 3, "names": ["isViablePhoneNumber", "parseNumber", "_isValidNumberForRegion", "isValidNumberForRegion", "number", "country", "metadata", "TypeError", "input", "defaultCountry", "undefined"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/legacy/isValidNumberForRegion.js"], "sourcesContent": ["import isViablePhoneNumber from '../helpers/isViablePhoneNumber.js';\nimport parseNumber from '../parse.js';\nimport _isValidNumberForRegion from './isValidNumberForRegion_.js'; // This function has been deprecated and is not exported as\n// `isValidPhoneNumberForCountry()` or `isValidPhoneNumberForRegion()`.\n//\n// The rationale is:\n//\n// * We don't use the \"region\" word, so \"country\" would be better.\n//\n// * It could be substituted with:\n//\n// ```js\n// export default function isValidPhoneNumberForCountry(phoneNumberString, country) {\n// \tconst phoneNumber = parsePhoneNumber(phoneNumberString, {\n// \t\tdefaultCountry: country,\n// \t\t// Demand that the entire input string must be a phone number.\n// \t\t// Otherwise, it would \"extract\" a phone number from an input string.\n// \t\textract: false\n// \t})\n// \tif (!phoneNumber) {\n// \t\treturn false\n// \t}\n// \tif (phoneNumber.country !== country) {\n// \t\treturn false\n// \t}\n// \treturn phoneNumber.isValid()\n// }\n// ```\n//\n// * Same function could be used for `isPossiblePhoneNumberForCountry()`\n//   by replacing `isValid()` with `isPossible()`.\n//\n// * The reason why this function is not exported is because its result is ambiguous.\n//   Suppose `false` is returned. It could mean any of:\n//   * Not a phone number.\n//   * The phone number is valid but belongs to another country or another calling code.\n//   * The phone number belongs to the correct country but is not valid digit-wise.\n//   All those three cases should be handled separately from a \"User Experience\" standpoint.\n//   Simply showing \"Invalid phone number\" error in all of those cases would be lazy UX.\n\nexport default function isValidNumberForRegion(number, country, metadata) {\n  if (typeof number !== 'string') {\n    throw new TypeError('number must be a string');\n  }\n\n  if (typeof country !== 'string') {\n    throw new TypeError('country must be a string');\n  } // `parse` extracts phone numbers from raw text,\n  // therefore it will cut off all \"garbage\" characters,\n  // while this `validate` function needs to verify\n  // that the phone number contains no \"garbage\"\n  // therefore the explicit `isViablePhoneNumber` check.\n\n\n  var input;\n\n  if (isViablePhoneNumber(number)) {\n    input = parseNumber(number, {\n      defaultCountry: country\n    }, metadata);\n  } else {\n    input = {};\n  }\n\n  return _isValidNumberForRegion(input, country, undefined, metadata);\n}\n"], "mappings": "AAAA,OAAOA,mBAAmB,MAAM,mCAAmC;AACnE,OAAOC,WAAW,MAAM,aAAa;AACrC,OAAOC,uBAAuB,MAAM,8BAA8B,CAAC,CAAC;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,sBAAsBA,CAACC,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAE;EACxE,IAAI,OAAOF,MAAM,KAAK,QAAQ,EAAE;IAC9B,MAAM,IAAIG,SAAS,CAAC,yBAAyB,CAAC;EAChD;EAEA,IAAI,OAAOF,OAAO,KAAK,QAAQ,EAAE;IAC/B,MAAM,IAAIE,SAAS,CAAC,0BAA0B,CAAC;EACjD,CAAC,CAAC;EACF;EACA;EACA;EACA;;EAGA,IAAIC,KAAK;EAET,IAAIR,mBAAmB,CAACI,MAAM,CAAC,EAAE;IAC/BI,KAAK,GAAGP,WAAW,CAACG,MAAM,EAAE;MAC1BK,cAAc,EAAEJ;IAClB,CAAC,EAAEC,QAAQ,CAAC;EACd,CAAC,MAAM;IACLE,KAAK,GAAG,CAAC,CAAC;EACZ;EAEA,OAAON,uBAAuB,CAACM,KAAK,EAAEH,OAAO,EAAEK,SAAS,EAAEJ,QAAQ,CAAC;AACrE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}