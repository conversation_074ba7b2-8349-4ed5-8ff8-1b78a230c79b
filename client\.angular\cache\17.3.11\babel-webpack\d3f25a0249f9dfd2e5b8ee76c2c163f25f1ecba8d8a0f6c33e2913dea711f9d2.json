{"ast": null, "code": "// Deprecated. Import from 'metadata.js' directly instead.\nexport { getCountryCallingCode as default } from './metadata.js';", "map": {"version": 3, "names": ["getCountryCallingCode", "default"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/getCountryCallingCode.js"], "sourcesContent": ["// Deprecated. Import from 'metadata.js' directly instead.\nexport { getCountryCallingCode as default } from './metadata.js';\n"], "mappings": "AAAA;AACA,SAASA,qBAAqB,IAAIC,OAAO,QAAQ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}