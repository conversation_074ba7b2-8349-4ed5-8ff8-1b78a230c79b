{"ast": null, "code": "import Metadata from './metadata.js';\nexport default function getCountries(metadata) {\n  return new Metadata(metadata).getCountries();\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "getCountries", "metadata"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/getCountries.js"], "sourcesContent": ["import Metadata from './metadata.js';\nexport default function getCountries(metadata) {\n  return new Metadata(metadata).getCountries();\n}\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;AACpC,eAAe,SAASC,YAAYA,CAACC,QAAQ,EAAE;EAC7C,OAAO,IAAIF,QAAQ,CAACE,QAAQ,CAAC,CAACD,YAAY,CAAC,CAAC;AAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}