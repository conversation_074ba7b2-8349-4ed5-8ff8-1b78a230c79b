{"ast": null, "code": "function _createForOfIteratorHelperLoose(o, allowArrayLike) {\n  var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"];\n  if (it) return (it = it.call(o)).next.bind(it);\n  if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") {\n    if (it) o = it;\n    var i = 0;\n    return function () {\n      if (i >= o.length) return {\n        done: true\n      };\n      return {\n        done: false,\n        value: o[i++]\n      };\n    };\n  }\n  throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n  return arr2;\n}\n\n// Should be the same as `DIGIT_PLACEHOLDER` in `libphonenumber-metadata-generator`.\nexport var DIGIT_PLACEHOLDER = 'x'; // '\\u2008' (punctuation space)\n\nvar DIGIT_PLACEHOLDER_MATCHER = new RegExp(DIGIT_PLACEHOLDER); // Counts all occurences of a symbol in a string.\n// Unicode-unsafe (because using `.split()`).\n\nexport function countOccurences(symbol, string) {\n  var count = 0; // Using `.split('')` to iterate through a string here\n  // to avoid requiring `Symbol.iterator` polyfill.\n  // `.split('')` is generally not safe for Unicode,\n  // but in this particular case for counting brackets it is safe.\n  // for (const character of string)\n\n  for (var _iterator = _createForOfIteratorHelperLoose(string.split('')), _step; !(_step = _iterator()).done;) {\n    var character = _step.value;\n    if (character === symbol) {\n      count++;\n    }\n  }\n  return count;\n} // Repeats a string (or a symbol) N times.\n// http://stackoverflow.com/questions/202605/repeat-string-javascript\n\nexport function repeat(string, times) {\n  if (times < 1) {\n    return '';\n  }\n  var result = '';\n  while (times > 1) {\n    if (times & 1) {\n      result += string;\n    }\n    times >>= 1;\n    string += string;\n  }\n  return result + string;\n}\nexport function cutAndStripNonPairedParens(string, cutBeforeIndex) {\n  if (string[cutBeforeIndex] === ')') {\n    cutBeforeIndex++;\n  }\n  return stripNonPairedParens(string.slice(0, cutBeforeIndex));\n}\nexport function closeNonPairedParens(template, cut_before) {\n  var retained_template = template.slice(0, cut_before);\n  var opening_braces = countOccurences('(', retained_template);\n  var closing_braces = countOccurences(')', retained_template);\n  var dangling_braces = opening_braces - closing_braces;\n  while (dangling_braces > 0 && cut_before < template.length) {\n    if (template[cut_before] === ')') {\n      dangling_braces--;\n    }\n    cut_before++;\n  }\n  return template.slice(0, cut_before);\n}\nexport function stripNonPairedParens(string) {\n  var dangling_braces = [];\n  var i = 0;\n  while (i < string.length) {\n    if (string[i] === '(') {\n      dangling_braces.push(i);\n    } else if (string[i] === ')') {\n      dangling_braces.pop();\n    }\n    i++;\n  }\n  var start = 0;\n  var cleared_string = '';\n  dangling_braces.push(string.length);\n  for (var _i = 0, _dangling_braces = dangling_braces; _i < _dangling_braces.length; _i++) {\n    var index = _dangling_braces[_i];\n    cleared_string += string.slice(start, index);\n    start = index + 1;\n  }\n  return cleared_string;\n}\nexport function populateTemplateWithDigits(template, position, digits) {\n  // Using `.split('')` to iterate through a string here\n  // to avoid requiring `Symbol.iterator` polyfill.\n  // `.split('')` is generally not safe for Unicode,\n  // but in this particular case for `digits` it is safe.\n  // for (const digit of digits)\n  for (var _iterator2 = _createForOfIteratorHelperLoose(digits.split('')), _step2; !(_step2 = _iterator2()).done;) {\n    var digit = _step2.value;\n\n    // If there is room for more digits in current `template`,\n    // then set the next digit in the `template`,\n    // and return the formatted digits so far.\n    // If more digits are entered than the current format could handle.\n    if (template.slice(position + 1).search(DIGIT_PLACEHOLDER_MATCHER) < 0) {\n      return;\n    }\n    position = template.search(DIGIT_PLACEHOLDER_MATCHER);\n    template = template.replace(DIGIT_PLACEHOLDER_MATCHER, digit);\n  }\n  return [template, position];\n}", "map": {"version": 3, "names": ["_createForOfIteratorHelperLoose", "o", "allowArrayLike", "it", "Symbol", "iterator", "call", "next", "bind", "Array", "isArray", "_unsupportedIterableToArray", "length", "i", "done", "value", "TypeError", "minLen", "_arrayLikeToArray", "n", "Object", "prototype", "toString", "slice", "constructor", "name", "from", "test", "arr", "len", "arr2", "DIGIT_PLACEHOLDER", "DIGIT_PLACEHOLDER_MATCHER", "RegExp", "countOccurences", "symbol", "string", "count", "_iterator", "split", "_step", "character", "repeat", "times", "result", "cutAndStripNonPairedParens", "cutBeforeIndex", "stripNonPairedParens", "closeNonPairedParens", "template", "cut_before", "retained_template", "opening_braces", "closing_braces", "dangling_braces", "push", "pop", "start", "cleared_string", "_i", "_dangling_braces", "index", "populateTemplateWithDigits", "position", "digits", "_iterator2", "_step2", "digit", "search", "replace"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/AsYouTypeFormatter.util.js"], "sourcesContent": ["function _createForOfIteratorHelperLoose(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (it) return (it = it.call(o)).next.bind(it); if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; return function () { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\n// Should be the same as `DIGIT_PLACEHOLDER` in `libphonenumber-metadata-generator`.\nexport var DIGIT_PLACEHOLDER = 'x'; // '\\u2008' (punctuation space)\n\nvar DIGIT_PLACEHOLDER_MATCHER = new RegExp(DIGIT_PLACEHOLDER); // Counts all occurences of a symbol in a string.\n// Unicode-unsafe (because using `.split()`).\n\nexport function countOccurences(symbol, string) {\n  var count = 0; // Using `.split('')` to iterate through a string here\n  // to avoid requiring `Symbol.iterator` polyfill.\n  // `.split('')` is generally not safe for Unicode,\n  // but in this particular case for counting brackets it is safe.\n  // for (const character of string)\n\n  for (var _iterator = _createForOfIteratorHelperLoose(string.split('')), _step; !(_step = _iterator()).done;) {\n    var character = _step.value;\n\n    if (character === symbol) {\n      count++;\n    }\n  }\n\n  return count;\n} // Repeats a string (or a symbol) N times.\n// http://stackoverflow.com/questions/202605/repeat-string-javascript\n\nexport function repeat(string, times) {\n  if (times < 1) {\n    return '';\n  }\n\n  var result = '';\n\n  while (times > 1) {\n    if (times & 1) {\n      result += string;\n    }\n\n    times >>= 1;\n    string += string;\n  }\n\n  return result + string;\n}\nexport function cutAndStripNonPairedParens(string, cutBeforeIndex) {\n  if (string[cutBeforeIndex] === ')') {\n    cutBeforeIndex++;\n  }\n\n  return stripNonPairedParens(string.slice(0, cutBeforeIndex));\n}\nexport function closeNonPairedParens(template, cut_before) {\n  var retained_template = template.slice(0, cut_before);\n  var opening_braces = countOccurences('(', retained_template);\n  var closing_braces = countOccurences(')', retained_template);\n  var dangling_braces = opening_braces - closing_braces;\n\n  while (dangling_braces > 0 && cut_before < template.length) {\n    if (template[cut_before] === ')') {\n      dangling_braces--;\n    }\n\n    cut_before++;\n  }\n\n  return template.slice(0, cut_before);\n}\nexport function stripNonPairedParens(string) {\n  var dangling_braces = [];\n  var i = 0;\n\n  while (i < string.length) {\n    if (string[i] === '(') {\n      dangling_braces.push(i);\n    } else if (string[i] === ')') {\n      dangling_braces.pop();\n    }\n\n    i++;\n  }\n\n  var start = 0;\n  var cleared_string = '';\n  dangling_braces.push(string.length);\n\n  for (var _i = 0, _dangling_braces = dangling_braces; _i < _dangling_braces.length; _i++) {\n    var index = _dangling_braces[_i];\n    cleared_string += string.slice(start, index);\n    start = index + 1;\n  }\n\n  return cleared_string;\n}\nexport function populateTemplateWithDigits(template, position, digits) {\n  // Using `.split('')` to iterate through a string here\n  // to avoid requiring `Symbol.iterator` polyfill.\n  // `.split('')` is generally not safe for Unicode,\n  // but in this particular case for `digits` it is safe.\n  // for (const digit of digits)\n  for (var _iterator2 = _createForOfIteratorHelperLoose(digits.split('')), _step2; !(_step2 = _iterator2()).done;) {\n    var digit = _step2.value;\n\n    // If there is room for more digits in current `template`,\n    // then set the next digit in the `template`,\n    // and return the formatted digits so far.\n    // If more digits are entered than the current format could handle.\n    if (template.slice(position + 1).search(DIGIT_PLACEHOLDER_MATCHER) < 0) {\n      return;\n    }\n\n    position = template.search(DIGIT_PLACEHOLDER_MATCHER);\n    template = template.replace(DIGIT_PLACEHOLDER_MATCHER, digit);\n  }\n\n  return [template, position];\n}\n"], "mappings": "AAAA,SAASA,+BAA+BA,CAACC,CAAC,EAAEC,cAAc,EAAE;EAAE,IAAIC,EAAE,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAIH,CAAC,CAACG,MAAM,CAACC,QAAQ,CAAC,IAAIJ,CAAC,CAAC,YAAY,CAAC;EAAE,IAAIE,EAAE,EAAE,OAAO,CAACA,EAAE,GAAGA,EAAE,CAACG,IAAI,CAACL,CAAC,CAAC,EAAEM,IAAI,CAACC,IAAI,CAACL,EAAE,CAAC;EAAE,IAAIM,KAAK,CAACC,OAAO,CAACT,CAAC,CAAC,KAAKE,EAAE,GAAGQ,2BAA2B,CAACV,CAAC,CAAC,CAAC,IAAIC,cAAc,IAAID,CAAC,IAAI,OAAOA,CAAC,CAACW,MAAM,KAAK,QAAQ,EAAE;IAAE,IAAIT,EAAE,EAAEF,CAAC,GAAGE,EAAE;IAAE,IAAIU,CAAC,GAAG,CAAC;IAAE,OAAO,YAAY;MAAE,IAAIA,CAAC,IAAIZ,CAAC,CAACW,MAAM,EAAE,OAAO;QAAEE,IAAI,EAAE;MAAK,CAAC;MAAE,OAAO;QAAEA,IAAI,EAAE,KAAK;QAAEC,KAAK,EAAEd,CAAC,CAACY,CAAC,EAAE;MAAE,CAAC;IAAE,CAAC;EAAE;EAAE,MAAM,IAAIG,SAAS,CAAC,uIAAuI,CAAC;AAAE;AAE3lB,SAASL,2BAA2BA,CAACV,CAAC,EAAEgB,MAAM,EAAE;EAAE,IAAI,CAAChB,CAAC,EAAE;EAAQ,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOiB,iBAAiB,CAACjB,CAAC,EAAEgB,MAAM,CAAC;EAAE,IAAIE,CAAC,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAAChB,IAAI,CAACL,CAAC,CAAC,CAACsB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAE,IAAIJ,CAAC,KAAK,QAAQ,IAAIlB,CAAC,CAACuB,WAAW,EAAEL,CAAC,GAAGlB,CAAC,CAACuB,WAAW,CAACC,IAAI;EAAE,IAAIN,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOV,KAAK,CAACiB,IAAI,CAACzB,CAAC,CAAC;EAAE,IAAIkB,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACQ,IAAI,CAACR,CAAC,CAAC,EAAE,OAAOD,iBAAiB,CAACjB,CAAC,EAAEgB,MAAM,CAAC;AAAE;AAE/Z,SAASC,iBAAiBA,CAACU,GAAG,EAAEC,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGD,GAAG,CAAChB,MAAM,EAAEiB,GAAG,GAAGD,GAAG,CAAChB,MAAM;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEiB,IAAI,GAAG,IAAIrB,KAAK,CAACoB,GAAG,CAAC,EAAEhB,CAAC,GAAGgB,GAAG,EAAEhB,CAAC,EAAE,EAAE;IAAEiB,IAAI,CAACjB,CAAC,CAAC,GAAGe,GAAG,CAACf,CAAC,CAAC;EAAE;EAAE,OAAOiB,IAAI;AAAE;;AAEtL;AACA,OAAO,IAAIC,iBAAiB,GAAG,GAAG,CAAC,CAAC;;AAEpC,IAAIC,yBAAyB,GAAG,IAAIC,MAAM,CAACF,iBAAiB,CAAC,CAAC,CAAC;AAC/D;;AAEA,OAAO,SAASG,eAAeA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAC9C,IAAIC,KAAK,GAAG,CAAC,CAAC,CAAC;EACf;EACA;EACA;EACA;;EAEA,KAAK,IAAIC,SAAS,GAAGtC,+BAA+B,CAACoC,MAAM,CAACG,KAAK,CAAC,EAAE,CAAC,CAAC,EAAEC,KAAK,EAAE,CAAC,CAACA,KAAK,GAAGF,SAAS,CAAC,CAAC,EAAExB,IAAI,GAAG;IAC3G,IAAI2B,SAAS,GAAGD,KAAK,CAACzB,KAAK;IAE3B,IAAI0B,SAAS,KAAKN,MAAM,EAAE;MACxBE,KAAK,EAAE;IACT;EACF;EAEA,OAAOA,KAAK;AACd,CAAC,CAAC;AACF;;AAEA,OAAO,SAASK,MAAMA,CAACN,MAAM,EAAEO,KAAK,EAAE;EACpC,IAAIA,KAAK,GAAG,CAAC,EAAE;IACb,OAAO,EAAE;EACX;EAEA,IAAIC,MAAM,GAAG,EAAE;EAEf,OAAOD,KAAK,GAAG,CAAC,EAAE;IAChB,IAAIA,KAAK,GAAG,CAAC,EAAE;MACbC,MAAM,IAAIR,MAAM;IAClB;IAEAO,KAAK,KAAK,CAAC;IACXP,MAAM,IAAIA,MAAM;EAClB;EAEA,OAAOQ,MAAM,GAAGR,MAAM;AACxB;AACA,OAAO,SAASS,0BAA0BA,CAACT,MAAM,EAAEU,cAAc,EAAE;EACjE,IAAIV,MAAM,CAACU,cAAc,CAAC,KAAK,GAAG,EAAE;IAClCA,cAAc,EAAE;EAClB;EAEA,OAAOC,oBAAoB,CAACX,MAAM,CAACb,KAAK,CAAC,CAAC,EAAEuB,cAAc,CAAC,CAAC;AAC9D;AACA,OAAO,SAASE,oBAAoBA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EACzD,IAAIC,iBAAiB,GAAGF,QAAQ,CAAC1B,KAAK,CAAC,CAAC,EAAE2B,UAAU,CAAC;EACrD,IAAIE,cAAc,GAAGlB,eAAe,CAAC,GAAG,EAAEiB,iBAAiB,CAAC;EAC5D,IAAIE,cAAc,GAAGnB,eAAe,CAAC,GAAG,EAAEiB,iBAAiB,CAAC;EAC5D,IAAIG,eAAe,GAAGF,cAAc,GAAGC,cAAc;EAErD,OAAOC,eAAe,GAAG,CAAC,IAAIJ,UAAU,GAAGD,QAAQ,CAACrC,MAAM,EAAE;IAC1D,IAAIqC,QAAQ,CAACC,UAAU,CAAC,KAAK,GAAG,EAAE;MAChCI,eAAe,EAAE;IACnB;IAEAJ,UAAU,EAAE;EACd;EAEA,OAAOD,QAAQ,CAAC1B,KAAK,CAAC,CAAC,EAAE2B,UAAU,CAAC;AACtC;AACA,OAAO,SAASH,oBAAoBA,CAACX,MAAM,EAAE;EAC3C,IAAIkB,eAAe,GAAG,EAAE;EACxB,IAAIzC,CAAC,GAAG,CAAC;EAET,OAAOA,CAAC,GAAGuB,MAAM,CAACxB,MAAM,EAAE;IACxB,IAAIwB,MAAM,CAACvB,CAAC,CAAC,KAAK,GAAG,EAAE;MACrByC,eAAe,CAACC,IAAI,CAAC1C,CAAC,CAAC;IACzB,CAAC,MAAM,IAAIuB,MAAM,CAACvB,CAAC,CAAC,KAAK,GAAG,EAAE;MAC5ByC,eAAe,CAACE,GAAG,CAAC,CAAC;IACvB;IAEA3C,CAAC,EAAE;EACL;EAEA,IAAI4C,KAAK,GAAG,CAAC;EACb,IAAIC,cAAc,GAAG,EAAE;EACvBJ,eAAe,CAACC,IAAI,CAACnB,MAAM,CAACxB,MAAM,CAAC;EAEnC,KAAK,IAAI+C,EAAE,GAAG,CAAC,EAAEC,gBAAgB,GAAGN,eAAe,EAAEK,EAAE,GAAGC,gBAAgB,CAAChD,MAAM,EAAE+C,EAAE,EAAE,EAAE;IACvF,IAAIE,KAAK,GAAGD,gBAAgB,CAACD,EAAE,CAAC;IAChCD,cAAc,IAAItB,MAAM,CAACb,KAAK,CAACkC,KAAK,EAAEI,KAAK,CAAC;IAC5CJ,KAAK,GAAGI,KAAK,GAAG,CAAC;EACnB;EAEA,OAAOH,cAAc;AACvB;AACA,OAAO,SAASI,0BAA0BA,CAACb,QAAQ,EAAEc,QAAQ,EAAEC,MAAM,EAAE;EACrE;EACA;EACA;EACA;EACA;EACA,KAAK,IAAIC,UAAU,GAAGjE,+BAA+B,CAACgE,MAAM,CAACzB,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE2B,MAAM,EAAE,CAAC,CAACA,MAAM,GAAGD,UAAU,CAAC,CAAC,EAAEnD,IAAI,GAAG;IAC/G,IAAIqD,KAAK,GAAGD,MAAM,CAACnD,KAAK;;IAExB;IACA;IACA;IACA;IACA,IAAIkC,QAAQ,CAAC1B,KAAK,CAACwC,QAAQ,GAAG,CAAC,CAAC,CAACK,MAAM,CAACpC,yBAAyB,CAAC,GAAG,CAAC,EAAE;MACtE;IACF;IAEA+B,QAAQ,GAAGd,QAAQ,CAACmB,MAAM,CAACpC,yBAAyB,CAAC;IACrDiB,QAAQ,GAAGA,QAAQ,CAACoB,OAAO,CAACrC,yBAAyB,EAAEmC,KAAK,CAAC;EAC/D;EAEA,OAAO,CAAClB,QAAQ,EAAEc,QAAQ,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}