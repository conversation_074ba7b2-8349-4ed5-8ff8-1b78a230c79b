{"ast": null, "code": "import PhoneNumber from './PhoneNumber.js';\nexport default function getExampleNumber(country, examples, metadata) {\n  if (examples[country]) {\n    return new PhoneNumber(country, examples[country], metadata);\n  }\n}", "map": {"version": 3, "names": ["PhoneNumber", "getExampleNumber", "country", "examples", "metadata"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/getExampleNumber.js"], "sourcesContent": ["import PhoneNumber from './PhoneNumber.js';\nexport default function getExampleNumber(country, examples, metadata) {\n  if (examples[country]) {\n    return new PhoneNumber(country, examples[country], metadata);\n  }\n}\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,kBAAkB;AAC1C,eAAe,SAASC,gBAAgBA,CAACC,OAAO,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;EACpE,IAAID,QAAQ,CAACD,OAAO,CAAC,EAAE;IACrB,OAAO,IAAIF,WAAW,CAACE,OAAO,EAAEC,QAAQ,CAACD,OAAO,CAAC,EAAEE,QAAQ,CAAC;EAC9D;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}