{"ast": null, "code": "import checkNumberLength from './helpers/checkNumberLength.js';\nimport parseDigits from './helpers/parseDigits.js';\nimport formatNationalNumberUsingFormat from './helpers/formatNationalNumberUsingFormat.js';\nexport default function formatCompleteNumber(state, format, _ref) {\n  var metadata = _ref.metadata,\n    shouldTryNationalPrefixFormattingRule = _ref.shouldTryNationalPrefixFormattingRule,\n    getSeparatorAfterNationalPrefix = _ref.getSeparatorAfterNationalPrefix;\n  var matcher = new RegExp(\"^(?:\".concat(format.pattern(), \")$\"));\n  if (matcher.test(state.nationalSignificantNumber)) {\n    return formatNationalNumberWithAndWithoutNationalPrefixFormattingRule(state, format, {\n      metadata: metadata,\n      shouldTryNationalPrefixFormattingRule: shouldTryNationalPrefixFormattingRule,\n      getSeparatorAfterNationalPrefix: getSeparatorAfterNationalPrefix\n    });\n  }\n}\nexport function canFormatCompleteNumber(nationalSignificantNumber, metadata) {\n  return checkNumberLength(nationalSignificantNumber, metadata) === 'IS_POSSIBLE';\n}\nfunction formatNationalNumberWithAndWithoutNationalPrefixFormattingRule(state, format, _ref2) {\n  var metadata = _ref2.metadata,\n    shouldTryNationalPrefixFormattingRule = _ref2.shouldTryNationalPrefixFormattingRule,\n    getSeparatorAfterNationalPrefix = _ref2.getSeparatorAfterNationalPrefix;\n  // `format` has already been checked for `nationalPrefix` requirement.\n  var nationalSignificantNumber = state.nationalSignificantNumber,\n    international = state.international,\n    nationalPrefix = state.nationalPrefix,\n    carrierCode = state.carrierCode; // Format the number with using `national_prefix_formatting_rule`.\n  // If the resulting formatted number is a valid formatted number, then return it.\n  //\n  // Google's AsYouType formatter is different in a way that it doesn't try\n  // to format using the \"national prefix formatting rule\", and instead it\n  // simply prepends a national prefix followed by a \" \" character.\n  // This code does that too, but as a fallback.\n  // The reason is that \"national prefix formatting rule\" may use parentheses,\n  // which wouldn't be included has it used the simpler Google's way.\n  //\n\n  if (shouldTryNationalPrefixFormattingRule(format)) {\n    var formattedNumber = formatNationalNumber(state, format, {\n      useNationalPrefixFormattingRule: true,\n      getSeparatorAfterNationalPrefix: getSeparatorAfterNationalPrefix,\n      metadata: metadata\n    });\n    if (formattedNumber) {\n      return formattedNumber;\n    }\n  } // Format the number without using `national_prefix_formatting_rule`.\n\n  return formatNationalNumber(state, format, {\n    useNationalPrefixFormattingRule: false,\n    getSeparatorAfterNationalPrefix: getSeparatorAfterNationalPrefix,\n    metadata: metadata\n  });\n}\nfunction formatNationalNumber(state, format, _ref3) {\n  var metadata = _ref3.metadata,\n    useNationalPrefixFormattingRule = _ref3.useNationalPrefixFormattingRule,\n    getSeparatorAfterNationalPrefix = _ref3.getSeparatorAfterNationalPrefix;\n  var formattedNationalNumber = formatNationalNumberUsingFormat(state.nationalSignificantNumber, format, {\n    carrierCode: state.carrierCode,\n    useInternationalFormat: state.international,\n    withNationalPrefix: useNationalPrefixFormattingRule,\n    metadata: metadata\n  });\n  if (!useNationalPrefixFormattingRule) {\n    if (state.nationalPrefix) {\n      // If a national prefix was extracted, then just prepend it,\n      // followed by a \" \" character.\n      formattedNationalNumber = state.nationalPrefix + getSeparatorAfterNationalPrefix(format) + formattedNationalNumber;\n    } else if (state.complexPrefixBeforeNationalSignificantNumber) {\n      formattedNationalNumber = state.complexPrefixBeforeNationalSignificantNumber + ' ' + formattedNationalNumber;\n    }\n  }\n  if (isValidFormattedNationalNumber(formattedNationalNumber, state)) {\n    return formattedNationalNumber;\n  }\n} // Check that the formatted phone number contains exactly\n// the same digits that have been input by the user.\n// For example, when \"0111523456789\" is input for `AR` country,\n// the extracted `this.nationalSignificantNumber` is \"91123456789\",\n// which means that the national part of `this.digits` isn't simply equal to\n// `this.nationalPrefix` + `this.nationalSignificantNumber`.\n//\n// Also, a `format` can add extra digits to the `this.nationalSignificantNumber`\n// being formatted via `metadata[country].national_prefix_transform_rule`.\n// For example, for `VI` country, it prepends `340` to the national number,\n// and if this check hasn't been implemented, then there would be a bug\n// when `340` \"area coude\" is \"duplicated\" during input for `VI` country:\n// https://github.com/catamphetamine/libphonenumber-js/issues/318\n//\n// So, all these \"gotchas\" are filtered out.\n//\n// In the original Google's code, the comments say:\n// \"Check that we didn't remove nor add any extra digits when we matched\n// this formatting pattern. This usually happens after we entered the last\n// digit during AYTF. Eg: In case of MX, we swallow mobile token (1) when\n// formatted but AYTF should retain all the number entered and not change\n// in order to match a format (of same leading digits and length) display\n// in that way.\"\n// \"If it's the same (i.e entered number and format is same), then it's\n// safe to return this in formatted number as nothing is lost / added.\"\n// Otherwise, don't use this format.\n// https://github.com/google/libphonenumber/commit/3e7c1f04f5e7200f87fb131e6f85c6e99d60f510#diff-9149457fa9f5d608a11bb975c6ef4bc5\n// https://github.com/google/libphonenumber/commit/3ac88c7106e7dcb553bcc794b15f19185928a1c6#diff-2dcb77e833422ee304da348b905cde0b\n//\n\nfunction isValidFormattedNationalNumber(formattedNationalNumber, state) {\n  return parseDigits(formattedNationalNumber) === state.getNationalDigits();\n}", "map": {"version": 3, "names": ["checkNumberLength", "parseDigits", "formatNationalNumberUsingFormat", "formatCompleteNumber", "state", "format", "_ref", "metadata", "shouldTryNationalPrefixFormattingRule", "getSeparatorAfterNationalPrefix", "matcher", "RegExp", "concat", "pattern", "test", "nationalSignificantNumber", "formatNationalNumberWithAndWithoutNationalPrefixFormattingRule", "canFormatCompleteNumber", "_ref2", "international", "nationalPrefix", "carrierCode", "formattedNumber", "formatNationalNumber", "useNationalPrefixFormattingRule", "_ref3", "formattedNationalNumber", "useInternationalFormat", "withNationalPrefix", "complexPrefixBeforeNationalSignificantNumber", "isValidFormattedNationalNumber", "getNationalDigits"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/AsYouTypeFormatter.complete.js"], "sourcesContent": ["import checkNumberLength from './helpers/checkNumberLength.js';\nimport parseDigits from './helpers/parseDigits.js';\nimport formatNationalNumberUsingFormat from './helpers/formatNationalNumberUsingFormat.js';\nexport default function formatCompleteNumber(state, format, _ref) {\n  var metadata = _ref.metadata,\n      shouldTryNationalPrefixFormattingRule = _ref.shouldTryNationalPrefixFormattingRule,\n      getSeparatorAfterNationalPrefix = _ref.getSeparatorAfterNationalPrefix;\n  var matcher = new RegExp(\"^(?:\".concat(format.pattern(), \")$\"));\n\n  if (matcher.test(state.nationalSignificantNumber)) {\n    return formatNationalNumberWithAndWithoutNationalPrefixFormattingRule(state, format, {\n      metadata: metadata,\n      shouldTryNationalPrefixFormattingRule: shouldTryNationalPrefixFormattingRule,\n      getSeparatorAfterNationalPrefix: getSeparatorAfterNationalPrefix\n    });\n  }\n}\nexport function canFormatCompleteNumber(nationalSignificantNumber, metadata) {\n  return checkNumberLength(nationalSignificantNumber, metadata) === 'IS_POSSIBLE';\n}\n\nfunction formatNationalNumberWithAndWithoutNationalPrefixFormattingRule(state, format, _ref2) {\n  var metadata = _ref2.metadata,\n      shouldTryNationalPrefixFormattingRule = _ref2.shouldTryNationalPrefixFormattingRule,\n      getSeparatorAfterNationalPrefix = _ref2.getSeparatorAfterNationalPrefix;\n  // `format` has already been checked for `nationalPrefix` requirement.\n  var nationalSignificantNumber = state.nationalSignificantNumber,\n      international = state.international,\n      nationalPrefix = state.nationalPrefix,\n      carrierCode = state.carrierCode; // Format the number with using `national_prefix_formatting_rule`.\n  // If the resulting formatted number is a valid formatted number, then return it.\n  //\n  // Google's AsYouType formatter is different in a way that it doesn't try\n  // to format using the \"national prefix formatting rule\", and instead it\n  // simply prepends a national prefix followed by a \" \" character.\n  // This code does that too, but as a fallback.\n  // The reason is that \"national prefix formatting rule\" may use parentheses,\n  // which wouldn't be included has it used the simpler Google's way.\n  //\n\n  if (shouldTryNationalPrefixFormattingRule(format)) {\n    var formattedNumber = formatNationalNumber(state, format, {\n      useNationalPrefixFormattingRule: true,\n      getSeparatorAfterNationalPrefix: getSeparatorAfterNationalPrefix,\n      metadata: metadata\n    });\n\n    if (formattedNumber) {\n      return formattedNumber;\n    }\n  } // Format the number without using `national_prefix_formatting_rule`.\n\n\n  return formatNationalNumber(state, format, {\n    useNationalPrefixFormattingRule: false,\n    getSeparatorAfterNationalPrefix: getSeparatorAfterNationalPrefix,\n    metadata: metadata\n  });\n}\n\nfunction formatNationalNumber(state, format, _ref3) {\n  var metadata = _ref3.metadata,\n      useNationalPrefixFormattingRule = _ref3.useNationalPrefixFormattingRule,\n      getSeparatorAfterNationalPrefix = _ref3.getSeparatorAfterNationalPrefix;\n  var formattedNationalNumber = formatNationalNumberUsingFormat(state.nationalSignificantNumber, format, {\n    carrierCode: state.carrierCode,\n    useInternationalFormat: state.international,\n    withNationalPrefix: useNationalPrefixFormattingRule,\n    metadata: metadata\n  });\n\n  if (!useNationalPrefixFormattingRule) {\n    if (state.nationalPrefix) {\n      // If a national prefix was extracted, then just prepend it,\n      // followed by a \" \" character.\n      formattedNationalNumber = state.nationalPrefix + getSeparatorAfterNationalPrefix(format) + formattedNationalNumber;\n    } else if (state.complexPrefixBeforeNationalSignificantNumber) {\n      formattedNationalNumber = state.complexPrefixBeforeNationalSignificantNumber + ' ' + formattedNationalNumber;\n    }\n  }\n\n  if (isValidFormattedNationalNumber(formattedNationalNumber, state)) {\n    return formattedNationalNumber;\n  }\n} // Check that the formatted phone number contains exactly\n// the same digits that have been input by the user.\n// For example, when \"0111523456789\" is input for `AR` country,\n// the extracted `this.nationalSignificantNumber` is \"91123456789\",\n// which means that the national part of `this.digits` isn't simply equal to\n// `this.nationalPrefix` + `this.nationalSignificantNumber`.\n//\n// Also, a `format` can add extra digits to the `this.nationalSignificantNumber`\n// being formatted via `metadata[country].national_prefix_transform_rule`.\n// For example, for `VI` country, it prepends `340` to the national number,\n// and if this check hasn't been implemented, then there would be a bug\n// when `340` \"area coude\" is \"duplicated\" during input for `VI` country:\n// https://github.com/catamphetamine/libphonenumber-js/issues/318\n//\n// So, all these \"gotchas\" are filtered out.\n//\n// In the original Google's code, the comments say:\n// \"Check that we didn't remove nor add any extra digits when we matched\n// this formatting pattern. This usually happens after we entered the last\n// digit during AYTF. Eg: In case of MX, we swallow mobile token (1) when\n// formatted but AYTF should retain all the number entered and not change\n// in order to match a format (of same leading digits and length) display\n// in that way.\"\n// \"If it's the same (i.e entered number and format is same), then it's\n// safe to return this in formatted number as nothing is lost / added.\"\n// Otherwise, don't use this format.\n// https://github.com/google/libphonenumber/commit/3e7c1f04f5e7200f87fb131e6f85c6e99d60f510#diff-9149457fa9f5d608a11bb975c6ef4bc5\n// https://github.com/google/libphonenumber/commit/3ac88c7106e7dcb553bcc794b15f19185928a1c6#diff-2dcb77e833422ee304da348b905cde0b\n//\n\n\nfunction isValidFormattedNationalNumber(formattedNationalNumber, state) {\n  return parseDigits(formattedNationalNumber) === state.getNationalDigits();\n}\n"], "mappings": "AAAA,OAAOA,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,+BAA+B,MAAM,8CAA8C;AAC1F,eAAe,SAASC,oBAAoBA,CAACC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAE;EAChE,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IACxBC,qCAAqC,GAAGF,IAAI,CAACE,qCAAqC;IAClFC,+BAA+B,GAAGH,IAAI,CAACG,+BAA+B;EAC1E,IAAIC,OAAO,GAAG,IAAIC,MAAM,CAAC,MAAM,CAACC,MAAM,CAACP,MAAM,CAACQ,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;EAE/D,IAAIH,OAAO,CAACI,IAAI,CAACV,KAAK,CAACW,yBAAyB,CAAC,EAAE;IACjD,OAAOC,8DAA8D,CAACZ,KAAK,EAAEC,MAAM,EAAE;MACnFE,QAAQ,EAAEA,QAAQ;MAClBC,qCAAqC,EAAEA,qCAAqC;MAC5EC,+BAA+B,EAAEA;IACnC,CAAC,CAAC;EACJ;AACF;AACA,OAAO,SAASQ,uBAAuBA,CAACF,yBAAyB,EAAER,QAAQ,EAAE;EAC3E,OAAOP,iBAAiB,CAACe,yBAAyB,EAAER,QAAQ,CAAC,KAAK,aAAa;AACjF;AAEA,SAASS,8DAA8DA,CAACZ,KAAK,EAAEC,MAAM,EAAEa,KAAK,EAAE;EAC5F,IAAIX,QAAQ,GAAGW,KAAK,CAACX,QAAQ;IACzBC,qCAAqC,GAAGU,KAAK,CAACV,qCAAqC;IACnFC,+BAA+B,GAAGS,KAAK,CAACT,+BAA+B;EAC3E;EACA,IAAIM,yBAAyB,GAAGX,KAAK,CAACW,yBAAyB;IAC3DI,aAAa,GAAGf,KAAK,CAACe,aAAa;IACnCC,cAAc,GAAGhB,KAAK,CAACgB,cAAc;IACrCC,WAAW,GAAGjB,KAAK,CAACiB,WAAW,CAAC,CAAC;EACrC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA,IAAIb,qCAAqC,CAACH,MAAM,CAAC,EAAE;IACjD,IAAIiB,eAAe,GAAGC,oBAAoB,CAACnB,KAAK,EAAEC,MAAM,EAAE;MACxDmB,+BAA+B,EAAE,IAAI;MACrCf,+BAA+B,EAAEA,+BAA+B;MAChEF,QAAQ,EAAEA;IACZ,CAAC,CAAC;IAEF,IAAIe,eAAe,EAAE;MACnB,OAAOA,eAAe;IACxB;EACF,CAAC,CAAC;;EAGF,OAAOC,oBAAoB,CAACnB,KAAK,EAAEC,MAAM,EAAE;IACzCmB,+BAA+B,EAAE,KAAK;IACtCf,+BAA+B,EAAEA,+BAA+B;IAChEF,QAAQ,EAAEA;EACZ,CAAC,CAAC;AACJ;AAEA,SAASgB,oBAAoBA,CAACnB,KAAK,EAAEC,MAAM,EAAEoB,KAAK,EAAE;EAClD,IAAIlB,QAAQ,GAAGkB,KAAK,CAAClB,QAAQ;IACzBiB,+BAA+B,GAAGC,KAAK,CAACD,+BAA+B;IACvEf,+BAA+B,GAAGgB,KAAK,CAAChB,+BAA+B;EAC3E,IAAIiB,uBAAuB,GAAGxB,+BAA+B,CAACE,KAAK,CAACW,yBAAyB,EAAEV,MAAM,EAAE;IACrGgB,WAAW,EAAEjB,KAAK,CAACiB,WAAW;IAC9BM,sBAAsB,EAAEvB,KAAK,CAACe,aAAa;IAC3CS,kBAAkB,EAAEJ,+BAA+B;IACnDjB,QAAQ,EAAEA;EACZ,CAAC,CAAC;EAEF,IAAI,CAACiB,+BAA+B,EAAE;IACpC,IAAIpB,KAAK,CAACgB,cAAc,EAAE;MACxB;MACA;MACAM,uBAAuB,GAAGtB,KAAK,CAACgB,cAAc,GAAGX,+BAA+B,CAACJ,MAAM,CAAC,GAAGqB,uBAAuB;IACpH,CAAC,MAAM,IAAItB,KAAK,CAACyB,4CAA4C,EAAE;MAC7DH,uBAAuB,GAAGtB,KAAK,CAACyB,4CAA4C,GAAG,GAAG,GAAGH,uBAAuB;IAC9G;EACF;EAEA,IAAII,8BAA8B,CAACJ,uBAAuB,EAAEtB,KAAK,CAAC,EAAE;IAClE,OAAOsB,uBAAuB;EAChC;AACF,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,SAASI,8BAA8BA,CAACJ,uBAAuB,EAAEtB,KAAK,EAAE;EACtE,OAAOH,WAAW,CAACyB,uBAAuB,CAAC,KAAKtB,KAAK,CAAC2B,iBAAiB,CAAC,CAAC;AAC3E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}