{"ast": null, "code": "import withMetadataArgument from '../min/exports/withMetadataArgument.js';\nimport _getNumberType from '../es6/legacy/getNumberType.js';\nexport function getNumberType() {\n  return withMetadataArgument(_getNumberType, arguments);\n}", "map": {"version": 3, "names": ["withMetadataArgument", "_getNumberType", "getNumberType", "arguments"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/index.es6.exports/getNumberType.js"], "sourcesContent": ["import withMetadataArgument from '../min/exports/withMetadataArgument.js'\r\n\r\nimport _getNumberType from '../es6/legacy/getNumberType.js'\r\n\r\nexport function getNumberType() {\r\n\treturn withMetadataArgument(_getNumberType, arguments)\r\n}\r\n"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,wCAAwC;AAEzE,OAAOC,cAAc,MAAM,gCAAgC;AAE3D,OAAO,SAASC,aAAaA,CAAA,EAAG;EAC/B,OAAOF,oBAAoB,CAACC,cAAc,EAAEE,SAAS,CAAC;AACvD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}