{"ast": null, "code": "// This is a port of Google Android `libphonenumber`'s\n// `phonenumberutil.js` of December 31th, 2018.\n//\n// https://github.com/googlei18n/libphonenumber/commits/master/javascript/i18n/phonenumbers/phonenumberutil.js\nimport { VALID_DIGITS, PLUS_CHARS, MIN_LENGTH_FOR_NSN, MAX_LENGTH_FOR_NSN } from './constants.js';\nimport ParseError from './ParseError.js';\nimport Metadata from './metadata.js';\nimport isViablePhoneNumber, { isViablePhoneNumberStart } from './helpers/isViablePhoneNumber.js';\nimport extractExtension from './helpers/extension/extractExtension.js';\nimport parseIncompletePhoneNumber from './parseIncompletePhoneNumber.js';\nimport getCountryCallingCode from './getCountryCallingCode.js';\nimport { isPossibleNumber } from './isPossible.js'; // import { parseRFC3966 } from './helpers/RFC3966.js'\n\nimport PhoneNumber from './PhoneNumber.js';\nimport matchesEntirely from './helpers/matchesEntirely.js';\nimport extractCountryCallingCode from './helpers/extractCountryCallingCode.js';\nimport extractNationalNumber from './helpers/extractNationalNumber.js';\nimport stripIddPrefix from './helpers/stripIddPrefix.js';\nimport getCountryByCallingCode from './helpers/getCountryByCallingCode.js';\nimport extractFormattedPhoneNumberFromPossibleRfc3966NumberUri from './helpers/extractFormattedPhoneNumberFromPossibleRfc3966NumberUri.js'; // We don't allow input strings for parsing to be longer than 250 chars.\n// This prevents malicious input from consuming CPU.\n\nvar MAX_INPUT_STRING_LENGTH = 250; // This consists of the plus symbol, digits, and arabic-indic digits.\n\nvar PHONE_NUMBER_START_PATTERN = new RegExp('[' + PLUS_CHARS + VALID_DIGITS + ']'); // Regular expression of trailing characters that we want to remove.\n// A trailing `#` is sometimes used when writing phone numbers with extensions in US.\n// Example: \"+****************-910#\" number has extension \"910\".\n\nvar AFTER_PHONE_NUMBER_END_PATTERN = new RegExp('[^' + VALID_DIGITS + '#' + ']+$');\nvar USE_NON_GEOGRAPHIC_COUNTRY_CODE = false; // Examples:\n//\n// ```js\n// parse('8 (800) 555-35-35', 'RU')\n// parse('8 (800) 555-35-35', 'RU', metadata)\n// parse('8 (800) 555-35-35', { country: { default: 'RU' } })\n// parse('8 (800) 555-35-35', { country: { default: 'RU' } }, metadata)\n// parse('****** 555 35 35')\n// parse('****** 555 35 35', metadata)\n// ```\n//\n\n/**\r\n * Parses a phone number.\r\n *\r\n * parse('123456789', { defaultCountry: 'RU', v2: true }, metadata)\r\n * parse('123456789', { defaultCountry: 'RU' }, metadata)\r\n * parse('123456789', undefined, metadata)\r\n *\r\n * @param  {string} input\r\n * @param  {object} [options]\r\n * @param  {object} metadata\r\n * @return {object|PhoneNumber?} If `options.v2: true` flag is passed, it returns a `PhoneNumber?` instance. Otherwise, returns an object of shape `{ phone: '...', country: '...' }` (or just `{}` if no phone number was parsed).\r\n */\n\nexport default function parse(text, options, metadata) {\n  // If assigning the `{}` default value is moved to the arguments above,\n  // code coverage would decrease for some weird reason.\n  options = options || {};\n  metadata = new Metadata(metadata); // Validate `defaultCountry`.\n\n  if (options.defaultCountry && !metadata.hasCountry(options.defaultCountry)) {\n    if (options.v2) {\n      throw new ParseError('INVALID_COUNTRY');\n    }\n    throw new Error(\"Unknown country: \".concat(options.defaultCountry));\n  } // Parse the phone number.\n\n  var _parseInput = parseInput(text, options.v2, options.extract),\n    formattedPhoneNumber = _parseInput.number,\n    ext = _parseInput.ext,\n    error = _parseInput.error; // If the phone number is not viable then return nothing.\n\n  if (!formattedPhoneNumber) {\n    if (options.v2) {\n      if (error === 'TOO_SHORT') {\n        throw new ParseError('TOO_SHORT');\n      }\n      throw new ParseError('NOT_A_NUMBER');\n    }\n    return {};\n  }\n  var _parsePhoneNumber = parsePhoneNumber(formattedPhoneNumber, options.defaultCountry, options.defaultCallingCode, metadata),\n    country = _parsePhoneNumber.country,\n    nationalNumber = _parsePhoneNumber.nationalNumber,\n    countryCallingCode = _parsePhoneNumber.countryCallingCode,\n    countryCallingCodeSource = _parsePhoneNumber.countryCallingCodeSource,\n    carrierCode = _parsePhoneNumber.carrierCode;\n  if (!metadata.hasSelectedNumberingPlan()) {\n    if (options.v2) {\n      throw new ParseError('INVALID_COUNTRY');\n    }\n    return {};\n  } // Validate national (significant) number length.\n\n  if (!nationalNumber || nationalNumber.length < MIN_LENGTH_FOR_NSN) {\n    // Won't throw here because the regexp already demands length > 1.\n\n    /* istanbul ignore if */\n    if (options.v2) {\n      throw new ParseError('TOO_SHORT');\n    } // Google's demo just throws an error in this case.\n\n    return {};\n  } // Validate national (significant) number length.\n  //\n  // A sidenote:\n  //\n  // They say that sometimes national (significant) numbers\n  // can be longer than `MAX_LENGTH_FOR_NSN` (e.g. in Germany).\n  // https://github.com/googlei18n/libphonenumber/blob/7e1748645552da39c4e1ba731e47969d97bdb539/resources/phonenumber.proto#L36\n  // Such numbers will just be discarded.\n  //\n\n  if (nationalNumber.length > MAX_LENGTH_FOR_NSN) {\n    if (options.v2) {\n      throw new ParseError('TOO_LONG');\n    } // Google's demo just throws an error in this case.\n\n    return {};\n  }\n  if (options.v2) {\n    var phoneNumber = new PhoneNumber(countryCallingCode, nationalNumber, metadata.metadata);\n    if (country) {\n      phoneNumber.country = country;\n    }\n    if (carrierCode) {\n      phoneNumber.carrierCode = carrierCode;\n    }\n    if (ext) {\n      phoneNumber.ext = ext;\n    }\n    phoneNumber.__countryCallingCodeSource = countryCallingCodeSource;\n    return phoneNumber;\n  } // Check if national phone number pattern matches the number.\n  // National number pattern is different for each country,\n  // even for those ones which are part of the \"NANPA\" group.\n\n  var valid = (options.extended ? metadata.hasSelectedNumberingPlan() : country) ? matchesEntirely(nationalNumber, metadata.nationalNumberPattern()) : false;\n  if (!options.extended) {\n    return valid ? result(country, nationalNumber, ext) : {};\n  } // isInternational: countryCallingCode !== undefined\n\n  return {\n    country: country,\n    countryCallingCode: countryCallingCode,\n    carrierCode: carrierCode,\n    valid: valid,\n    possible: valid ? true : options.extended === true && metadata.possibleLengths() && isPossibleNumber(nationalNumber, metadata) ? true : false,\n    phone: nationalNumber,\n    ext: ext\n  };\n}\n/**\r\n * Extracts a formatted phone number from text.\r\n * Doesn't guarantee that the extracted phone number\r\n * is a valid phone number (for example, doesn't validate its length).\r\n * @param  {string} text\r\n * @param  {boolean} [extract] — If `false`, then will parse the entire `text` as a phone number.\r\n * @param  {boolean} [throwOnError] — By default, it won't throw if the text is too long.\r\n * @return {string}\r\n * @example\r\n * // Returns \"(*************\".\r\n * extractFormattedPhoneNumber(\"Call (************* for assistance.\")\r\n */\n\nfunction _extractFormattedPhoneNumber(text, extract, throwOnError) {\n  if (!text) {\n    return;\n  }\n  if (text.length > MAX_INPUT_STRING_LENGTH) {\n    if (throwOnError) {\n      throw new ParseError('TOO_LONG');\n    }\n    return;\n  }\n  if (extract === false) {\n    return text;\n  } // Attempt to extract a possible number from the string passed in\n\n  var startsAt = text.search(PHONE_NUMBER_START_PATTERN);\n  if (startsAt < 0) {\n    return;\n  }\n  return text // Trim everything to the left of the phone number\n  .slice(startsAt) // Remove trailing non-numerical characters\n  .replace(AFTER_PHONE_NUMBER_END_PATTERN, '');\n}\n/**\r\n * @param  {string} text - Input.\r\n * @param  {boolean} v2 - Legacy API functions don't pass `v2: true` flag.\r\n * @param  {boolean} [extract] - Whether to extract a phone number from `text`, or attempt to parse the entire text as a phone number.\r\n * @return {object} `{ ?number, ?ext }`.\r\n */\n\nfunction parseInput(text, v2, extract) {\n  // // Parse RFC 3966 phone number URI.\n  // if (text && text.indexOf('tel:') === 0) {\n  // \treturn parseRFC3966(text)\n  // }\n  // let number = extractFormattedPhoneNumber(text, extract, v2)\n  var number = extractFormattedPhoneNumberFromPossibleRfc3966NumberUri(text, {\n    extractFormattedPhoneNumber: function extractFormattedPhoneNumber(text) {\n      return _extractFormattedPhoneNumber(text, extract, v2);\n    }\n  }); // If the phone number is not viable, then abort.\n\n  if (!number) {\n    return {};\n  }\n  if (!isViablePhoneNumber(number)) {\n    if (isViablePhoneNumberStart(number)) {\n      return {\n        error: 'TOO_SHORT'\n      };\n    }\n    return {};\n  } // Attempt to parse extension first, since it doesn't require region-specific\n  // data and we want to have the non-normalised number here.\n\n  var withExtensionStripped = extractExtension(number);\n  if (withExtensionStripped.ext) {\n    return withExtensionStripped;\n  }\n  return {\n    number: number\n  };\n}\n/**\r\n * Creates `parse()` result object.\r\n */\n\nfunction result(country, nationalNumber, ext) {\n  var result = {\n    country: country,\n    phone: nationalNumber\n  };\n  if (ext) {\n    result.ext = ext;\n  }\n  return result;\n}\n/**\r\n * Parses a viable phone number.\r\n * @param {string} formattedPhoneNumber — Example: \"(*************\".\r\n * @param {string} [defaultCountry]\r\n * @param {string} [defaultCallingCode]\r\n * @param {Metadata} metadata\r\n * @return {object} Returns `{ country: string?, countryCallingCode: string?, nationalNumber: string? }`.\r\n */\n\nfunction parsePhoneNumber(formattedPhoneNumber, defaultCountry, defaultCallingCode, metadata) {\n  // Extract calling code from phone number.\n  var _extractCountryCallin = extractCountryCallingCode(parseIncompletePhoneNumber(formattedPhoneNumber), defaultCountry, defaultCallingCode, metadata.metadata),\n    countryCallingCodeSource = _extractCountryCallin.countryCallingCodeSource,\n    countryCallingCode = _extractCountryCallin.countryCallingCode,\n    number = _extractCountryCallin.number; // Choose a country by `countryCallingCode`.\n\n  var country;\n  if (countryCallingCode) {\n    metadata.selectNumberingPlan(countryCallingCode);\n  } // If `formattedPhoneNumber` is passed in \"national\" format\n  // then `number` is defined and `countryCallingCode` is `undefined`.\n  else if (number && (defaultCountry || defaultCallingCode)) {\n    metadata.selectNumberingPlan(defaultCountry, defaultCallingCode);\n    if (defaultCountry) {\n      country = defaultCountry;\n    } else {\n      /* istanbul ignore if */\n      if (USE_NON_GEOGRAPHIC_COUNTRY_CODE) {\n        if (metadata.isNonGeographicCallingCode(defaultCallingCode)) {\n          country = '001';\n        }\n      }\n    }\n    countryCallingCode = defaultCallingCode || getCountryCallingCode(defaultCountry, metadata.metadata);\n  } else return {};\n  if (!number) {\n    return {\n      countryCallingCodeSource: countryCallingCodeSource,\n      countryCallingCode: countryCallingCode\n    };\n  }\n  var _extractNationalNumbe = extractNationalNumber(parseIncompletePhoneNumber(number), metadata),\n    nationalNumber = _extractNationalNumbe.nationalNumber,\n    carrierCode = _extractNationalNumbe.carrierCode; // Sometimes there are several countries\n  // corresponding to the same country phone code\n  // (e.g. NANPA countries all having `1` country phone code).\n  // Therefore, to reliably determine the exact country,\n  // national (significant) number should have been parsed first.\n  //\n  // When `metadata.json` is generated, all \"ambiguous\" country phone codes\n  // get their countries populated with the full set of\n  // \"phone number type\" regular expressions.\n  //\n\n  var exactCountry = getCountryByCallingCode(countryCallingCode, {\n    nationalNumber: nationalNumber,\n    defaultCountry: defaultCountry,\n    metadata: metadata\n  });\n  if (exactCountry) {\n    country = exactCountry;\n    /* istanbul ignore if */\n\n    if (exactCountry === '001') {// Can't happen with `USE_NON_GEOGRAPHIC_COUNTRY_CODE` being `false`.\n      // If `USE_NON_GEOGRAPHIC_COUNTRY_CODE` is set to `true` for some reason,\n      // then remove the \"istanbul ignore if\".\n    } else {\n      metadata.country(country);\n    }\n  }\n  return {\n    country: country,\n    countryCallingCode: countryCallingCode,\n    countryCallingCodeSource: countryCallingCodeSource,\n    nationalNumber: nationalNumber,\n    carrierCode: carrierCode\n  };\n}", "map": {"version": 3, "names": ["VALID_DIGITS", "PLUS_CHARS", "MIN_LENGTH_FOR_NSN", "MAX_LENGTH_FOR_NSN", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "isViablePhoneNumber", "isViablePhoneNumberStart", "extractExtension", "parseIncompletePhoneNumber", "getCountryCallingCode", "isPossibleNumber", "PhoneNumber", "matchesEntirely", "extractCountryCallingCode", "extractNationalNumber", "stripIddPrefix", "getCountryByCallingCode", "extractFormattedPhoneNumberFromPossibleRfc3966NumberUri", "MAX_INPUT_STRING_LENGTH", "PHONE_NUMBER_START_PATTERN", "RegExp", "AFTER_PHONE_NUMBER_END_PATTERN", "USE_NON_GEOGRAPHIC_COUNTRY_CODE", "parse", "text", "options", "metadata", "defaultCountry", "hasCountry", "v2", "Error", "concat", "_parseInput", "parseInput", "extract", "formattedPhoneNumber", "number", "ext", "error", "_parsePhoneNumber", "parsePhoneNumber", "defaultCallingCode", "country", "nationalNumber", "countryCallingCode", "countryCallingCodeSource", "carrierCode", "hasSelectedNumberingPlan", "length", "phoneNumber", "__countryCallingCodeSource", "valid", "extended", "nationalNumberPattern", "result", "possible", "possibleLengths", "phone", "_extractFormattedPhoneNumber", "throwOnError", "startsAt", "search", "slice", "replace", "extractFormattedPhoneNumber", "withExtensionStripped", "_extractCountryCallin", "selectNumberingPlan", "isNonGeographicCallingCode", "_extractNationalNumbe", "exactCountry"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/parse.js"], "sourcesContent": ["// This is a port of Google Android `libphonenumber`'s\n// `phonenumberutil.js` of December 31th, 2018.\n//\n// https://github.com/googlei18n/libphonenumber/commits/master/javascript/i18n/phonenumbers/phonenumberutil.js\nimport { VALID_DIGITS, PLUS_CHARS, MIN_LENGTH_FOR_NSN, MAX_LENGTH_FOR_NSN } from './constants.js';\nimport ParseError from './ParseError.js';\nimport Metadata from './metadata.js';\nimport isViablePhoneNumber, { isViablePhoneNumberStart } from './helpers/isViablePhoneNumber.js';\nimport extractExtension from './helpers/extension/extractExtension.js';\nimport parseIncompletePhoneNumber from './parseIncompletePhoneNumber.js';\nimport getCountryCallingCode from './getCountryCallingCode.js';\nimport { isPossibleNumber } from './isPossible.js'; // import { parseRFC3966 } from './helpers/RFC3966.js'\n\nimport PhoneNumber from './PhoneNumber.js';\nimport matchesEntirely from './helpers/matchesEntirely.js';\nimport extractCountryCallingCode from './helpers/extractCountryCallingCode.js';\nimport extractNationalNumber from './helpers/extractNationalNumber.js';\nimport stripIddPrefix from './helpers/stripIddPrefix.js';\nimport getCountryByCallingCode from './helpers/getCountryByCallingCode.js';\nimport extractFormattedPhoneNumberFromPossibleRfc3966NumberUri from './helpers/extractFormattedPhoneNumberFromPossibleRfc3966NumberUri.js'; // We don't allow input strings for parsing to be longer than 250 chars.\n// This prevents malicious input from consuming CPU.\n\nvar MAX_INPUT_STRING_LENGTH = 250; // This consists of the plus symbol, digits, and arabic-indic digits.\n\nvar PHONE_NUMBER_START_PATTERN = new RegExp('[' + PLUS_CHARS + VALID_DIGITS + ']'); // Regular expression of trailing characters that we want to remove.\n// A trailing `#` is sometimes used when writing phone numbers with extensions in US.\n// Example: \"+****************-910#\" number has extension \"910\".\n\nvar AFTER_PHONE_NUMBER_END_PATTERN = new RegExp('[^' + VALID_DIGITS + '#' + ']+$');\nvar USE_NON_GEOGRAPHIC_COUNTRY_CODE = false; // Examples:\n//\n// ```js\n// parse('8 (800) 555-35-35', 'RU')\n// parse('8 (800) 555-35-35', 'RU', metadata)\n// parse('8 (800) 555-35-35', { country: { default: 'RU' } })\n// parse('8 (800) 555-35-35', { country: { default: 'RU' } }, metadata)\n// parse('****** 555 35 35')\n// parse('****** 555 35 35', metadata)\n// ```\n//\n\n/**\r\n * Parses a phone number.\r\n *\r\n * parse('123456789', { defaultCountry: 'RU', v2: true }, metadata)\r\n * parse('123456789', { defaultCountry: 'RU' }, metadata)\r\n * parse('123456789', undefined, metadata)\r\n *\r\n * @param  {string} input\r\n * @param  {object} [options]\r\n * @param  {object} metadata\r\n * @return {object|PhoneNumber?} If `options.v2: true` flag is passed, it returns a `PhoneNumber?` instance. Otherwise, returns an object of shape `{ phone: '...', country: '...' }` (or just `{}` if no phone number was parsed).\r\n */\n\nexport default function parse(text, options, metadata) {\n  // If assigning the `{}` default value is moved to the arguments above,\n  // code coverage would decrease for some weird reason.\n  options = options || {};\n  metadata = new Metadata(metadata); // Validate `defaultCountry`.\n\n  if (options.defaultCountry && !metadata.hasCountry(options.defaultCountry)) {\n    if (options.v2) {\n      throw new ParseError('INVALID_COUNTRY');\n    }\n\n    throw new Error(\"Unknown country: \".concat(options.defaultCountry));\n  } // Parse the phone number.\n\n\n  var _parseInput = parseInput(text, options.v2, options.extract),\n      formattedPhoneNumber = _parseInput.number,\n      ext = _parseInput.ext,\n      error = _parseInput.error; // If the phone number is not viable then return nothing.\n\n\n  if (!formattedPhoneNumber) {\n    if (options.v2) {\n      if (error === 'TOO_SHORT') {\n        throw new ParseError('TOO_SHORT');\n      }\n\n      throw new ParseError('NOT_A_NUMBER');\n    }\n\n    return {};\n  }\n\n  var _parsePhoneNumber = parsePhoneNumber(formattedPhoneNumber, options.defaultCountry, options.defaultCallingCode, metadata),\n      country = _parsePhoneNumber.country,\n      nationalNumber = _parsePhoneNumber.nationalNumber,\n      countryCallingCode = _parsePhoneNumber.countryCallingCode,\n      countryCallingCodeSource = _parsePhoneNumber.countryCallingCodeSource,\n      carrierCode = _parsePhoneNumber.carrierCode;\n\n  if (!metadata.hasSelectedNumberingPlan()) {\n    if (options.v2) {\n      throw new ParseError('INVALID_COUNTRY');\n    }\n\n    return {};\n  } // Validate national (significant) number length.\n\n\n  if (!nationalNumber || nationalNumber.length < MIN_LENGTH_FOR_NSN) {\n    // Won't throw here because the regexp already demands length > 1.\n\n    /* istanbul ignore if */\n    if (options.v2) {\n      throw new ParseError('TOO_SHORT');\n    } // Google's demo just throws an error in this case.\n\n\n    return {};\n  } // Validate national (significant) number length.\n  //\n  // A sidenote:\n  //\n  // They say that sometimes national (significant) numbers\n  // can be longer than `MAX_LENGTH_FOR_NSN` (e.g. in Germany).\n  // https://github.com/googlei18n/libphonenumber/blob/7e1748645552da39c4e1ba731e47969d97bdb539/resources/phonenumber.proto#L36\n  // Such numbers will just be discarded.\n  //\n\n\n  if (nationalNumber.length > MAX_LENGTH_FOR_NSN) {\n    if (options.v2) {\n      throw new ParseError('TOO_LONG');\n    } // Google's demo just throws an error in this case.\n\n\n    return {};\n  }\n\n  if (options.v2) {\n    var phoneNumber = new PhoneNumber(countryCallingCode, nationalNumber, metadata.metadata);\n\n    if (country) {\n      phoneNumber.country = country;\n    }\n\n    if (carrierCode) {\n      phoneNumber.carrierCode = carrierCode;\n    }\n\n    if (ext) {\n      phoneNumber.ext = ext;\n    }\n\n    phoneNumber.__countryCallingCodeSource = countryCallingCodeSource;\n    return phoneNumber;\n  } // Check if national phone number pattern matches the number.\n  // National number pattern is different for each country,\n  // even for those ones which are part of the \"NANPA\" group.\n\n\n  var valid = (options.extended ? metadata.hasSelectedNumberingPlan() : country) ? matchesEntirely(nationalNumber, metadata.nationalNumberPattern()) : false;\n\n  if (!options.extended) {\n    return valid ? result(country, nationalNumber, ext) : {};\n  } // isInternational: countryCallingCode !== undefined\n\n\n  return {\n    country: country,\n    countryCallingCode: countryCallingCode,\n    carrierCode: carrierCode,\n    valid: valid,\n    possible: valid ? true : options.extended === true && metadata.possibleLengths() && isPossibleNumber(nationalNumber, metadata) ? true : false,\n    phone: nationalNumber,\n    ext: ext\n  };\n}\n/**\r\n * Extracts a formatted phone number from text.\r\n * Doesn't guarantee that the extracted phone number\r\n * is a valid phone number (for example, doesn't validate its length).\r\n * @param  {string} text\r\n * @param  {boolean} [extract] — If `false`, then will parse the entire `text` as a phone number.\r\n * @param  {boolean} [throwOnError] — By default, it won't throw if the text is too long.\r\n * @return {string}\r\n * @example\r\n * // Returns \"(*************\".\r\n * extractFormattedPhoneNumber(\"Call (************* for assistance.\")\r\n */\n\nfunction _extractFormattedPhoneNumber(text, extract, throwOnError) {\n  if (!text) {\n    return;\n  }\n\n  if (text.length > MAX_INPUT_STRING_LENGTH) {\n    if (throwOnError) {\n      throw new ParseError('TOO_LONG');\n    }\n\n    return;\n  }\n\n  if (extract === false) {\n    return text;\n  } // Attempt to extract a possible number from the string passed in\n\n\n  var startsAt = text.search(PHONE_NUMBER_START_PATTERN);\n\n  if (startsAt < 0) {\n    return;\n  }\n\n  return text // Trim everything to the left of the phone number\n  .slice(startsAt) // Remove trailing non-numerical characters\n  .replace(AFTER_PHONE_NUMBER_END_PATTERN, '');\n}\n/**\r\n * @param  {string} text - Input.\r\n * @param  {boolean} v2 - Legacy API functions don't pass `v2: true` flag.\r\n * @param  {boolean} [extract] - Whether to extract a phone number from `text`, or attempt to parse the entire text as a phone number.\r\n * @return {object} `{ ?number, ?ext }`.\r\n */\n\n\nfunction parseInput(text, v2, extract) {\n  // // Parse RFC 3966 phone number URI.\n  // if (text && text.indexOf('tel:') === 0) {\n  // \treturn parseRFC3966(text)\n  // }\n  // let number = extractFormattedPhoneNumber(text, extract, v2)\n  var number = extractFormattedPhoneNumberFromPossibleRfc3966NumberUri(text, {\n    extractFormattedPhoneNumber: function extractFormattedPhoneNumber(text) {\n      return _extractFormattedPhoneNumber(text, extract, v2);\n    }\n  }); // If the phone number is not viable, then abort.\n\n  if (!number) {\n    return {};\n  }\n\n  if (!isViablePhoneNumber(number)) {\n    if (isViablePhoneNumberStart(number)) {\n      return {\n        error: 'TOO_SHORT'\n      };\n    }\n\n    return {};\n  } // Attempt to parse extension first, since it doesn't require region-specific\n  // data and we want to have the non-normalised number here.\n\n\n  var withExtensionStripped = extractExtension(number);\n\n  if (withExtensionStripped.ext) {\n    return withExtensionStripped;\n  }\n\n  return {\n    number: number\n  };\n}\n/**\r\n * Creates `parse()` result object.\r\n */\n\n\nfunction result(country, nationalNumber, ext) {\n  var result = {\n    country: country,\n    phone: nationalNumber\n  };\n\n  if (ext) {\n    result.ext = ext;\n  }\n\n  return result;\n}\n/**\r\n * Parses a viable phone number.\r\n * @param {string} formattedPhoneNumber — Example: \"(*************\".\r\n * @param {string} [defaultCountry]\r\n * @param {string} [defaultCallingCode]\r\n * @param {Metadata} metadata\r\n * @return {object} Returns `{ country: string?, countryCallingCode: string?, nationalNumber: string? }`.\r\n */\n\n\nfunction parsePhoneNumber(formattedPhoneNumber, defaultCountry, defaultCallingCode, metadata) {\n  // Extract calling code from phone number.\n  var _extractCountryCallin = extractCountryCallingCode(parseIncompletePhoneNumber(formattedPhoneNumber), defaultCountry, defaultCallingCode, metadata.metadata),\n      countryCallingCodeSource = _extractCountryCallin.countryCallingCodeSource,\n      countryCallingCode = _extractCountryCallin.countryCallingCode,\n      number = _extractCountryCallin.number; // Choose a country by `countryCallingCode`.\n\n\n  var country;\n\n  if (countryCallingCode) {\n    metadata.selectNumberingPlan(countryCallingCode);\n  } // If `formattedPhoneNumber` is passed in \"national\" format\n  // then `number` is defined and `countryCallingCode` is `undefined`.\n  else if (number && (defaultCountry || defaultCallingCode)) {\n    metadata.selectNumberingPlan(defaultCountry, defaultCallingCode);\n\n    if (defaultCountry) {\n      country = defaultCountry;\n    } else {\n      /* istanbul ignore if */\n      if (USE_NON_GEOGRAPHIC_COUNTRY_CODE) {\n        if (metadata.isNonGeographicCallingCode(defaultCallingCode)) {\n          country = '001';\n        }\n      }\n    }\n\n    countryCallingCode = defaultCallingCode || getCountryCallingCode(defaultCountry, metadata.metadata);\n  } else return {};\n\n  if (!number) {\n    return {\n      countryCallingCodeSource: countryCallingCodeSource,\n      countryCallingCode: countryCallingCode\n    };\n  }\n\n  var _extractNationalNumbe = extractNationalNumber(parseIncompletePhoneNumber(number), metadata),\n      nationalNumber = _extractNationalNumbe.nationalNumber,\n      carrierCode = _extractNationalNumbe.carrierCode; // Sometimes there are several countries\n  // corresponding to the same country phone code\n  // (e.g. NANPA countries all having `1` country phone code).\n  // Therefore, to reliably determine the exact country,\n  // national (significant) number should have been parsed first.\n  //\n  // When `metadata.json` is generated, all \"ambiguous\" country phone codes\n  // get their countries populated with the full set of\n  // \"phone number type\" regular expressions.\n  //\n\n\n  var exactCountry = getCountryByCallingCode(countryCallingCode, {\n    nationalNumber: nationalNumber,\n    defaultCountry: defaultCountry,\n    metadata: metadata\n  });\n\n  if (exactCountry) {\n    country = exactCountry;\n    /* istanbul ignore if */\n\n    if (exactCountry === '001') {// Can't happen with `USE_NON_GEOGRAPHIC_COUNTRY_CODE` being `false`.\n      // If `USE_NON_GEOGRAPHIC_COUNTRY_CODE` is set to `true` for some reason,\n      // then remove the \"istanbul ignore if\".\n    } else {\n      metadata.country(country);\n    }\n  }\n\n  return {\n    country: country,\n    countryCallingCode: countryCallingCode,\n    countryCallingCodeSource: countryCallingCodeSource,\n    nationalNumber: nationalNumber,\n    carrierCode: carrierCode\n  };\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,YAAY,EAAEC,UAAU,EAAEC,kBAAkB,EAAEC,kBAAkB,QAAQ,gBAAgB;AACjG,OAAOC,UAAU,MAAM,iBAAiB;AACxC,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,mBAAmB,IAAIC,wBAAwB,QAAQ,kCAAkC;AAChG,OAAOC,gBAAgB,MAAM,yCAAyC;AACtE,OAAOC,0BAA0B,MAAM,iCAAiC;AACxE,OAAOC,qBAAqB,MAAM,4BAA4B;AAC9D,SAASC,gBAAgB,QAAQ,iBAAiB,CAAC,CAAC;;AAEpD,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,yBAAyB,MAAM,wCAAwC;AAC9E,OAAOC,qBAAqB,MAAM,oCAAoC;AACtE,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,uBAAuB,MAAM,sCAAsC;AAC1E,OAAOC,uDAAuD,MAAM,sEAAsE,CAAC,CAAC;AAC5I;;AAEA,IAAIC,uBAAuB,GAAG,GAAG,CAAC,CAAC;;AAEnC,IAAIC,0BAA0B,GAAG,IAAIC,MAAM,CAAC,GAAG,GAAGpB,UAAU,GAAGD,YAAY,GAAG,GAAG,CAAC,CAAC,CAAC;AACpF;AACA;;AAEA,IAAIsB,8BAA8B,GAAG,IAAID,MAAM,CAAC,IAAI,GAAGrB,YAAY,GAAG,GAAG,GAAG,KAAK,CAAC;AAClF,IAAIuB,+BAA+B,GAAG,KAAK,CAAC,CAAC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,KAAKA,CAACC,IAAI,EAAEC,OAAO,EAAEC,QAAQ,EAAE;EACrD;EACA;EACAD,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvBC,QAAQ,GAAG,IAAItB,QAAQ,CAACsB,QAAQ,CAAC,CAAC,CAAC;;EAEnC,IAAID,OAAO,CAACE,cAAc,IAAI,CAACD,QAAQ,CAACE,UAAU,CAACH,OAAO,CAACE,cAAc,CAAC,EAAE;IAC1E,IAAIF,OAAO,CAACI,EAAE,EAAE;MACd,MAAM,IAAI1B,UAAU,CAAC,iBAAiB,CAAC;IACzC;IAEA,MAAM,IAAI2B,KAAK,CAAC,mBAAmB,CAACC,MAAM,CAACN,OAAO,CAACE,cAAc,CAAC,CAAC;EACrE,CAAC,CAAC;;EAGF,IAAIK,WAAW,GAAGC,UAAU,CAACT,IAAI,EAAEC,OAAO,CAACI,EAAE,EAAEJ,OAAO,CAACS,OAAO,CAAC;IAC3DC,oBAAoB,GAAGH,WAAW,CAACI,MAAM;IACzCC,GAAG,GAAGL,WAAW,CAACK,GAAG;IACrBC,KAAK,GAAGN,WAAW,CAACM,KAAK,CAAC,CAAC;;EAG/B,IAAI,CAACH,oBAAoB,EAAE;IACzB,IAAIV,OAAO,CAACI,EAAE,EAAE;MACd,IAAIS,KAAK,KAAK,WAAW,EAAE;QACzB,MAAM,IAAInC,UAAU,CAAC,WAAW,CAAC;MACnC;MAEA,MAAM,IAAIA,UAAU,CAAC,cAAc,CAAC;IACtC;IAEA,OAAO,CAAC,CAAC;EACX;EAEA,IAAIoC,iBAAiB,GAAGC,gBAAgB,CAACL,oBAAoB,EAAEV,OAAO,CAACE,cAAc,EAAEF,OAAO,CAACgB,kBAAkB,EAAEf,QAAQ,CAAC;IACxHgB,OAAO,GAAGH,iBAAiB,CAACG,OAAO;IACnCC,cAAc,GAAGJ,iBAAiB,CAACI,cAAc;IACjDC,kBAAkB,GAAGL,iBAAiB,CAACK,kBAAkB;IACzDC,wBAAwB,GAAGN,iBAAiB,CAACM,wBAAwB;IACrEC,WAAW,GAAGP,iBAAiB,CAACO,WAAW;EAE/C,IAAI,CAACpB,QAAQ,CAACqB,wBAAwB,CAAC,CAAC,EAAE;IACxC,IAAItB,OAAO,CAACI,EAAE,EAAE;MACd,MAAM,IAAI1B,UAAU,CAAC,iBAAiB,CAAC;IACzC;IAEA,OAAO,CAAC,CAAC;EACX,CAAC,CAAC;;EAGF,IAAI,CAACwC,cAAc,IAAIA,cAAc,CAACK,MAAM,GAAG/C,kBAAkB,EAAE;IACjE;;IAEA;IACA,IAAIwB,OAAO,CAACI,EAAE,EAAE;MACd,MAAM,IAAI1B,UAAU,CAAC,WAAW,CAAC;IACnC,CAAC,CAAC;;IAGF,OAAO,CAAC,CAAC;EACX,CAAC,CAAC;EACF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAGA,IAAIwC,cAAc,CAACK,MAAM,GAAG9C,kBAAkB,EAAE;IAC9C,IAAIuB,OAAO,CAACI,EAAE,EAAE;MACd,MAAM,IAAI1B,UAAU,CAAC,UAAU,CAAC;IAClC,CAAC,CAAC;;IAGF,OAAO,CAAC,CAAC;EACX;EAEA,IAAIsB,OAAO,CAACI,EAAE,EAAE;IACd,IAAIoB,WAAW,GAAG,IAAItC,WAAW,CAACiC,kBAAkB,EAAED,cAAc,EAAEjB,QAAQ,CAACA,QAAQ,CAAC;IAExF,IAAIgB,OAAO,EAAE;MACXO,WAAW,CAACP,OAAO,GAAGA,OAAO;IAC/B;IAEA,IAAII,WAAW,EAAE;MACfG,WAAW,CAACH,WAAW,GAAGA,WAAW;IACvC;IAEA,IAAIT,GAAG,EAAE;MACPY,WAAW,CAACZ,GAAG,GAAGA,GAAG;IACvB;IAEAY,WAAW,CAACC,0BAA0B,GAAGL,wBAAwB;IACjE,OAAOI,WAAW;EACpB,CAAC,CAAC;EACF;EACA;;EAGA,IAAIE,KAAK,GAAG,CAAC1B,OAAO,CAAC2B,QAAQ,GAAG1B,QAAQ,CAACqB,wBAAwB,CAAC,CAAC,GAAGL,OAAO,IAAI9B,eAAe,CAAC+B,cAAc,EAAEjB,QAAQ,CAAC2B,qBAAqB,CAAC,CAAC,CAAC,GAAG,KAAK;EAE1J,IAAI,CAAC5B,OAAO,CAAC2B,QAAQ,EAAE;IACrB,OAAOD,KAAK,GAAGG,MAAM,CAACZ,OAAO,EAAEC,cAAc,EAAEN,GAAG,CAAC,GAAG,CAAC,CAAC;EAC1D,CAAC,CAAC;;EAGF,OAAO;IACLK,OAAO,EAAEA,OAAO;IAChBE,kBAAkB,EAAEA,kBAAkB;IACtCE,WAAW,EAAEA,WAAW;IACxBK,KAAK,EAAEA,KAAK;IACZI,QAAQ,EAAEJ,KAAK,GAAG,IAAI,GAAG1B,OAAO,CAAC2B,QAAQ,KAAK,IAAI,IAAI1B,QAAQ,CAAC8B,eAAe,CAAC,CAAC,IAAI9C,gBAAgB,CAACiC,cAAc,EAAEjB,QAAQ,CAAC,GAAG,IAAI,GAAG,KAAK;IAC7I+B,KAAK,EAAEd,cAAc;IACrBN,GAAG,EAAEA;EACP,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASqB,4BAA4BA,CAAClC,IAAI,EAAEU,OAAO,EAAEyB,YAAY,EAAE;EACjE,IAAI,CAACnC,IAAI,EAAE;IACT;EACF;EAEA,IAAIA,IAAI,CAACwB,MAAM,GAAG9B,uBAAuB,EAAE;IACzC,IAAIyC,YAAY,EAAE;MAChB,MAAM,IAAIxD,UAAU,CAAC,UAAU,CAAC;IAClC;IAEA;EACF;EAEA,IAAI+B,OAAO,KAAK,KAAK,EAAE;IACrB,OAAOV,IAAI;EACb,CAAC,CAAC;;EAGF,IAAIoC,QAAQ,GAAGpC,IAAI,CAACqC,MAAM,CAAC1C,0BAA0B,CAAC;EAEtD,IAAIyC,QAAQ,GAAG,CAAC,EAAE;IAChB;EACF;EAEA,OAAOpC,IAAI,CAAC;EAAA,CACXsC,KAAK,CAACF,QAAQ,CAAC,CAAC;EAAA,CAChBG,OAAO,CAAC1C,8BAA8B,EAAE,EAAE,CAAC;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,SAASY,UAAUA,CAACT,IAAI,EAAEK,EAAE,EAAEK,OAAO,EAAE;EACrC;EACA;EACA;EACA;EACA;EACA,IAAIE,MAAM,GAAGnB,uDAAuD,CAACO,IAAI,EAAE;IACzEwC,2BAA2B,EAAE,SAASA,2BAA2BA,CAACxC,IAAI,EAAE;MACtE,OAAOkC,4BAA4B,CAAClC,IAAI,EAAEU,OAAO,EAAEL,EAAE,CAAC;IACxD;EACF,CAAC,CAAC,CAAC,CAAC;;EAEJ,IAAI,CAACO,MAAM,EAAE;IACX,OAAO,CAAC,CAAC;EACX;EAEA,IAAI,CAAC/B,mBAAmB,CAAC+B,MAAM,CAAC,EAAE;IAChC,IAAI9B,wBAAwB,CAAC8B,MAAM,CAAC,EAAE;MACpC,OAAO;QACLE,KAAK,EAAE;MACT,CAAC;IACH;IAEA,OAAO,CAAC,CAAC;EACX,CAAC,CAAC;EACF;;EAGA,IAAI2B,qBAAqB,GAAG1D,gBAAgB,CAAC6B,MAAM,CAAC;EAEpD,IAAI6B,qBAAqB,CAAC5B,GAAG,EAAE;IAC7B,OAAO4B,qBAAqB;EAC9B;EAEA,OAAO;IACL7B,MAAM,EAAEA;EACV,CAAC;AACH;AACA;AACA;AACA;;AAGA,SAASkB,MAAMA,CAACZ,OAAO,EAAEC,cAAc,EAAEN,GAAG,EAAE;EAC5C,IAAIiB,MAAM,GAAG;IACXZ,OAAO,EAAEA,OAAO;IAChBe,KAAK,EAAEd;EACT,CAAC;EAED,IAAIN,GAAG,EAAE;IACPiB,MAAM,CAACjB,GAAG,GAAGA,GAAG;EAClB;EAEA,OAAOiB,MAAM;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,SAASd,gBAAgBA,CAACL,oBAAoB,EAAER,cAAc,EAAEc,kBAAkB,EAAEf,QAAQ,EAAE;EAC5F;EACA,IAAIwC,qBAAqB,GAAGrD,yBAAyB,CAACL,0BAA0B,CAAC2B,oBAAoB,CAAC,EAAER,cAAc,EAAEc,kBAAkB,EAAEf,QAAQ,CAACA,QAAQ,CAAC;IAC1JmB,wBAAwB,GAAGqB,qBAAqB,CAACrB,wBAAwB;IACzED,kBAAkB,GAAGsB,qBAAqB,CAACtB,kBAAkB;IAC7DR,MAAM,GAAG8B,qBAAqB,CAAC9B,MAAM,CAAC,CAAC;;EAG3C,IAAIM,OAAO;EAEX,IAAIE,kBAAkB,EAAE;IACtBlB,QAAQ,CAACyC,mBAAmB,CAACvB,kBAAkB,CAAC;EAClD,CAAC,CAAC;EACF;EAAA,KACK,IAAIR,MAAM,KAAKT,cAAc,IAAIc,kBAAkB,CAAC,EAAE;IACzDf,QAAQ,CAACyC,mBAAmB,CAACxC,cAAc,EAAEc,kBAAkB,CAAC;IAEhE,IAAId,cAAc,EAAE;MAClBe,OAAO,GAAGf,cAAc;IAC1B,CAAC,MAAM;MACL;MACA,IAAIL,+BAA+B,EAAE;QACnC,IAAII,QAAQ,CAAC0C,0BAA0B,CAAC3B,kBAAkB,CAAC,EAAE;UAC3DC,OAAO,GAAG,KAAK;QACjB;MACF;IACF;IAEAE,kBAAkB,GAAGH,kBAAkB,IAAIhC,qBAAqB,CAACkB,cAAc,EAAED,QAAQ,CAACA,QAAQ,CAAC;EACrG,CAAC,MAAM,OAAO,CAAC,CAAC;EAEhB,IAAI,CAACU,MAAM,EAAE;IACX,OAAO;MACLS,wBAAwB,EAAEA,wBAAwB;MAClDD,kBAAkB,EAAEA;IACtB,CAAC;EACH;EAEA,IAAIyB,qBAAqB,GAAGvD,qBAAqB,CAACN,0BAA0B,CAAC4B,MAAM,CAAC,EAAEV,QAAQ,CAAC;IAC3FiB,cAAc,GAAG0B,qBAAqB,CAAC1B,cAAc;IACrDG,WAAW,GAAGuB,qBAAqB,CAACvB,WAAW,CAAC,CAAC;EACrD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAGA,IAAIwB,YAAY,GAAGtD,uBAAuB,CAAC4B,kBAAkB,EAAE;IAC7DD,cAAc,EAAEA,cAAc;IAC9BhB,cAAc,EAAEA,cAAc;IAC9BD,QAAQ,EAAEA;EACZ,CAAC,CAAC;EAEF,IAAI4C,YAAY,EAAE;IAChB5B,OAAO,GAAG4B,YAAY;IACtB;;IAEA,IAAIA,YAAY,KAAK,KAAK,EAAE,CAAC;MAC3B;MACA;IAAA,CACD,MAAM;MACL5C,QAAQ,CAACgB,OAAO,CAACA,OAAO,CAAC;IAC3B;EACF;EAEA,OAAO;IACLA,OAAO,EAAEA,OAAO;IAChBE,kBAAkB,EAAEA,kBAAkB;IACtCC,wBAAwB,EAAEA,wBAAwB;IAClDF,cAAc,EAAEA,cAAc;IAC9BG,WAAW,EAAEA;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}