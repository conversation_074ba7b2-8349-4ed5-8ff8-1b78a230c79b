{"ast": null, "code": "import withMetadataArgument from '../min/exports/withMetadataArgument.js';\nimport _parse from '../es6/legacy/parse.js';\nexport function parse() {\n  return withMetadataArgument(_parse, arguments);\n}", "map": {"version": 3, "names": ["withMetadataArgument", "_parse", "parse", "arguments"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/index.es6.exports/parse.js"], "sourcesContent": ["import withMetadataArgument from '../min/exports/withMetadataArgument.js'\r\n\r\nimport _parse from '../es6/legacy/parse.js'\r\n\r\nexport function parse() {\r\n\treturn withMetadataArgument(_parse, arguments)\r\n}\r\n"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,wCAAwC;AAEzE,OAAOC,MAAM,MAAM,wBAAwB;AAE3C,OAAO,SAASC,KAAKA,CAAA,EAAG;EACvB,OAAOF,oBAAoB,CAACC,MAAM,EAAEE,SAAS,CAAC;AAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}