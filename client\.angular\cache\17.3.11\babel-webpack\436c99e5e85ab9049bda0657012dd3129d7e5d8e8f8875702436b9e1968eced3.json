{"ast": null, "code": "import Metadata from '../metadata.js';\n/**\r\n * Returns a list of countries that the phone number could potentially belong to.\r\n * @param  {string} callingCode — Calling code.\r\n * @param  {string} nationalNumber — National (significant) number.\r\n * @param  {object} metadata — Metadata.\r\n * @return {string[]} A list of possible countries.\r\n */\n\nexport default function getPossibleCountriesForNumber(callingCode, nationalNumber, metadata) {\n  var _metadata = new Metadata(metadata);\n  var possibleCountries = _metadata.getCountryCodesForCallingCode(callingCode);\n  if (!possibleCountries) {\n    return [];\n  }\n  return possibleCountries.filter(function (country) {\n    return couldNationalNumberBelongToCountry(nationalNumber, country, metadata);\n  });\n}\nfunction couldNationalNumberBelongToCountry(nationalNumber, country, metadata) {\n  var _metadata = new Metadata(metadata);\n  _metadata.selectNumberingPlan(country);\n  if (_metadata.numberingPlan.possibleLengths().indexOf(nationalNumber.length) >= 0) {\n    return true;\n  }\n  return false;\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "getPossibleCountriesForNumber", "callingCode", "nationalNumber", "metadata", "_metadata", "possibleCountries", "getCountryCodesForCallingCode", "filter", "country", "couldNationalNumberBelongToCountry", "selectNumberingPlan", "numberingPlan", "possibleLengths", "indexOf", "length"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/helpers/getPossibleCountriesForNumber.js"], "sourcesContent": ["import Metadata from '../metadata.js';\n/**\r\n * Returns a list of countries that the phone number could potentially belong to.\r\n * @param  {string} callingCode — Calling code.\r\n * @param  {string} nationalNumber — National (significant) number.\r\n * @param  {object} metadata — Metadata.\r\n * @return {string[]} A list of possible countries.\r\n */\n\nexport default function getPossibleCountriesForNumber(callingCode, nationalNumber, metadata) {\n  var _metadata = new Metadata(metadata);\n\n  var possibleCountries = _metadata.getCountryCodesForCallingCode(callingCode);\n\n  if (!possibleCountries) {\n    return [];\n  }\n\n  return possibleCountries.filter(function (country) {\n    return couldNationalNumberBelongToCountry(nationalNumber, country, metadata);\n  });\n}\n\nfunction couldNationalNumberBelongToCountry(nationalNumber, country, metadata) {\n  var _metadata = new Metadata(metadata);\n\n  _metadata.selectNumberingPlan(country);\n\n  if (_metadata.numberingPlan.possibleLengths().indexOf(nationalNumber.length) >= 0) {\n    return true;\n  }\n\n  return false;\n}\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,6BAA6BA,CAACC,WAAW,EAAEC,cAAc,EAAEC,QAAQ,EAAE;EAC3F,IAAIC,SAAS,GAAG,IAAIL,QAAQ,CAACI,QAAQ,CAAC;EAEtC,IAAIE,iBAAiB,GAAGD,SAAS,CAACE,6BAA6B,CAACL,WAAW,CAAC;EAE5E,IAAI,CAACI,iBAAiB,EAAE;IACtB,OAAO,EAAE;EACX;EAEA,OAAOA,iBAAiB,CAACE,MAAM,CAAC,UAAUC,OAAO,EAAE;IACjD,OAAOC,kCAAkC,CAACP,cAAc,EAAEM,OAAO,EAAEL,QAAQ,CAAC;EAC9E,CAAC,CAAC;AACJ;AAEA,SAASM,kCAAkCA,CAACP,cAAc,EAAEM,OAAO,EAAEL,QAAQ,EAAE;EAC7E,IAAIC,SAAS,GAAG,IAAIL,QAAQ,CAACI,QAAQ,CAAC;EAEtCC,SAAS,CAACM,mBAAmB,CAACF,OAAO,CAAC;EAEtC,IAAIJ,SAAS,CAACO,aAAa,CAACC,eAAe,CAAC,CAAC,CAACC,OAAO,CAACX,cAAc,CAACY,MAAM,CAAC,IAAI,CAAC,EAAE;IACjF,OAAO,IAAI;EACb;EAEA,OAAO,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}