{"ast": null, "code": "import withMetadataArgument from './withMetadataArgument.js';\nimport { isSupportedCountry as _isSupportedCountry } from '../../core/index.js';\nexport function isSupportedCountry() {\n  return withMetadataArgument(_isSupportedCountry, arguments);\n}", "map": {"version": 3, "names": ["withMetadataArgument", "isSupportedCountry", "_isSupportedCountry", "arguments"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/min/exports/isSupportedCountry.js"], "sourcesContent": ["import withMetadataArgument from './withMetadataArgument.js'\r\nimport { isSupportedCountry as _isSupportedCountry } from '../../core/index.js'\r\n\r\nexport function isSupportedCountry() {\r\n\treturn withMetadataArgument(_isSupportedCountry, arguments)\r\n}"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,2BAA2B;AAC5D,SAASC,kBAAkB,IAAIC,mBAAmB,QAAQ,qBAAqB;AAE/E,OAAO,SAASD,kBAAkBA,CAAA,EAAG;EACpC,OAAOD,oBAAoB,CAACE,mBAAmB,EAAEC,SAAS,CAAC;AAC5D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}