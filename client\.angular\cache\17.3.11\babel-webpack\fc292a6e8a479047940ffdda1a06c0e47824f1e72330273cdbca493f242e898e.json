{"ast": null, "code": "function _createForOfIteratorHelperLoose(o, allowArrayLike) {\n  var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"];\n  if (it) return (it = it.call(o)).next.bind(it);\n  if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") {\n    if (it) o = it;\n    var i = 0;\n    return function () {\n      if (i >= o.length) return {\n        done: true\n      };\n      return {\n        done: false,\n        value: o[i++]\n      };\n    };\n  }\n  throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n  return arr2;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nimport { DIGIT_PLACEHOLDER, countOccurences, repeat, cutAndStripNonPairedParens, closeNonPairedParens, stripNonPairedParens, populateTemplateWithDigits } from './AsYouTypeFormatter.util.js';\nimport formatCompleteNumber, { canFormatCompleteNumber } from './AsYouTypeFormatter.complete.js';\nimport PatternMatcher from './AsYouTypeFormatter.PatternMatcher.js';\nimport parseDigits from './helpers/parseDigits.js';\nexport { DIGIT_PLACEHOLDER } from './AsYouTypeFormatter.util.js';\nimport { FIRST_GROUP_PATTERN } from './helpers/formatNationalNumberUsingFormat.js';\nimport { VALID_PUNCTUATION } from './constants.js';\nimport applyInternationalSeparatorStyle from './helpers/applyInternationalSeparatorStyle.js'; // Used in phone number format template creation.\n// Could be any digit, I guess.\n\nvar DUMMY_DIGIT = '9'; // I don't know why is it exactly `15`\n\nvar LONGEST_NATIONAL_PHONE_NUMBER_LENGTH = 15; // Create a phone number consisting only of the digit 9 that matches the\n// `number_pattern` by applying the pattern to the \"longest phone number\" string.\n\nvar LONGEST_DUMMY_PHONE_NUMBER = repeat(DUMMY_DIGIT, LONGEST_NATIONAL_PHONE_NUMBER_LENGTH); // A set of characters that, if found in a national prefix formatting rules, are an indicator to\n// us that we should separate the national prefix from the number when formatting.\n\nvar NATIONAL_PREFIX_SEPARATORS_PATTERN = /[- ]/; // Deprecated: Google has removed some formatting pattern related code from their repo.\n// https://github.com/googlei18n/libphonenumber/commit/a395b4fef3caf57c4bc5f082e1152a4d2bd0ba4c\n// \"We no longer have numbers in formatting matching patterns, only \\d.\"\n// Because this library supports generating custom metadata\n// some users may still be using old metadata so the relevant\n// code seems to stay until some next major version update.\n\nvar SUPPORT_LEGACY_FORMATTING_PATTERNS = true; // A pattern that is used to match character classes in regular expressions.\n// An example of a character class is \"[1-4]\".\n\nvar CREATE_CHARACTER_CLASS_PATTERN = SUPPORT_LEGACY_FORMATTING_PATTERNS && function () {\n  return /\\[([^\\[\\]])*\\]/g;\n}; // Any digit in a regular expression that actually denotes a digit. For\n// example, in the regular expression \"80[0-2]\\d{6,10}\", the first 2 digits\n// (8 and 0) are standalone digits, but the rest are not.\n// Two look-aheads are needed because the number following \\\\d could be a\n// two-digit number, since the phone number can be as long as 15 digits.\n\nvar CREATE_STANDALONE_DIGIT_PATTERN = SUPPORT_LEGACY_FORMATTING_PATTERNS && function () {\n  return /\\d(?=[^,}][^,}])/g;\n}; // A regular expression that is used to determine if a `format` is\n// suitable to be used in the \"as you type formatter\".\n// A `format` is suitable when the resulting formatted number has\n// the same digits as the user has entered.\n//\n// In the simplest case, that would mean that the format\n// doesn't add any additional digits when formatting a number.\n// Google says that it also shouldn't add \"star\" (`*`) characters,\n// like it does in some Israeli formats.\n// Such basic format would only contain \"valid punctuation\"\n// and \"captured group\" identifiers ($1, $2, etc).\n//\n// An example of a format that adds additional digits:\n//\n// Country: `AR` (Argentina).\n// Format:\n// {\n//    \"pattern\": \"(\\\\d)(\\\\d{2})(\\\\d{4})(\\\\d{4})\",\n//    \"leading_digits_patterns\": [\"91\"],\n//    \"national_prefix_formatting_rule\": \"0$1\",\n//    \"format\": \"$2 15-$3-$4\",\n//    \"international_format\": \"$1 $2 $3-$4\"\n// }\n//\n// In the format above, the `format` adds `15` to the digits when formatting a number.\n// A sidenote: this format actually is suitable because `national_prefix_for_parsing`\n// has previously removed `15` from a national number, so re-adding `15` in `format`\n// doesn't actually result in any extra digits added to user's input.\n// But verifying that would be a complex procedure, so the code chooses a simpler path:\n// it simply filters out all `format`s that contain anything but \"captured group\" ids.\n//\n// This regular expression is called `ELIGIBLE_FORMAT_PATTERN` in Google's\n// `libphonenumber` code.\n//\n\nvar NON_ALTERING_FORMAT_REG_EXP = new RegExp('[' + VALID_PUNCTUATION + ']*' +\n// Google developers say:\n// \"We require that the first matching group is present in the\n//  output pattern to ensure no data is lost while formatting.\"\n'\\\\$1' + '[' + VALID_PUNCTUATION + ']*' + '(\\\\$\\\\d[' + VALID_PUNCTUATION + ']*)*' + '$'); // This is the minimum length of the leading digits of a phone number\n// to guarantee the first \"leading digits pattern\" for a phone number format\n// to be preemptive.\n\nvar MIN_LEADING_DIGITS_LENGTH = 3;\nvar AsYouTypeFormatter = /*#__PURE__*/function () {\n  function AsYouTypeFormatter(_ref) {\n    var state = _ref.state,\n      metadata = _ref.metadata;\n    _classCallCheck(this, AsYouTypeFormatter);\n    this.metadata = metadata;\n    this.resetFormat();\n  }\n  _createClass(AsYouTypeFormatter, [{\n    key: \"resetFormat\",\n    value: function resetFormat() {\n      this.chosenFormat = undefined;\n      this.template = undefined;\n      this.nationalNumberTemplate = undefined;\n      this.populatedNationalNumberTemplate = undefined;\n      this.populatedNationalNumberTemplatePosition = -1;\n    }\n  }, {\n    key: \"reset\",\n    value: function reset(numberingPlan, state) {\n      this.resetFormat();\n      if (numberingPlan) {\n        this.isNANP = numberingPlan.callingCode() === '1';\n        this.matchingFormats = numberingPlan.formats();\n        if (state.nationalSignificantNumber) {\n          this.narrowDownMatchingFormats(state);\n        }\n      } else {\n        this.isNANP = undefined;\n        this.matchingFormats = [];\n      }\n    }\n    /**\r\n     * Formats an updated phone number.\r\n     * @param  {string} nextDigits — Additional phone number digits.\r\n     * @param  {object} state — `AsYouType` state.\r\n     * @return {[string]} Returns undefined if the updated phone number can't be formatted using any of the available formats.\r\n     */\n  }, {\n    key: \"format\",\n    value: function format(nextDigits, state) {\n      var _this = this;\n\n      // See if the phone number digits can be formatted as a complete phone number.\n      // If not, use the results from `formatNationalNumberWithNextDigits()`,\n      // which formats based on the chosen formatting pattern.\n      //\n      // Attempting to format complete phone number first is how it's done\n      // in Google's `libphonenumber`, so this library just follows it.\n      // Google's `libphonenumber` code doesn't explain in detail why does it\n      // attempt to format digits as a complete phone number\n      // instead of just going with a previoulsy (or newly) chosen `format`:\n      //\n      // \"Checks to see if there is an exact pattern match for these digits.\n      //  If so, we should use this instead of any other formatting template\n      //  whose leadingDigitsPattern also matches the input.\"\n      //\n      if (canFormatCompleteNumber(state.nationalSignificantNumber, this.metadata)) {\n        for (var _iterator = _createForOfIteratorHelperLoose(this.matchingFormats), _step; !(_step = _iterator()).done;) {\n          var format = _step.value;\n          var formattedCompleteNumber = formatCompleteNumber(state, format, {\n            metadata: this.metadata,\n            shouldTryNationalPrefixFormattingRule: function shouldTryNationalPrefixFormattingRule(format) {\n              return _this.shouldTryNationalPrefixFormattingRule(format, {\n                international: state.international,\n                nationalPrefix: state.nationalPrefix\n              });\n            },\n            getSeparatorAfterNationalPrefix: function getSeparatorAfterNationalPrefix(format) {\n              return _this.getSeparatorAfterNationalPrefix(format);\n            }\n          });\n          if (formattedCompleteNumber) {\n            this.resetFormat();\n            this.chosenFormat = format;\n            this.setNationalNumberTemplate(formattedCompleteNumber.replace(/\\d/g, DIGIT_PLACEHOLDER), state);\n            this.populatedNationalNumberTemplate = formattedCompleteNumber; // With a new formatting template, the matched position\n            // using the old template needs to be reset.\n\n            this.populatedNationalNumberTemplatePosition = this.template.lastIndexOf(DIGIT_PLACEHOLDER);\n            return formattedCompleteNumber;\n          }\n        }\n      } // Format the digits as a partial (incomplete) phone number\n      // using the previously chosen formatting pattern (or a newly chosen one).\n\n      return this.formatNationalNumberWithNextDigits(nextDigits, state);\n    } // Formats the next phone number digits.\n  }, {\n    key: \"formatNationalNumberWithNextDigits\",\n    value: function formatNationalNumberWithNextDigits(nextDigits, state) {\n      var previouslyChosenFormat = this.chosenFormat; // Choose a format from the list of matching ones.\n\n      var newlyChosenFormat = this.chooseFormat(state);\n      if (newlyChosenFormat) {\n        if (newlyChosenFormat === previouslyChosenFormat) {\n          // If it can format the next (current) digits\n          // using the previously chosen phone number format\n          // then return the updated formatted number.\n          return this.formatNextNationalNumberDigits(nextDigits);\n        } else {\n          // If a more appropriate phone number format\n          // has been chosen for these \"leading digits\",\n          // then re-format the national phone number part\n          // using the newly selected format.\n          return this.formatNextNationalNumberDigits(state.getNationalDigits());\n        }\n      }\n    }\n  }, {\n    key: \"narrowDownMatchingFormats\",\n    value: function narrowDownMatchingFormats(_ref2) {\n      var _this2 = this;\n      var nationalSignificantNumber = _ref2.nationalSignificantNumber,\n        nationalPrefix = _ref2.nationalPrefix,\n        international = _ref2.international;\n      var leadingDigits = nationalSignificantNumber; // \"leading digits\" pattern list starts with a\n      // \"leading digits\" pattern fitting a maximum of 3 leading digits.\n      // So, after a user inputs 3 digits of a national (significant) phone number\n      // this national (significant) number can already be formatted.\n      // The next \"leading digits\" pattern is for 4 leading digits max,\n      // and the \"leading digits\" pattern after it is for 5 leading digits max, etc.\n      // This implementation is different from Google's\n      // in that it searches for a fitting format\n      // even if the user has entered less than\n      // `MIN_LEADING_DIGITS_LENGTH` digits of a national number.\n      // Because some leading digit patterns already match for a single first digit.\n\n      var leadingDigitsPatternIndex = leadingDigits.length - MIN_LEADING_DIGITS_LENGTH;\n      if (leadingDigitsPatternIndex < 0) {\n        leadingDigitsPatternIndex = 0;\n      }\n      this.matchingFormats = this.matchingFormats.filter(function (format) {\n        return _this2.formatSuits(format, international, nationalPrefix) && _this2.formatMatches(format, leadingDigits, leadingDigitsPatternIndex);\n      }); // If there was a phone number format chosen\n      // and it no longer holds given the new leading digits then reset it.\n      // The test for this `if` condition is marked as:\n      // \"Reset a chosen format when it no longer holds given the new leading digits\".\n      // To construct a valid test case for this one can find a country\n      // in `PhoneNumberMetadata.xml` yielding one format for 3 `<leadingDigits>`\n      // and yielding another format for 4 `<leadingDigits>` (Australia in this case).\n\n      if (this.chosenFormat && this.matchingFormats.indexOf(this.chosenFormat) === -1) {\n        this.resetFormat();\n      }\n    }\n  }, {\n    key: \"formatSuits\",\n    value: function formatSuits(format, international, nationalPrefix) {\n      // When a prefix before a national (significant) number is\n      // simply a national prefix, then it's parsed as `this.nationalPrefix`.\n      // In more complex cases, a prefix before national (significant) number\n      // could include a national prefix as well as some \"capturing groups\",\n      // and in that case there's no info whether a national prefix has been parsed.\n      // If national prefix is not used when formatting a phone number\n      // using this format, but a national prefix has been entered by the user,\n      // and was extracted, then discard such phone number format.\n      // In Google's \"AsYouType\" formatter code, the equivalent would be this part:\n      // https://github.com/google/libphonenumber/blob/0a45cfd96e71cad8edb0e162a70fcc8bd9728933/java/libphonenumber/src/com/google/i18n/phonenumbers/AsYouTypeFormatter.java#L175-L184\n      if (nationalPrefix && !format.usesNationalPrefix() &&\n      // !format.domesticCarrierCodeFormattingRule() &&\n      !format.nationalPrefixIsOptionalWhenFormattingInNationalFormat()) {\n        return false;\n      } // If national prefix is mandatory for this phone number format\n      // and there're no guarantees that a national prefix is present in user input\n      // then discard this phone number format as not suitable.\n      // In Google's \"AsYouType\" formatter code, the equivalent would be this part:\n      // https://github.com/google/libphonenumber/blob/0a45cfd96e71cad8edb0e162a70fcc8bd9728933/java/libphonenumber/src/com/google/i18n/phonenumbers/AsYouTypeFormatter.java#L185-L193\n\n      if (!international && !nationalPrefix && format.nationalPrefixIsMandatoryWhenFormattingInNationalFormat()) {\n        return false;\n      }\n      return true;\n    }\n  }, {\n    key: \"formatMatches\",\n    value: function formatMatches(format, leadingDigits, leadingDigitsPatternIndex) {\n      var leadingDigitsPatternsCount = format.leadingDigitsPatterns().length; // If this format is not restricted to a certain\n      // leading digits pattern then it fits.\n      // The test case could be found by searching for \"leadingDigitsPatternsCount === 0\".\n\n      if (leadingDigitsPatternsCount === 0) {\n        return true;\n      } // Start narrowing down the list of possible formats based on the leading digits.\n      // (only previously matched formats take part in the narrowing down process)\n      // `leading_digits_patterns` start with 3 digits min\n      // and then go up from there one digit at a time.\n\n      leadingDigitsPatternIndex = Math.min(leadingDigitsPatternIndex, leadingDigitsPatternsCount - 1);\n      var leadingDigitsPattern = format.leadingDigitsPatterns()[leadingDigitsPatternIndex]; // Google imposes a requirement on the leading digits\n      // to be minimum 3 digits long in order to be eligible\n      // for checking those with a leading digits pattern.\n      //\n      // Since `leading_digits_patterns` start with 3 digits min,\n      // Google's original `libphonenumber` library only starts\n      // excluding any non-matching formats only when the\n      // national number entered so far is at least 3 digits long,\n      // otherwise format matching would give false negatives.\n      //\n      // For example, when the digits entered so far are `2`\n      // and the leading digits pattern is `21` –\n      // it's quite obvious in this case that the format could be the one\n      // but due to the absence of further digits it would give false negative.\n      //\n      // Also, `leading_digits_patterns` doesn't always correspond to a single\n      // digits count. For example, `60|8` pattern would already match `8`\n      // but the `60` part would require having at least two leading digits,\n      // so the whole pattern would require inputting two digits first in order to\n      // decide on whether it matches the input, even when the input is \"80\".\n      //\n      // This library — `libphonenumber-js` — allows filtering by `leading_digits_patterns`\n      // even when there's only 1 or 2 digits of the national (significant) number.\n      // To do that, it uses a non-strict pattern matcher written specifically for that.\n      //\n\n      if (leadingDigits.length < MIN_LEADING_DIGITS_LENGTH) {\n        // Before leading digits < 3 matching was implemented:\n        // return true\n        //\n        // After leading digits < 3 matching was implemented:\n        try {\n          return new PatternMatcher(leadingDigitsPattern).match(leadingDigits, {\n            allowOverflow: true\n          }) !== undefined;\n        } catch (error) /* istanbul ignore next */\n        {\n          // There's a slight possibility that there could be some undiscovered bug\n          // in the pattern matcher code. Since the \"leading digits < 3 matching\"\n          // feature is not \"essential\" for operation, it can fall back to the old way\n          // in case of any issues rather than halting the application's execution.\n          console.error(error);\n          return true;\n        }\n      } // If at least `MIN_LEADING_DIGITS_LENGTH` digits of a national number are\n      // available then use the usual regular expression matching.\n      //\n      // The whole pattern is wrapped in round brackets (`()`) because\n      // the pattern can use \"or\" operator (`|`) at the top level of the pattern.\n      //\n\n      return new RegExp(\"^(\".concat(leadingDigitsPattern, \")\")).test(leadingDigits);\n    }\n  }, {\n    key: \"getFormatFormat\",\n    value: function getFormatFormat(format, international) {\n      return international ? format.internationalFormat() : format.format();\n    }\n  }, {\n    key: \"chooseFormat\",\n    value: function chooseFormat(state) {\n      var _this3 = this;\n      var _loop = function _loop() {\n        var format = _step2.value;\n\n        // If this format is currently being used\n        // and is still suitable, then stick to it.\n        if (_this3.chosenFormat === format) {\n          return \"break\";\n        } // Sometimes, a formatting rule inserts additional digits in a phone number,\n        // and \"as you type\" formatter can't do that: it should only use the digits\n        // that the user has input.\n        //\n        // For example, in Argentina, there's a format for mobile phone numbers:\n        //\n        // {\n        //    \"pattern\": \"(\\\\d)(\\\\d{2})(\\\\d{4})(\\\\d{4})\",\n        //    \"leading_digits_patterns\": [\"91\"],\n        //    \"national_prefix_formatting_rule\": \"0$1\",\n        //    \"format\": \"$2 15-$3-$4\",\n        //    \"international_format\": \"$1 $2 $3-$4\"\n        // }\n        //\n        // In that format, `international_format` is used instead of `format`\n        // because `format` inserts `15` in the formatted number,\n        // and `AsYouType` formatter should only use the digits\n        // the user has actually input, without adding any extra digits.\n        // In this case, it wouldn't make a difference, because the `15`\n        // is first stripped when applying `national_prefix_for_parsing`\n        // and then re-added when using `format`, so in reality it doesn't\n        // add any new digits to the number, but to detect that, the code\n        // would have to be more complex: it would have to try formatting\n        // the digits using the format and then see if any digits have\n        // actually been added or removed, and then, every time a new digit\n        // is input, it should re-check whether the chosen format doesn't\n        // alter the digits.\n        //\n        // Google's code doesn't go that far, and so does this library:\n        // it simply requires that a `format` doesn't add any additonal\n        // digits to user's input.\n        //\n        // Also, people in general should move from inputting phone numbers\n        // in national format (possibly with national prefixes)\n        // and use international phone number format instead:\n        // it's a logical thing in the modern age of mobile phones,\n        // globalization and the internet.\n        //\n\n        /* istanbul ignore if */\n\n        if (!NON_ALTERING_FORMAT_REG_EXP.test(_this3.getFormatFormat(format, state.international))) {\n          return \"continue\";\n        }\n        if (!_this3.createTemplateForFormat(format, state)) {\n          // Remove the format if it can't generate a template.\n          _this3.matchingFormats = _this3.matchingFormats.filter(function (_) {\n            return _ !== format;\n          });\n          return \"continue\";\n        }\n        _this3.chosenFormat = format;\n        return \"break\";\n      };\n\n      // When there are multiple available formats, the formatter uses the first\n      // format where a formatting template could be created.\n      //\n      // For some weird reason, `istanbul` says \"else path not taken\"\n      // for the `for of` line below. Supposedly that means that\n      // the loop doesn't ever go over the last element in the list.\n      // That's true because there always is `this.chosenFormat`\n      // when `this.matchingFormats` is non-empty.\n      // And, for some weird reason, it doesn't think that the case\n      // with empty `this.matchingFormats` qualifies for a valid \"else\" path.\n      // So simply muting this `istanbul` warning.\n      // It doesn't skip the contents of the `for of` loop,\n      // it just skips the `for of` line.\n      //\n\n      /* istanbul ignore next */\n      for (var _iterator2 = _createForOfIteratorHelperLoose(this.matchingFormats.slice()), _step2; !(_step2 = _iterator2()).done;) {\n        var _ret = _loop();\n        if (_ret === \"break\") break;\n        if (_ret === \"continue\") continue;\n      }\n      if (!this.chosenFormat) {\n        // No format matches the national (significant) phone number.\n        this.resetFormat();\n      }\n      return this.chosenFormat;\n    }\n  }, {\n    key: \"createTemplateForFormat\",\n    value: function createTemplateForFormat(format, state) {\n      // The formatter doesn't format numbers when numberPattern contains '|', e.g.\n      // (20|3)\\d{4}. In those cases we quickly return.\n      // (Though there's no such format in current metadata)\n\n      /* istanbul ignore if */\n      if (SUPPORT_LEGACY_FORMATTING_PATTERNS && format.pattern().indexOf('|') >= 0) {\n        return;\n      } // Get formatting template for this phone number format\n\n      var template = this.getTemplateForFormat(format, state); // If the national number entered is too long\n      // for any phone number format, then abort.\n\n      if (template) {\n        this.setNationalNumberTemplate(template, state);\n        return true;\n      }\n    }\n  }, {\n    key: \"getSeparatorAfterNationalPrefix\",\n    value: function getSeparatorAfterNationalPrefix(format) {\n      // `US` metadata doesn't have a `national_prefix_formatting_rule`,\n      // so the `if` condition below doesn't apply to `US`,\n      // but in reality there shoudl be a separator\n      // between a national prefix and a national (significant) number.\n      // So `US` national prefix separator is a \"special\" \"hardcoded\" case.\n      if (this.isNANP) {\n        return ' ';\n      } // If a `format` has a `national_prefix_formatting_rule`\n      // and that rule has a separator after a national prefix,\n      // then it means that there should be a separator\n      // between a national prefix and a national (significant) number.\n\n      if (format && format.nationalPrefixFormattingRule() && NATIONAL_PREFIX_SEPARATORS_PATTERN.test(format.nationalPrefixFormattingRule())) {\n        return ' ';\n      } // At this point, there seems to be no clear evidence that\n      // there should be a separator between a national prefix\n      // and a national (significant) number. So don't insert one.\n\n      return '';\n    }\n  }, {\n    key: \"getInternationalPrefixBeforeCountryCallingCode\",\n    value: function getInternationalPrefixBeforeCountryCallingCode(_ref3, options) {\n      var IDDPrefix = _ref3.IDDPrefix,\n        missingPlus = _ref3.missingPlus;\n      if (IDDPrefix) {\n        return options && options.spacing === false ? IDDPrefix : IDDPrefix + ' ';\n      }\n      if (missingPlus) {\n        return '';\n      }\n      return '+';\n    }\n  }, {\n    key: \"getTemplate\",\n    value: function getTemplate(state) {\n      if (!this.template) {\n        return;\n      } // `this.template` holds the template for a \"complete\" phone number.\n      // The currently entered phone number is most likely not \"complete\",\n      // so trim all non-populated digits.\n\n      var index = -1;\n      var i = 0;\n      var internationalPrefix = state.international ? this.getInternationalPrefixBeforeCountryCallingCode(state, {\n        spacing: false\n      }) : '';\n      while (i < internationalPrefix.length + state.getDigitsWithoutInternationalPrefix().length) {\n        index = this.template.indexOf(DIGIT_PLACEHOLDER, index + 1);\n        i++;\n      }\n      return cutAndStripNonPairedParens(this.template, index + 1);\n    }\n  }, {\n    key: \"setNationalNumberTemplate\",\n    value: function setNationalNumberTemplate(template, state) {\n      this.nationalNumberTemplate = template;\n      this.populatedNationalNumberTemplate = template; // With a new formatting template, the matched position\n      // using the old template needs to be reset.\n\n      this.populatedNationalNumberTemplatePosition = -1; // For convenience, the public `.template` property\n      // contains the whole international number\n      // if the phone number being input is international:\n      // 'x' for the '+' sign, 'x'es for the country phone code,\n      // a spacebar and then the template for the formatted national number.\n\n      if (state.international) {\n        this.template = this.getInternationalPrefixBeforeCountryCallingCode(state).replace(/[\\d\\+]/g, DIGIT_PLACEHOLDER) + repeat(DIGIT_PLACEHOLDER, state.callingCode.length) + ' ' + template;\n      } else {\n        this.template = template;\n      }\n    }\n    /**\r\n     * Generates formatting template for a national phone number,\r\n     * optionally containing a national prefix, for a format.\r\n     * @param  {Format} format\r\n     * @param  {string} nationalPrefix\r\n     * @return {string}\r\n     */\n  }, {\n    key: \"getTemplateForFormat\",\n    value: function getTemplateForFormat(format, _ref4) {\n      var nationalSignificantNumber = _ref4.nationalSignificantNumber,\n        international = _ref4.international,\n        nationalPrefix = _ref4.nationalPrefix,\n        complexPrefixBeforeNationalSignificantNumber = _ref4.complexPrefixBeforeNationalSignificantNumber;\n      var pattern = format.pattern();\n      /* istanbul ignore else */\n\n      if (SUPPORT_LEGACY_FORMATTING_PATTERNS) {\n        pattern = pattern // Replace anything in the form of [..] with \\d\n        .replace(CREATE_CHARACTER_CLASS_PATTERN(), '\\\\d') // Replace any standalone digit (not the one in `{}`) with \\d\n        .replace(CREATE_STANDALONE_DIGIT_PATTERN(), '\\\\d');\n      } // Generate a dummy national number (consisting of `9`s)\n      // that fits this format's `pattern`.\n      //\n      // This match will always succeed,\n      // because the \"longest dummy phone number\"\n      // has enough length to accomodate any possible\n      // national phone number format pattern.\n      //\n\n      var digits = LONGEST_DUMMY_PHONE_NUMBER.match(pattern)[0]; // If the national number entered is too long\n      // for any phone number format, then abort.\n\n      if (nationalSignificantNumber.length > digits.length) {\n        return;\n      } // Get a formatting template which can be used to efficiently format\n      // a partial number where digits are added one by one.\n      // Below `strictPattern` is used for the\n      // regular expression (with `^` and `$`).\n      // This wasn't originally in Google's `libphonenumber`\n      // and I guess they don't really need it\n      // because they're not using \"templates\" to format phone numbers\n      // but I added `strictPattern` after encountering\n      // South Korean phone number formatting bug.\n      //\n      // Non-strict regular expression bug demonstration:\n      //\n      // this.nationalSignificantNumber : `111111111` (9 digits)\n      //\n      // pattern : (\\d{2})(\\d{3,4})(\\d{4})\n      // format : `$1 $2 $3`\n      // digits : `9999999999` (10 digits)\n      //\n      // '9999999999'.replace(new RegExp(/(\\d{2})(\\d{3,4})(\\d{4})/g), '$1 $2 $3') = \"99 9999 9999\"\n      //\n      // template : xx xxxx xxxx\n      //\n      // But the correct template in this case is `xx xxx xxxx`.\n      // The template was generated incorrectly because of the\n      // `{3,4}` variability in the `pattern`.\n      //\n      // The fix is, if `this.nationalSignificantNumber` has already sufficient length\n      // to satisfy the `pattern` completely then `this.nationalSignificantNumber`\n      // is used instead of `digits`.\n\n      var strictPattern = new RegExp('^' + pattern + '$');\n      var nationalNumberDummyDigits = nationalSignificantNumber.replace(/\\d/g, DUMMY_DIGIT); // If `this.nationalSignificantNumber` has already sufficient length\n      // to satisfy the `pattern` completely then use it\n      // instead of `digits`.\n\n      if (strictPattern.test(nationalNumberDummyDigits)) {\n        digits = nationalNumberDummyDigits;\n      }\n      var numberFormat = this.getFormatFormat(format, international);\n      var nationalPrefixIncludedInTemplate; // If a user did input a national prefix (and that's guaranteed),\n      // and if a `format` does have a national prefix formatting rule,\n      // then see if that national prefix formatting rule\n      // prepends exactly the same national prefix the user has input.\n      // If that's the case, then use the `format` with the national prefix formatting rule.\n      // Otherwise, use  the `format` without the national prefix formatting rule,\n      // and prepend a national prefix manually to it.\n\n      if (this.shouldTryNationalPrefixFormattingRule(format, {\n        international: international,\n        nationalPrefix: nationalPrefix\n      })) {\n        var numberFormatWithNationalPrefix = numberFormat.replace(FIRST_GROUP_PATTERN, format.nationalPrefixFormattingRule()); // If `national_prefix_formatting_rule` of a `format` simply prepends\n        // national prefix at the start of a national (significant) number,\n        // then such formatting can be used with `AsYouType` formatter.\n        // There seems to be no `else` case: everywhere in metadata,\n        // national prefix formatting rule is national prefix + $1,\n        // or `($1)`, in which case such format isn't even considered\n        // when the user has input a national prefix.\n\n        /* istanbul ignore else */\n\n        if (parseDigits(format.nationalPrefixFormattingRule()) === (nationalPrefix || '') + parseDigits('$1')) {\n          numberFormat = numberFormatWithNationalPrefix;\n          nationalPrefixIncludedInTemplate = true; // Replace all digits of the national prefix in the formatting template\n          // with `DIGIT_PLACEHOLDER`s.\n\n          if (nationalPrefix) {\n            var i = nationalPrefix.length;\n            while (i > 0) {\n              numberFormat = numberFormat.replace(/\\d/, DIGIT_PLACEHOLDER);\n              i--;\n            }\n          }\n        }\n      } // Generate formatting template for this phone number format.\n\n      var template = digits // Format the dummy phone number according to the format.\n      .replace(new RegExp(pattern), numberFormat) // Replace each dummy digit with a DIGIT_PLACEHOLDER.\n      .replace(new RegExp(DUMMY_DIGIT, 'g'), DIGIT_PLACEHOLDER); // If a prefix of a national (significant) number is not as simple\n      // as just a basic national prefix, then just prepend such prefix\n      // before the national (significant) number, optionally spacing\n      // the two with a whitespace.\n\n      if (!nationalPrefixIncludedInTemplate) {\n        if (complexPrefixBeforeNationalSignificantNumber) {\n          // Prepend the prefix to the template manually.\n          template = repeat(DIGIT_PLACEHOLDER, complexPrefixBeforeNationalSignificantNumber.length) + ' ' + template;\n        } else if (nationalPrefix) {\n          // Prepend national prefix to the template manually.\n          template = repeat(DIGIT_PLACEHOLDER, nationalPrefix.length) + this.getSeparatorAfterNationalPrefix(format) + template;\n        }\n      }\n      if (international) {\n        template = applyInternationalSeparatorStyle(template);\n      }\n      return template;\n    }\n  }, {\n    key: \"formatNextNationalNumberDigits\",\n    value: function formatNextNationalNumberDigits(digits) {\n      var result = populateTemplateWithDigits(this.populatedNationalNumberTemplate, this.populatedNationalNumberTemplatePosition, digits);\n      if (!result) {\n        // Reset the format.\n        this.resetFormat();\n        return;\n      }\n      this.populatedNationalNumberTemplate = result[0];\n      this.populatedNationalNumberTemplatePosition = result[1]; // Return the formatted phone number so far.\n\n      return cutAndStripNonPairedParens(this.populatedNationalNumberTemplate, this.populatedNationalNumberTemplatePosition + 1); // The old way which was good for `input-format` but is not so good\n      // for `react-phone-number-input`'s default input (`InputBasic`).\n      // return closeNonPairedParens(this.populatedNationalNumberTemplate, this.populatedNationalNumberTemplatePosition + 1)\n      // \t.replace(new RegExp(DIGIT_PLACEHOLDER, 'g'), ' ')\n    }\n  }, {\n    key: \"shouldTryNationalPrefixFormattingRule\",\n    value: function shouldTryNationalPrefixFormattingRule(format, _ref5) {\n      var international = _ref5.international,\n        nationalPrefix = _ref5.nationalPrefix;\n      if (format.nationalPrefixFormattingRule()) {\n        // In some countries, `national_prefix_formatting_rule` is `($1)`,\n        // so it applies even if the user hasn't input a national prefix.\n        // `format.usesNationalPrefix()` detects such cases.\n        var usesNationalPrefix = format.usesNationalPrefix();\n        if (usesNationalPrefix && nationalPrefix || !usesNationalPrefix && !international) {\n          return true;\n        }\n      }\n    }\n  }]);\n  return AsYouTypeFormatter;\n}();\nexport { AsYouTypeFormatter as default };", "map": {"version": 3, "names": ["_createForOfIteratorHelperLoose", "o", "allowArrayLike", "it", "Symbol", "iterator", "call", "next", "bind", "Array", "isArray", "_unsupportedIterableToArray", "length", "i", "done", "value", "TypeError", "minLen", "_arrayLikeToArray", "n", "Object", "prototype", "toString", "slice", "constructor", "name", "from", "test", "arr", "len", "arr2", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "_defineProperties", "target", "props", "descriptor", "enumerable", "configurable", "writable", "defineProperty", "key", "_createClass", "protoProps", "staticProps", "DIGIT_PLACEHOLDER", "countOccurences", "repeat", "cutAndStripNonPairedParens", "closeNonPairedParens", "stripNonPairedParens", "populateTemplateWithDigits", "formatCompleteNumber", "canFormatCompleteNumber", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseDigits", "FIRST_GROUP_PATTERN", "VALID_PUNCTUATION", "applyInternationalSeparatorStyle", "DUMMY_DIGIT", "LONGEST_NATIONAL_PHONE_NUMBER_LENGTH", "LONGEST_DUMMY_PHONE_NUMBER", "NATIONAL_PREFIX_SEPARATORS_PATTERN", "SUPPORT_LEGACY_FORMATTING_PATTERNS", "CREATE_CHARACTER_CLASS_PATTERN", "CREATE_STANDALONE_DIGIT_PATTERN", "NON_ALTERING_FORMAT_REG_EXP", "RegExp", "MIN_LEADING_DIGITS_LENGTH", "AsYouTypeFormatter", "_ref", "state", "metadata", "resetFormat", "chosenFormat", "undefined", "template", "nationalNumberTemplate", "populatedNationalNumberTemplate", "populatedNationalNumberTemplatePosition", "reset", "numberingPlan", "isNANP", "callingCode", "matchingFormats", "formats", "nationalSignificantNumber", "narrowDownMatchingFormats", "format", "nextDigits", "_this", "_iterator", "_step", "formattedCompleteNumber", "shouldTryNationalPrefixFormattingRule", "international", "nationalPrefix", "getSeparatorAfterNationalPrefix", "setNationalNumberTemplate", "replace", "lastIndexOf", "formatNationalNumberWithNextDigits", "previouslyChosenFormat", "newlyChosenFormat", "chooseFormat", "formatNextNationalNumberDigits", "getNationalDigits", "_ref2", "_this2", "leadingDigits", "leadingDigitsPatternIndex", "filter", "formatSuits", "formatMatches", "indexOf", "usesNationalPrefix", "nationalPrefixIsOptionalWhenFormattingInNationalFormat", "nationalPrefixIsMandatoryWhenFormattingInNationalFormat", "leadingDigitsPatternsCount", "leadingDigitsPatterns", "Math", "min", "leadingDigitsPattern", "match", "allowOverflow", "error", "console", "concat", "getFormatFormat", "internationalFormat", "_this3", "_loop", "_step2", "createTemplateForFormat", "_", "_iterator2", "_ret", "pattern", "getTemplateForFormat", "nationalPrefixFormattingRule", "getInternationalPrefixBeforeCountryCallingCode", "_ref3", "options", "IDDPrefix", "missingPlus", "spacing", "getTemplate", "index", "internationalPrefix", "getDigitsWithoutInternationalPrefix", "_ref4", "complexPrefixBeforeNationalSignificantNumber", "digits", "strictPattern", "nationalNumberDummyDigits", "numberFormat", "nationalPrefixIncludedInTemplate", "numberFormatWithNationalPrefix", "result", "_ref5", "default"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/AsYouTypeFormatter.js"], "sourcesContent": ["function _createForOfIteratorHelperLoose(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (it) return (it = it.call(o)).next.bind(it); if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; return function () { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\n\nimport { DIGIT_PLACEHOLDER, countOccurences, repeat, cutAndStripNonPairedParens, closeNonPairedParens, stripNonPairedParens, populateTemplateWithDigits } from './AsYouTypeFormatter.util.js';\nimport formatCompleteNumber, { canFormatCompleteNumber } from './AsYouTypeFormatter.complete.js';\nimport PatternMatcher from './AsYouTypeFormatter.PatternMatcher.js';\nimport parseDigits from './helpers/parseDigits.js';\nexport { DIGIT_PLACEHOLDER } from './AsYouTypeFormatter.util.js';\nimport { FIRST_GROUP_PATTERN } from './helpers/formatNationalNumberUsingFormat.js';\nimport { VALID_PUNCTUATION } from './constants.js';\nimport applyInternationalSeparatorStyle from './helpers/applyInternationalSeparatorStyle.js'; // Used in phone number format template creation.\n// Could be any digit, I guess.\n\nvar DUMMY_DIGIT = '9'; // I don't know why is it exactly `15`\n\nvar LONGEST_NATIONAL_PHONE_NUMBER_LENGTH = 15; // Create a phone number consisting only of the digit 9 that matches the\n// `number_pattern` by applying the pattern to the \"longest phone number\" string.\n\nvar LONGEST_DUMMY_PHONE_NUMBER = repeat(DUMMY_DIGIT, LONGEST_NATIONAL_PHONE_NUMBER_LENGTH); // A set of characters that, if found in a national prefix formatting rules, are an indicator to\n// us that we should separate the national prefix from the number when formatting.\n\nvar NATIONAL_PREFIX_SEPARATORS_PATTERN = /[- ]/; // Deprecated: Google has removed some formatting pattern related code from their repo.\n// https://github.com/googlei18n/libphonenumber/commit/a395b4fef3caf57c4bc5f082e1152a4d2bd0ba4c\n// \"We no longer have numbers in formatting matching patterns, only \\d.\"\n// Because this library supports generating custom metadata\n// some users may still be using old metadata so the relevant\n// code seems to stay until some next major version update.\n\nvar SUPPORT_LEGACY_FORMATTING_PATTERNS = true; // A pattern that is used to match character classes in regular expressions.\n// An example of a character class is \"[1-4]\".\n\nvar CREATE_CHARACTER_CLASS_PATTERN = SUPPORT_LEGACY_FORMATTING_PATTERNS && function () {\n  return /\\[([^\\[\\]])*\\]/g;\n}; // Any digit in a regular expression that actually denotes a digit. For\n// example, in the regular expression \"80[0-2]\\d{6,10}\", the first 2 digits\n// (8 and 0) are standalone digits, but the rest are not.\n// Two look-aheads are needed because the number following \\\\d could be a\n// two-digit number, since the phone number can be as long as 15 digits.\n\n\nvar CREATE_STANDALONE_DIGIT_PATTERN = SUPPORT_LEGACY_FORMATTING_PATTERNS && function () {\n  return /\\d(?=[^,}][^,}])/g;\n}; // A regular expression that is used to determine if a `format` is\n// suitable to be used in the \"as you type formatter\".\n// A `format` is suitable when the resulting formatted number has\n// the same digits as the user has entered.\n//\n// In the simplest case, that would mean that the format\n// doesn't add any additional digits when formatting a number.\n// Google says that it also shouldn't add \"star\" (`*`) characters,\n// like it does in some Israeli formats.\n// Such basic format would only contain \"valid punctuation\"\n// and \"captured group\" identifiers ($1, $2, etc).\n//\n// An example of a format that adds additional digits:\n//\n// Country: `AR` (Argentina).\n// Format:\n// {\n//    \"pattern\": \"(\\\\d)(\\\\d{2})(\\\\d{4})(\\\\d{4})\",\n//    \"leading_digits_patterns\": [\"91\"],\n//    \"national_prefix_formatting_rule\": \"0$1\",\n//    \"format\": \"$2 15-$3-$4\",\n//    \"international_format\": \"$1 $2 $3-$4\"\n// }\n//\n// In the format above, the `format` adds `15` to the digits when formatting a number.\n// A sidenote: this format actually is suitable because `national_prefix_for_parsing`\n// has previously removed `15` from a national number, so re-adding `15` in `format`\n// doesn't actually result in any extra digits added to user's input.\n// But verifying that would be a complex procedure, so the code chooses a simpler path:\n// it simply filters out all `format`s that contain anything but \"captured group\" ids.\n//\n// This regular expression is called `ELIGIBLE_FORMAT_PATTERN` in Google's\n// `libphonenumber` code.\n//\n\n\nvar NON_ALTERING_FORMAT_REG_EXP = new RegExp('[' + VALID_PUNCTUATION + ']*' + // Google developers say:\n// \"We require that the first matching group is present in the\n//  output pattern to ensure no data is lost while formatting.\"\n'\\\\$1' + '[' + VALID_PUNCTUATION + ']*' + '(\\\\$\\\\d[' + VALID_PUNCTUATION + ']*)*' + '$'); // This is the minimum length of the leading digits of a phone number\n// to guarantee the first \"leading digits pattern\" for a phone number format\n// to be preemptive.\n\nvar MIN_LEADING_DIGITS_LENGTH = 3;\n\nvar AsYouTypeFormatter = /*#__PURE__*/function () {\n  function AsYouTypeFormatter(_ref) {\n    var state = _ref.state,\n        metadata = _ref.metadata;\n\n    _classCallCheck(this, AsYouTypeFormatter);\n\n    this.metadata = metadata;\n    this.resetFormat();\n  }\n\n  _createClass(AsYouTypeFormatter, [{\n    key: \"resetFormat\",\n    value: function resetFormat() {\n      this.chosenFormat = undefined;\n      this.template = undefined;\n      this.nationalNumberTemplate = undefined;\n      this.populatedNationalNumberTemplate = undefined;\n      this.populatedNationalNumberTemplatePosition = -1;\n    }\n  }, {\n    key: \"reset\",\n    value: function reset(numberingPlan, state) {\n      this.resetFormat();\n\n      if (numberingPlan) {\n        this.isNANP = numberingPlan.callingCode() === '1';\n        this.matchingFormats = numberingPlan.formats();\n\n        if (state.nationalSignificantNumber) {\n          this.narrowDownMatchingFormats(state);\n        }\n      } else {\n        this.isNANP = undefined;\n        this.matchingFormats = [];\n      }\n    }\n    /**\r\n     * Formats an updated phone number.\r\n     * @param  {string} nextDigits — Additional phone number digits.\r\n     * @param  {object} state — `AsYouType` state.\r\n     * @return {[string]} Returns undefined if the updated phone number can't be formatted using any of the available formats.\r\n     */\n\n  }, {\n    key: \"format\",\n    value: function format(nextDigits, state) {\n      var _this = this;\n\n      // See if the phone number digits can be formatted as a complete phone number.\n      // If not, use the results from `formatNationalNumberWithNextDigits()`,\n      // which formats based on the chosen formatting pattern.\n      //\n      // Attempting to format complete phone number first is how it's done\n      // in Google's `libphonenumber`, so this library just follows it.\n      // Google's `libphonenumber` code doesn't explain in detail why does it\n      // attempt to format digits as a complete phone number\n      // instead of just going with a previoulsy (or newly) chosen `format`:\n      //\n      // \"Checks to see if there is an exact pattern match for these digits.\n      //  If so, we should use this instead of any other formatting template\n      //  whose leadingDigitsPattern also matches the input.\"\n      //\n      if (canFormatCompleteNumber(state.nationalSignificantNumber, this.metadata)) {\n        for (var _iterator = _createForOfIteratorHelperLoose(this.matchingFormats), _step; !(_step = _iterator()).done;) {\n          var format = _step.value;\n          var formattedCompleteNumber = formatCompleteNumber(state, format, {\n            metadata: this.metadata,\n            shouldTryNationalPrefixFormattingRule: function shouldTryNationalPrefixFormattingRule(format) {\n              return _this.shouldTryNationalPrefixFormattingRule(format, {\n                international: state.international,\n                nationalPrefix: state.nationalPrefix\n              });\n            },\n            getSeparatorAfterNationalPrefix: function getSeparatorAfterNationalPrefix(format) {\n              return _this.getSeparatorAfterNationalPrefix(format);\n            }\n          });\n\n          if (formattedCompleteNumber) {\n            this.resetFormat();\n            this.chosenFormat = format;\n            this.setNationalNumberTemplate(formattedCompleteNumber.replace(/\\d/g, DIGIT_PLACEHOLDER), state);\n            this.populatedNationalNumberTemplate = formattedCompleteNumber; // With a new formatting template, the matched position\n            // using the old template needs to be reset.\n\n            this.populatedNationalNumberTemplatePosition = this.template.lastIndexOf(DIGIT_PLACEHOLDER);\n            return formattedCompleteNumber;\n          }\n        }\n      } // Format the digits as a partial (incomplete) phone number\n      // using the previously chosen formatting pattern (or a newly chosen one).\n\n\n      return this.formatNationalNumberWithNextDigits(nextDigits, state);\n    } // Formats the next phone number digits.\n\n  }, {\n    key: \"formatNationalNumberWithNextDigits\",\n    value: function formatNationalNumberWithNextDigits(nextDigits, state) {\n      var previouslyChosenFormat = this.chosenFormat; // Choose a format from the list of matching ones.\n\n      var newlyChosenFormat = this.chooseFormat(state);\n\n      if (newlyChosenFormat) {\n        if (newlyChosenFormat === previouslyChosenFormat) {\n          // If it can format the next (current) digits\n          // using the previously chosen phone number format\n          // then return the updated formatted number.\n          return this.formatNextNationalNumberDigits(nextDigits);\n        } else {\n          // If a more appropriate phone number format\n          // has been chosen for these \"leading digits\",\n          // then re-format the national phone number part\n          // using the newly selected format.\n          return this.formatNextNationalNumberDigits(state.getNationalDigits());\n        }\n      }\n    }\n  }, {\n    key: \"narrowDownMatchingFormats\",\n    value: function narrowDownMatchingFormats(_ref2) {\n      var _this2 = this;\n\n      var nationalSignificantNumber = _ref2.nationalSignificantNumber,\n          nationalPrefix = _ref2.nationalPrefix,\n          international = _ref2.international;\n      var leadingDigits = nationalSignificantNumber; // \"leading digits\" pattern list starts with a\n      // \"leading digits\" pattern fitting a maximum of 3 leading digits.\n      // So, after a user inputs 3 digits of a national (significant) phone number\n      // this national (significant) number can already be formatted.\n      // The next \"leading digits\" pattern is for 4 leading digits max,\n      // and the \"leading digits\" pattern after it is for 5 leading digits max, etc.\n      // This implementation is different from Google's\n      // in that it searches for a fitting format\n      // even if the user has entered less than\n      // `MIN_LEADING_DIGITS_LENGTH` digits of a national number.\n      // Because some leading digit patterns already match for a single first digit.\n\n      var leadingDigitsPatternIndex = leadingDigits.length - MIN_LEADING_DIGITS_LENGTH;\n\n      if (leadingDigitsPatternIndex < 0) {\n        leadingDigitsPatternIndex = 0;\n      }\n\n      this.matchingFormats = this.matchingFormats.filter(function (format) {\n        return _this2.formatSuits(format, international, nationalPrefix) && _this2.formatMatches(format, leadingDigits, leadingDigitsPatternIndex);\n      }); // If there was a phone number format chosen\n      // and it no longer holds given the new leading digits then reset it.\n      // The test for this `if` condition is marked as:\n      // \"Reset a chosen format when it no longer holds given the new leading digits\".\n      // To construct a valid test case for this one can find a country\n      // in `PhoneNumberMetadata.xml` yielding one format for 3 `<leadingDigits>`\n      // and yielding another format for 4 `<leadingDigits>` (Australia in this case).\n\n      if (this.chosenFormat && this.matchingFormats.indexOf(this.chosenFormat) === -1) {\n        this.resetFormat();\n      }\n    }\n  }, {\n    key: \"formatSuits\",\n    value: function formatSuits(format, international, nationalPrefix) {\n      // When a prefix before a national (significant) number is\n      // simply a national prefix, then it's parsed as `this.nationalPrefix`.\n      // In more complex cases, a prefix before national (significant) number\n      // could include a national prefix as well as some \"capturing groups\",\n      // and in that case there's no info whether a national prefix has been parsed.\n      // If national prefix is not used when formatting a phone number\n      // using this format, but a national prefix has been entered by the user,\n      // and was extracted, then discard such phone number format.\n      // In Google's \"AsYouType\" formatter code, the equivalent would be this part:\n      // https://github.com/google/libphonenumber/blob/0a45cfd96e71cad8edb0e162a70fcc8bd9728933/java/libphonenumber/src/com/google/i18n/phonenumbers/AsYouTypeFormatter.java#L175-L184\n      if (nationalPrefix && !format.usesNationalPrefix() && // !format.domesticCarrierCodeFormattingRule() &&\n      !format.nationalPrefixIsOptionalWhenFormattingInNationalFormat()) {\n        return false;\n      } // If national prefix is mandatory for this phone number format\n      // and there're no guarantees that a national prefix is present in user input\n      // then discard this phone number format as not suitable.\n      // In Google's \"AsYouType\" formatter code, the equivalent would be this part:\n      // https://github.com/google/libphonenumber/blob/0a45cfd96e71cad8edb0e162a70fcc8bd9728933/java/libphonenumber/src/com/google/i18n/phonenumbers/AsYouTypeFormatter.java#L185-L193\n\n\n      if (!international && !nationalPrefix && format.nationalPrefixIsMandatoryWhenFormattingInNationalFormat()) {\n        return false;\n      }\n\n      return true;\n    }\n  }, {\n    key: \"formatMatches\",\n    value: function formatMatches(format, leadingDigits, leadingDigitsPatternIndex) {\n      var leadingDigitsPatternsCount = format.leadingDigitsPatterns().length; // If this format is not restricted to a certain\n      // leading digits pattern then it fits.\n      // The test case could be found by searching for \"leadingDigitsPatternsCount === 0\".\n\n      if (leadingDigitsPatternsCount === 0) {\n        return true;\n      } // Start narrowing down the list of possible formats based on the leading digits.\n      // (only previously matched formats take part in the narrowing down process)\n      // `leading_digits_patterns` start with 3 digits min\n      // and then go up from there one digit at a time.\n\n\n      leadingDigitsPatternIndex = Math.min(leadingDigitsPatternIndex, leadingDigitsPatternsCount - 1);\n      var leadingDigitsPattern = format.leadingDigitsPatterns()[leadingDigitsPatternIndex]; // Google imposes a requirement on the leading digits\n      // to be minimum 3 digits long in order to be eligible\n      // for checking those with a leading digits pattern.\n      //\n      // Since `leading_digits_patterns` start with 3 digits min,\n      // Google's original `libphonenumber` library only starts\n      // excluding any non-matching formats only when the\n      // national number entered so far is at least 3 digits long,\n      // otherwise format matching would give false negatives.\n      //\n      // For example, when the digits entered so far are `2`\n      // and the leading digits pattern is `21` –\n      // it's quite obvious in this case that the format could be the one\n      // but due to the absence of further digits it would give false negative.\n      //\n      // Also, `leading_digits_patterns` doesn't always correspond to a single\n      // digits count. For example, `60|8` pattern would already match `8`\n      // but the `60` part would require having at least two leading digits,\n      // so the whole pattern would require inputting two digits first in order to\n      // decide on whether it matches the input, even when the input is \"80\".\n      //\n      // This library — `libphonenumber-js` — allows filtering by `leading_digits_patterns`\n      // even when there's only 1 or 2 digits of the national (significant) number.\n      // To do that, it uses a non-strict pattern matcher written specifically for that.\n      //\n\n      if (leadingDigits.length < MIN_LEADING_DIGITS_LENGTH) {\n        // Before leading digits < 3 matching was implemented:\n        // return true\n        //\n        // After leading digits < 3 matching was implemented:\n        try {\n          return new PatternMatcher(leadingDigitsPattern).match(leadingDigits, {\n            allowOverflow: true\n          }) !== undefined;\n        } catch (error)\n        /* istanbul ignore next */\n        {\n          // There's a slight possibility that there could be some undiscovered bug\n          // in the pattern matcher code. Since the \"leading digits < 3 matching\"\n          // feature is not \"essential\" for operation, it can fall back to the old way\n          // in case of any issues rather than halting the application's execution.\n          console.error(error);\n          return true;\n        }\n      } // If at least `MIN_LEADING_DIGITS_LENGTH` digits of a national number are\n      // available then use the usual regular expression matching.\n      //\n      // The whole pattern is wrapped in round brackets (`()`) because\n      // the pattern can use \"or\" operator (`|`) at the top level of the pattern.\n      //\n\n\n      return new RegExp(\"^(\".concat(leadingDigitsPattern, \")\")).test(leadingDigits);\n    }\n  }, {\n    key: \"getFormatFormat\",\n    value: function getFormatFormat(format, international) {\n      return international ? format.internationalFormat() : format.format();\n    }\n  }, {\n    key: \"chooseFormat\",\n    value: function chooseFormat(state) {\n      var _this3 = this;\n\n      var _loop = function _loop() {\n        var format = _step2.value;\n\n        // If this format is currently being used\n        // and is still suitable, then stick to it.\n        if (_this3.chosenFormat === format) {\n          return \"break\";\n        } // Sometimes, a formatting rule inserts additional digits in a phone number,\n        // and \"as you type\" formatter can't do that: it should only use the digits\n        // that the user has input.\n        //\n        // For example, in Argentina, there's a format for mobile phone numbers:\n        //\n        // {\n        //    \"pattern\": \"(\\\\d)(\\\\d{2})(\\\\d{4})(\\\\d{4})\",\n        //    \"leading_digits_patterns\": [\"91\"],\n        //    \"national_prefix_formatting_rule\": \"0$1\",\n        //    \"format\": \"$2 15-$3-$4\",\n        //    \"international_format\": \"$1 $2 $3-$4\"\n        // }\n        //\n        // In that format, `international_format` is used instead of `format`\n        // because `format` inserts `15` in the formatted number,\n        // and `AsYouType` formatter should only use the digits\n        // the user has actually input, without adding any extra digits.\n        // In this case, it wouldn't make a difference, because the `15`\n        // is first stripped when applying `national_prefix_for_parsing`\n        // and then re-added when using `format`, so in reality it doesn't\n        // add any new digits to the number, but to detect that, the code\n        // would have to be more complex: it would have to try formatting\n        // the digits using the format and then see if any digits have\n        // actually been added or removed, and then, every time a new digit\n        // is input, it should re-check whether the chosen format doesn't\n        // alter the digits.\n        //\n        // Google's code doesn't go that far, and so does this library:\n        // it simply requires that a `format` doesn't add any additonal\n        // digits to user's input.\n        //\n        // Also, people in general should move from inputting phone numbers\n        // in national format (possibly with national prefixes)\n        // and use international phone number format instead:\n        // it's a logical thing in the modern age of mobile phones,\n        // globalization and the internet.\n        //\n\n        /* istanbul ignore if */\n\n\n        if (!NON_ALTERING_FORMAT_REG_EXP.test(_this3.getFormatFormat(format, state.international))) {\n          return \"continue\";\n        }\n\n        if (!_this3.createTemplateForFormat(format, state)) {\n          // Remove the format if it can't generate a template.\n          _this3.matchingFormats = _this3.matchingFormats.filter(function (_) {\n            return _ !== format;\n          });\n          return \"continue\";\n        }\n\n        _this3.chosenFormat = format;\n        return \"break\";\n      };\n\n      // When there are multiple available formats, the formatter uses the first\n      // format where a formatting template could be created.\n      //\n      // For some weird reason, `istanbul` says \"else path not taken\"\n      // for the `for of` line below. Supposedly that means that\n      // the loop doesn't ever go over the last element in the list.\n      // That's true because there always is `this.chosenFormat`\n      // when `this.matchingFormats` is non-empty.\n      // And, for some weird reason, it doesn't think that the case\n      // with empty `this.matchingFormats` qualifies for a valid \"else\" path.\n      // So simply muting this `istanbul` warning.\n      // It doesn't skip the contents of the `for of` loop,\n      // it just skips the `for of` line.\n      //\n\n      /* istanbul ignore next */\n      for (var _iterator2 = _createForOfIteratorHelperLoose(this.matchingFormats.slice()), _step2; !(_step2 = _iterator2()).done;) {\n        var _ret = _loop();\n\n        if (_ret === \"break\") break;\n        if (_ret === \"continue\") continue;\n      }\n\n      if (!this.chosenFormat) {\n        // No format matches the national (significant) phone number.\n        this.resetFormat();\n      }\n\n      return this.chosenFormat;\n    }\n  }, {\n    key: \"createTemplateForFormat\",\n    value: function createTemplateForFormat(format, state) {\n      // The formatter doesn't format numbers when numberPattern contains '|', e.g.\n      // (20|3)\\d{4}. In those cases we quickly return.\n      // (Though there's no such format in current metadata)\n\n      /* istanbul ignore if */\n      if (SUPPORT_LEGACY_FORMATTING_PATTERNS && format.pattern().indexOf('|') >= 0) {\n        return;\n      } // Get formatting template for this phone number format\n\n\n      var template = this.getTemplateForFormat(format, state); // If the national number entered is too long\n      // for any phone number format, then abort.\n\n      if (template) {\n        this.setNationalNumberTemplate(template, state);\n        return true;\n      }\n    }\n  }, {\n    key: \"getSeparatorAfterNationalPrefix\",\n    value: function getSeparatorAfterNationalPrefix(format) {\n      // `US` metadata doesn't have a `national_prefix_formatting_rule`,\n      // so the `if` condition below doesn't apply to `US`,\n      // but in reality there shoudl be a separator\n      // between a national prefix and a national (significant) number.\n      // So `US` national prefix separator is a \"special\" \"hardcoded\" case.\n      if (this.isNANP) {\n        return ' ';\n      } // If a `format` has a `national_prefix_formatting_rule`\n      // and that rule has a separator after a national prefix,\n      // then it means that there should be a separator\n      // between a national prefix and a national (significant) number.\n\n\n      if (format && format.nationalPrefixFormattingRule() && NATIONAL_PREFIX_SEPARATORS_PATTERN.test(format.nationalPrefixFormattingRule())) {\n        return ' ';\n      } // At this point, there seems to be no clear evidence that\n      // there should be a separator between a national prefix\n      // and a national (significant) number. So don't insert one.\n\n\n      return '';\n    }\n  }, {\n    key: \"getInternationalPrefixBeforeCountryCallingCode\",\n    value: function getInternationalPrefixBeforeCountryCallingCode(_ref3, options) {\n      var IDDPrefix = _ref3.IDDPrefix,\n          missingPlus = _ref3.missingPlus;\n\n      if (IDDPrefix) {\n        return options && options.spacing === false ? IDDPrefix : IDDPrefix + ' ';\n      }\n\n      if (missingPlus) {\n        return '';\n      }\n\n      return '+';\n    }\n  }, {\n    key: \"getTemplate\",\n    value: function getTemplate(state) {\n      if (!this.template) {\n        return;\n      } // `this.template` holds the template for a \"complete\" phone number.\n      // The currently entered phone number is most likely not \"complete\",\n      // so trim all non-populated digits.\n\n\n      var index = -1;\n      var i = 0;\n      var internationalPrefix = state.international ? this.getInternationalPrefixBeforeCountryCallingCode(state, {\n        spacing: false\n      }) : '';\n\n      while (i < internationalPrefix.length + state.getDigitsWithoutInternationalPrefix().length) {\n        index = this.template.indexOf(DIGIT_PLACEHOLDER, index + 1);\n        i++;\n      }\n\n      return cutAndStripNonPairedParens(this.template, index + 1);\n    }\n  }, {\n    key: \"setNationalNumberTemplate\",\n    value: function setNationalNumberTemplate(template, state) {\n      this.nationalNumberTemplate = template;\n      this.populatedNationalNumberTemplate = template; // With a new formatting template, the matched position\n      // using the old template needs to be reset.\n\n      this.populatedNationalNumberTemplatePosition = -1; // For convenience, the public `.template` property\n      // contains the whole international number\n      // if the phone number being input is international:\n      // 'x' for the '+' sign, 'x'es for the country phone code,\n      // a spacebar and then the template for the formatted national number.\n\n      if (state.international) {\n        this.template = this.getInternationalPrefixBeforeCountryCallingCode(state).replace(/[\\d\\+]/g, DIGIT_PLACEHOLDER) + repeat(DIGIT_PLACEHOLDER, state.callingCode.length) + ' ' + template;\n      } else {\n        this.template = template;\n      }\n    }\n    /**\r\n     * Generates formatting template for a national phone number,\r\n     * optionally containing a national prefix, for a format.\r\n     * @param  {Format} format\r\n     * @param  {string} nationalPrefix\r\n     * @return {string}\r\n     */\n\n  }, {\n    key: \"getTemplateForFormat\",\n    value: function getTemplateForFormat(format, _ref4) {\n      var nationalSignificantNumber = _ref4.nationalSignificantNumber,\n          international = _ref4.international,\n          nationalPrefix = _ref4.nationalPrefix,\n          complexPrefixBeforeNationalSignificantNumber = _ref4.complexPrefixBeforeNationalSignificantNumber;\n      var pattern = format.pattern();\n      /* istanbul ignore else */\n\n      if (SUPPORT_LEGACY_FORMATTING_PATTERNS) {\n        pattern = pattern // Replace anything in the form of [..] with \\d\n        .replace(CREATE_CHARACTER_CLASS_PATTERN(), '\\\\d') // Replace any standalone digit (not the one in `{}`) with \\d\n        .replace(CREATE_STANDALONE_DIGIT_PATTERN(), '\\\\d');\n      } // Generate a dummy national number (consisting of `9`s)\n      // that fits this format's `pattern`.\n      //\n      // This match will always succeed,\n      // because the \"longest dummy phone number\"\n      // has enough length to accomodate any possible\n      // national phone number format pattern.\n      //\n\n\n      var digits = LONGEST_DUMMY_PHONE_NUMBER.match(pattern)[0]; // If the national number entered is too long\n      // for any phone number format, then abort.\n\n      if (nationalSignificantNumber.length > digits.length) {\n        return;\n      } // Get a formatting template which can be used to efficiently format\n      // a partial number where digits are added one by one.\n      // Below `strictPattern` is used for the\n      // regular expression (with `^` and `$`).\n      // This wasn't originally in Google's `libphonenumber`\n      // and I guess they don't really need it\n      // because they're not using \"templates\" to format phone numbers\n      // but I added `strictPattern` after encountering\n      // South Korean phone number formatting bug.\n      //\n      // Non-strict regular expression bug demonstration:\n      //\n      // this.nationalSignificantNumber : `111111111` (9 digits)\n      //\n      // pattern : (\\d{2})(\\d{3,4})(\\d{4})\n      // format : `$1 $2 $3`\n      // digits : `9999999999` (10 digits)\n      //\n      // '9999999999'.replace(new RegExp(/(\\d{2})(\\d{3,4})(\\d{4})/g), '$1 $2 $3') = \"99 9999 9999\"\n      //\n      // template : xx xxxx xxxx\n      //\n      // But the correct template in this case is `xx xxx xxxx`.\n      // The template was generated incorrectly because of the\n      // `{3,4}` variability in the `pattern`.\n      //\n      // The fix is, if `this.nationalSignificantNumber` has already sufficient length\n      // to satisfy the `pattern` completely then `this.nationalSignificantNumber`\n      // is used instead of `digits`.\n\n\n      var strictPattern = new RegExp('^' + pattern + '$');\n      var nationalNumberDummyDigits = nationalSignificantNumber.replace(/\\d/g, DUMMY_DIGIT); // If `this.nationalSignificantNumber` has already sufficient length\n      // to satisfy the `pattern` completely then use it\n      // instead of `digits`.\n\n      if (strictPattern.test(nationalNumberDummyDigits)) {\n        digits = nationalNumberDummyDigits;\n      }\n\n      var numberFormat = this.getFormatFormat(format, international);\n      var nationalPrefixIncludedInTemplate; // If a user did input a national prefix (and that's guaranteed),\n      // and if a `format` does have a national prefix formatting rule,\n      // then see if that national prefix formatting rule\n      // prepends exactly the same national prefix the user has input.\n      // If that's the case, then use the `format` with the national prefix formatting rule.\n      // Otherwise, use  the `format` without the national prefix formatting rule,\n      // and prepend a national prefix manually to it.\n\n      if (this.shouldTryNationalPrefixFormattingRule(format, {\n        international: international,\n        nationalPrefix: nationalPrefix\n      })) {\n        var numberFormatWithNationalPrefix = numberFormat.replace(FIRST_GROUP_PATTERN, format.nationalPrefixFormattingRule()); // If `national_prefix_formatting_rule` of a `format` simply prepends\n        // national prefix at the start of a national (significant) number,\n        // then such formatting can be used with `AsYouType` formatter.\n        // There seems to be no `else` case: everywhere in metadata,\n        // national prefix formatting rule is national prefix + $1,\n        // or `($1)`, in which case such format isn't even considered\n        // when the user has input a national prefix.\n\n        /* istanbul ignore else */\n\n        if (parseDigits(format.nationalPrefixFormattingRule()) === (nationalPrefix || '') + parseDigits('$1')) {\n          numberFormat = numberFormatWithNationalPrefix;\n          nationalPrefixIncludedInTemplate = true; // Replace all digits of the national prefix in the formatting template\n          // with `DIGIT_PLACEHOLDER`s.\n\n          if (nationalPrefix) {\n            var i = nationalPrefix.length;\n\n            while (i > 0) {\n              numberFormat = numberFormat.replace(/\\d/, DIGIT_PLACEHOLDER);\n              i--;\n            }\n          }\n        }\n      } // Generate formatting template for this phone number format.\n\n\n      var template = digits // Format the dummy phone number according to the format.\n      .replace(new RegExp(pattern), numberFormat) // Replace each dummy digit with a DIGIT_PLACEHOLDER.\n      .replace(new RegExp(DUMMY_DIGIT, 'g'), DIGIT_PLACEHOLDER); // If a prefix of a national (significant) number is not as simple\n      // as just a basic national prefix, then just prepend such prefix\n      // before the national (significant) number, optionally spacing\n      // the two with a whitespace.\n\n      if (!nationalPrefixIncludedInTemplate) {\n        if (complexPrefixBeforeNationalSignificantNumber) {\n          // Prepend the prefix to the template manually.\n          template = repeat(DIGIT_PLACEHOLDER, complexPrefixBeforeNationalSignificantNumber.length) + ' ' + template;\n        } else if (nationalPrefix) {\n          // Prepend national prefix to the template manually.\n          template = repeat(DIGIT_PLACEHOLDER, nationalPrefix.length) + this.getSeparatorAfterNationalPrefix(format) + template;\n        }\n      }\n\n      if (international) {\n        template = applyInternationalSeparatorStyle(template);\n      }\n\n      return template;\n    }\n  }, {\n    key: \"formatNextNationalNumberDigits\",\n    value: function formatNextNationalNumberDigits(digits) {\n      var result = populateTemplateWithDigits(this.populatedNationalNumberTemplate, this.populatedNationalNumberTemplatePosition, digits);\n\n      if (!result) {\n        // Reset the format.\n        this.resetFormat();\n        return;\n      }\n\n      this.populatedNationalNumberTemplate = result[0];\n      this.populatedNationalNumberTemplatePosition = result[1]; // Return the formatted phone number so far.\n\n      return cutAndStripNonPairedParens(this.populatedNationalNumberTemplate, this.populatedNationalNumberTemplatePosition + 1); // The old way which was good for `input-format` but is not so good\n      // for `react-phone-number-input`'s default input (`InputBasic`).\n      // return closeNonPairedParens(this.populatedNationalNumberTemplate, this.populatedNationalNumberTemplatePosition + 1)\n      // \t.replace(new RegExp(DIGIT_PLACEHOLDER, 'g'), ' ')\n    }\n  }, {\n    key: \"shouldTryNationalPrefixFormattingRule\",\n    value: function shouldTryNationalPrefixFormattingRule(format, _ref5) {\n      var international = _ref5.international,\n          nationalPrefix = _ref5.nationalPrefix;\n\n      if (format.nationalPrefixFormattingRule()) {\n        // In some countries, `national_prefix_formatting_rule` is `($1)`,\n        // so it applies even if the user hasn't input a national prefix.\n        // `format.usesNationalPrefix()` detects such cases.\n        var usesNationalPrefix = format.usesNationalPrefix();\n\n        if (usesNationalPrefix && nationalPrefix || !usesNationalPrefix && !international) {\n          return true;\n        }\n      }\n    }\n  }]);\n\n  return AsYouTypeFormatter;\n}();\n\nexport { AsYouTypeFormatter as default };\n"], "mappings": "AAAA,SAASA,+BAA+BA,CAACC,CAAC,EAAEC,cAAc,EAAE;EAAE,IAAIC,EAAE,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAIH,CAAC,CAACG,MAAM,CAACC,QAAQ,CAAC,IAAIJ,CAAC,CAAC,YAAY,CAAC;EAAE,IAAIE,EAAE,EAAE,OAAO,CAACA,EAAE,GAAGA,EAAE,CAACG,IAAI,CAACL,CAAC,CAAC,EAAEM,IAAI,CAACC,IAAI,CAACL,EAAE,CAAC;EAAE,IAAIM,KAAK,CAACC,OAAO,CAACT,CAAC,CAAC,KAAKE,EAAE,GAAGQ,2BAA2B,CAACV,CAAC,CAAC,CAAC,IAAIC,cAAc,IAAID,CAAC,IAAI,OAAOA,CAAC,CAACW,MAAM,KAAK,QAAQ,EAAE;IAAE,IAAIT,EAAE,EAAEF,CAAC,GAAGE,EAAE;IAAE,IAAIU,CAAC,GAAG,CAAC;IAAE,OAAO,YAAY;MAAE,IAAIA,CAAC,IAAIZ,CAAC,CAACW,MAAM,EAAE,OAAO;QAAEE,IAAI,EAAE;MAAK,CAAC;MAAE,OAAO;QAAEA,IAAI,EAAE,KAAK;QAAEC,KAAK,EAAEd,CAAC,CAACY,CAAC,EAAE;MAAE,CAAC;IAAE,CAAC;EAAE;EAAE,MAAM,IAAIG,SAAS,CAAC,uIAAuI,CAAC;AAAE;AAE3lB,SAASL,2BAA2BA,CAACV,CAAC,EAAEgB,MAAM,EAAE;EAAE,IAAI,CAAChB,CAAC,EAAE;EAAQ,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOiB,iBAAiB,CAACjB,CAAC,EAAEgB,MAAM,CAAC;EAAE,IAAIE,CAAC,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAAChB,IAAI,CAACL,CAAC,CAAC,CAACsB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAE,IAAIJ,CAAC,KAAK,QAAQ,IAAIlB,CAAC,CAACuB,WAAW,EAAEL,CAAC,GAAGlB,CAAC,CAACuB,WAAW,CAACC,IAAI;EAAE,IAAIN,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOV,KAAK,CAACiB,IAAI,CAACzB,CAAC,CAAC;EAAE,IAAIkB,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACQ,IAAI,CAACR,CAAC,CAAC,EAAE,OAAOD,iBAAiB,CAACjB,CAAC,EAAEgB,MAAM,CAAC;AAAE;AAE/Z,SAASC,iBAAiBA,CAACU,GAAG,EAAEC,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGD,GAAG,CAAChB,MAAM,EAAEiB,GAAG,GAAGD,GAAG,CAAChB,MAAM;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEiB,IAAI,GAAG,IAAIrB,KAAK,CAACoB,GAAG,CAAC,EAAEhB,CAAC,GAAGgB,GAAG,EAAEhB,CAAC,EAAE,EAAE;IAAEiB,IAAI,CAACjB,CAAC,CAAC,GAAGe,GAAG,CAACf,CAAC,CAAC;EAAE;EAAE,OAAOiB,IAAI;AAAE;AAEtL,SAASC,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIjB,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AAExJ,SAASkB,iBAAiBA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAAE,KAAK,IAAIvB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuB,KAAK,CAACxB,MAAM,EAAEC,CAAC,EAAE,EAAE;IAAE,IAAIwB,UAAU,GAAGD,KAAK,CAACvB,CAAC,CAAC;IAAEwB,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;IAAED,UAAU,CAACE,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;IAAEpB,MAAM,CAACqB,cAAc,CAACN,MAAM,EAAEE,UAAU,CAACK,GAAG,EAAEL,UAAU,CAAC;EAAE;AAAE;AAE5T,SAASM,YAAYA,CAACV,WAAW,EAAEW,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEV,iBAAiB,CAACD,WAAW,CAACZ,SAAS,EAAEuB,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAEX,iBAAiB,CAACD,WAAW,EAAEY,WAAW,CAAC;EAAEzB,MAAM,CAACqB,cAAc,CAACR,WAAW,EAAE,WAAW,EAAE;IAAEO,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAOP,WAAW;AAAE;AAE5R,SAASa,iBAAiB,EAAEC,eAAe,EAAEC,MAAM,EAAEC,0BAA0B,EAAEC,oBAAoB,EAAEC,oBAAoB,EAAEC,0BAA0B,QAAQ,8BAA8B;AAC7L,OAAOC,oBAAoB,IAAIC,uBAAuB,QAAQ,kCAAkC;AAChG,OAAOC,cAAc,MAAM,wCAAwC;AACnE,OAAOC,WAAW,MAAM,0BAA0B;AAClD,SAASV,iBAAiB,QAAQ,8BAA8B;AAChE,SAASW,mBAAmB,QAAQ,8CAA8C;AAClF,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,OAAOC,gCAAgC,MAAM,+CAA+C,CAAC,CAAC;AAC9F;;AAEA,IAAIC,WAAW,GAAG,GAAG,CAAC,CAAC;;AAEvB,IAAIC,oCAAoC,GAAG,EAAE,CAAC,CAAC;AAC/C;;AAEA,IAAIC,0BAA0B,GAAGd,MAAM,CAACY,WAAW,EAAEC,oCAAoC,CAAC,CAAC,CAAC;AAC5F;;AAEA,IAAIE,kCAAkC,GAAG,MAAM,CAAC,CAAC;AACjD;AACA;AACA;AACA;AACA;;AAEA,IAAIC,kCAAkC,GAAG,IAAI,CAAC,CAAC;AAC/C;;AAEA,IAAIC,8BAA8B,GAAGD,kCAAkC,IAAI,YAAY;EACrF,OAAO,iBAAiB;AAC1B,CAAC,CAAC,CAAC;AACH;AACA;AACA;AACA;;AAGA,IAAIE,+BAA+B,GAAGF,kCAAkC,IAAI,YAAY;EACtF,OAAO,mBAAmB;AAC5B,CAAC,CAAC,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,IAAIG,2BAA2B,GAAG,IAAIC,MAAM,CAAC,GAAG,GAAGV,iBAAiB,GAAG,IAAI;AAAG;AAC9E;AACA;AACA,MAAM,GAAG,GAAG,GAAGA,iBAAiB,GAAG,IAAI,GAAG,UAAU,GAAGA,iBAAiB,GAAG,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC;AAC1F;AACA;;AAEA,IAAIW,yBAAyB,GAAG,CAAC;AAEjC,IAAIC,kBAAkB,GAAG,aAAa,YAAY;EAChD,SAASA,kBAAkBA,CAACC,IAAI,EAAE;IAChC,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;MAClBC,QAAQ,GAAGF,IAAI,CAACE,QAAQ;IAE5B1C,eAAe,CAAC,IAAI,EAAEuC,kBAAkB,CAAC;IAEzC,IAAI,CAACG,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,WAAW,CAAC,CAAC;EACpB;EAEA/B,YAAY,CAAC2B,kBAAkB,EAAE,CAAC;IAChC5B,GAAG,EAAE,aAAa;IAClB3B,KAAK,EAAE,SAAS2D,WAAWA,CAAA,EAAG;MAC5B,IAAI,CAACC,YAAY,GAAGC,SAAS;MAC7B,IAAI,CAACC,QAAQ,GAAGD,SAAS;MACzB,IAAI,CAACE,sBAAsB,GAAGF,SAAS;MACvC,IAAI,CAACG,+BAA+B,GAAGH,SAAS;MAChD,IAAI,CAACI,uCAAuC,GAAG,CAAC,CAAC;IACnD;EACF,CAAC,EAAE;IACDtC,GAAG,EAAE,OAAO;IACZ3B,KAAK,EAAE,SAASkE,KAAKA,CAACC,aAAa,EAAEV,KAAK,EAAE;MAC1C,IAAI,CAACE,WAAW,CAAC,CAAC;MAElB,IAAIQ,aAAa,EAAE;QACjB,IAAI,CAACC,MAAM,GAAGD,aAAa,CAACE,WAAW,CAAC,CAAC,KAAK,GAAG;QACjD,IAAI,CAACC,eAAe,GAAGH,aAAa,CAACI,OAAO,CAAC,CAAC;QAE9C,IAAId,KAAK,CAACe,yBAAyB,EAAE;UACnC,IAAI,CAACC,yBAAyB,CAAChB,KAAK,CAAC;QACvC;MACF,CAAC,MAAM;QACL,IAAI,CAACW,MAAM,GAAGP,SAAS;QACvB,IAAI,CAACS,eAAe,GAAG,EAAE;MAC3B;IACF;IACA;AACJ;AACA;AACA;AACA;AACA;EAEE,CAAC,EAAE;IACD3C,GAAG,EAAE,QAAQ;IACb3B,KAAK,EAAE,SAAS0E,MAAMA,CAACC,UAAU,EAAElB,KAAK,EAAE;MACxC,IAAImB,KAAK,GAAG,IAAI;;MAEhB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAIrC,uBAAuB,CAACkB,KAAK,CAACe,yBAAyB,EAAE,IAAI,CAACd,QAAQ,CAAC,EAAE;QAC3E,KAAK,IAAImB,SAAS,GAAG5F,+BAA+B,CAAC,IAAI,CAACqF,eAAe,CAAC,EAAEQ,KAAK,EAAE,CAAC,CAACA,KAAK,GAAGD,SAAS,CAAC,CAAC,EAAE9E,IAAI,GAAG;UAC/G,IAAI2E,MAAM,GAAGI,KAAK,CAAC9E,KAAK;UACxB,IAAI+E,uBAAuB,GAAGzC,oBAAoB,CAACmB,KAAK,EAAEiB,MAAM,EAAE;YAChEhB,QAAQ,EAAE,IAAI,CAACA,QAAQ;YACvBsB,qCAAqC,EAAE,SAASA,qCAAqCA,CAACN,MAAM,EAAE;cAC5F,OAAOE,KAAK,CAACI,qCAAqC,CAACN,MAAM,EAAE;gBACzDO,aAAa,EAAExB,KAAK,CAACwB,aAAa;gBAClCC,cAAc,EAAEzB,KAAK,CAACyB;cACxB,CAAC,CAAC;YACJ,CAAC;YACDC,+BAA+B,EAAE,SAASA,+BAA+BA,CAACT,MAAM,EAAE;cAChF,OAAOE,KAAK,CAACO,+BAA+B,CAACT,MAAM,CAAC;YACtD;UACF,CAAC,CAAC;UAEF,IAAIK,uBAAuB,EAAE;YAC3B,IAAI,CAACpB,WAAW,CAAC,CAAC;YAClB,IAAI,CAACC,YAAY,GAAGc,MAAM;YAC1B,IAAI,CAACU,yBAAyB,CAACL,uBAAuB,CAACM,OAAO,CAAC,KAAK,EAAEtD,iBAAiB,CAAC,EAAE0B,KAAK,CAAC;YAChG,IAAI,CAACO,+BAA+B,GAAGe,uBAAuB,CAAC,CAAC;YAChE;;YAEA,IAAI,CAACd,uCAAuC,GAAG,IAAI,CAACH,QAAQ,CAACwB,WAAW,CAACvD,iBAAiB,CAAC;YAC3F,OAAOgD,uBAAuB;UAChC;QACF;MACF,CAAC,CAAC;MACF;;MAGA,OAAO,IAAI,CAACQ,kCAAkC,CAACZ,UAAU,EAAElB,KAAK,CAAC;IACnE,CAAC,CAAC;EAEJ,CAAC,EAAE;IACD9B,GAAG,EAAE,oCAAoC;IACzC3B,KAAK,EAAE,SAASuF,kCAAkCA,CAACZ,UAAU,EAAElB,KAAK,EAAE;MACpE,IAAI+B,sBAAsB,GAAG,IAAI,CAAC5B,YAAY,CAAC,CAAC;;MAEhD,IAAI6B,iBAAiB,GAAG,IAAI,CAACC,YAAY,CAACjC,KAAK,CAAC;MAEhD,IAAIgC,iBAAiB,EAAE;QACrB,IAAIA,iBAAiB,KAAKD,sBAAsB,EAAE;UAChD;UACA;UACA;UACA,OAAO,IAAI,CAACG,8BAA8B,CAAChB,UAAU,CAAC;QACxD,CAAC,MAAM;UACL;UACA;UACA;UACA;UACA,OAAO,IAAI,CAACgB,8BAA8B,CAAClC,KAAK,CAACmC,iBAAiB,CAAC,CAAC,CAAC;QACvE;MACF;IACF;EACF,CAAC,EAAE;IACDjE,GAAG,EAAE,2BAA2B;IAChC3B,KAAK,EAAE,SAASyE,yBAAyBA,CAACoB,KAAK,EAAE;MAC/C,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAItB,yBAAyB,GAAGqB,KAAK,CAACrB,yBAAyB;QAC3DU,cAAc,GAAGW,KAAK,CAACX,cAAc;QACrCD,aAAa,GAAGY,KAAK,CAACZ,aAAa;MACvC,IAAIc,aAAa,GAAGvB,yBAAyB,CAAC,CAAC;MAC/C;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA,IAAIwB,yBAAyB,GAAGD,aAAa,CAAClG,MAAM,GAAGyD,yBAAyB;MAEhF,IAAI0C,yBAAyB,GAAG,CAAC,EAAE;QACjCA,yBAAyB,GAAG,CAAC;MAC/B;MAEA,IAAI,CAAC1B,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC2B,MAAM,CAAC,UAAUvB,MAAM,EAAE;QACnE,OAAOoB,MAAM,CAACI,WAAW,CAACxB,MAAM,EAAEO,aAAa,EAAEC,cAAc,CAAC,IAAIY,MAAM,CAACK,aAAa,CAACzB,MAAM,EAAEqB,aAAa,EAAEC,yBAAyB,CAAC;MAC5I,CAAC,CAAC,CAAC,CAAC;MACJ;MACA;MACA;MACA;MACA;MACA;;MAEA,IAAI,IAAI,CAACpC,YAAY,IAAI,IAAI,CAACU,eAAe,CAAC8B,OAAO,CAAC,IAAI,CAACxC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE;QAC/E,IAAI,CAACD,WAAW,CAAC,CAAC;MACpB;IACF;EACF,CAAC,EAAE;IACDhC,GAAG,EAAE,aAAa;IAClB3B,KAAK,EAAE,SAASkG,WAAWA,CAACxB,MAAM,EAAEO,aAAa,EAAEC,cAAc,EAAE;MACjE;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAIA,cAAc,IAAI,CAACR,MAAM,CAAC2B,kBAAkB,CAAC,CAAC;MAAI;MACtD,CAAC3B,MAAM,CAAC4B,sDAAsD,CAAC,CAAC,EAAE;QAChE,OAAO,KAAK;MACd,CAAC,CAAC;MACF;MACA;MACA;MACA;;MAGA,IAAI,CAACrB,aAAa,IAAI,CAACC,cAAc,IAAIR,MAAM,CAAC6B,uDAAuD,CAAC,CAAC,EAAE;QACzG,OAAO,KAAK;MACd;MAEA,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACD5E,GAAG,EAAE,eAAe;IACpB3B,KAAK,EAAE,SAASmG,aAAaA,CAACzB,MAAM,EAAEqB,aAAa,EAAEC,yBAAyB,EAAE;MAC9E,IAAIQ,0BAA0B,GAAG9B,MAAM,CAAC+B,qBAAqB,CAAC,CAAC,CAAC5G,MAAM,CAAC,CAAC;MACxE;MACA;;MAEA,IAAI2G,0BAA0B,KAAK,CAAC,EAAE;QACpC,OAAO,IAAI;MACb,CAAC,CAAC;MACF;MACA;MACA;;MAGAR,yBAAyB,GAAGU,IAAI,CAACC,GAAG,CAACX,yBAAyB,EAAEQ,0BAA0B,GAAG,CAAC,CAAC;MAC/F,IAAII,oBAAoB,GAAGlC,MAAM,CAAC+B,qBAAqB,CAAC,CAAC,CAACT,yBAAyB,CAAC,CAAC,CAAC;MACtF;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA,IAAID,aAAa,CAAClG,MAAM,GAAGyD,yBAAyB,EAAE;QACpD;QACA;QACA;QACA;QACA,IAAI;UACF,OAAO,IAAId,cAAc,CAACoE,oBAAoB,CAAC,CAACC,KAAK,CAACd,aAAa,EAAE;YACnEe,aAAa,EAAE;UACjB,CAAC,CAAC,KAAKjD,SAAS;QAClB,CAAC,CAAC,OAAOkD,KAAK,EACd;QACA;UACE;UACA;UACA;UACA;UACAC,OAAO,CAACD,KAAK,CAACA,KAAK,CAAC;UACpB,OAAO,IAAI;QACb;MACF,CAAC,CAAC;MACF;MACA;MACA;MACA;MACA;;MAGA,OAAO,IAAI1D,MAAM,CAAC,IAAI,CAAC4D,MAAM,CAACL,oBAAoB,EAAE,GAAG,CAAC,CAAC,CAAChG,IAAI,CAACmF,aAAa,CAAC;IAC/E;EACF,CAAC,EAAE;IACDpE,GAAG,EAAE,iBAAiB;IACtB3B,KAAK,EAAE,SAASkH,eAAeA,CAACxC,MAAM,EAAEO,aAAa,EAAE;MACrD,OAAOA,aAAa,GAAGP,MAAM,CAACyC,mBAAmB,CAAC,CAAC,GAAGzC,MAAM,CAACA,MAAM,CAAC,CAAC;IACvE;EACF,CAAC,EAAE;IACD/C,GAAG,EAAE,cAAc;IACnB3B,KAAK,EAAE,SAAS0F,YAAYA,CAACjC,KAAK,EAAE;MAClC,IAAI2D,MAAM,GAAG,IAAI;MAEjB,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;QAC3B,IAAI3C,MAAM,GAAG4C,MAAM,CAACtH,KAAK;;QAEzB;QACA;QACA,IAAIoH,MAAM,CAACxD,YAAY,KAAKc,MAAM,EAAE;UAClC,OAAO,OAAO;QAChB,CAAC,CAAC;QACF;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;;QAGA,IAAI,CAACtB,2BAA2B,CAACxC,IAAI,CAACwG,MAAM,CAACF,eAAe,CAACxC,MAAM,EAAEjB,KAAK,CAACwB,aAAa,CAAC,CAAC,EAAE;UAC1F,OAAO,UAAU;QACnB;QAEA,IAAI,CAACmC,MAAM,CAACG,uBAAuB,CAAC7C,MAAM,EAAEjB,KAAK,CAAC,EAAE;UAClD;UACA2D,MAAM,CAAC9C,eAAe,GAAG8C,MAAM,CAAC9C,eAAe,CAAC2B,MAAM,CAAC,UAAUuB,CAAC,EAAE;YAClE,OAAOA,CAAC,KAAK9C,MAAM;UACrB,CAAC,CAAC;UACF,OAAO,UAAU;QACnB;QAEA0C,MAAM,CAACxD,YAAY,GAAGc,MAAM;QAC5B,OAAO,OAAO;MAChB,CAAC;;MAED;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA,KAAK,IAAI+C,UAAU,GAAGxI,+BAA+B,CAAC,IAAI,CAACqF,eAAe,CAAC9D,KAAK,CAAC,CAAC,CAAC,EAAE8G,MAAM,EAAE,CAAC,CAACA,MAAM,GAAGG,UAAU,CAAC,CAAC,EAAE1H,IAAI,GAAG;QAC3H,IAAI2H,IAAI,GAAGL,KAAK,CAAC,CAAC;QAElB,IAAIK,IAAI,KAAK,OAAO,EAAE;QACtB,IAAIA,IAAI,KAAK,UAAU,EAAE;MAC3B;MAEA,IAAI,CAAC,IAAI,CAAC9D,YAAY,EAAE;QACtB;QACA,IAAI,CAACD,WAAW,CAAC,CAAC;MACpB;MAEA,OAAO,IAAI,CAACC,YAAY;IAC1B;EACF,CAAC,EAAE;IACDjC,GAAG,EAAE,yBAAyB;IAC9B3B,KAAK,EAAE,SAASuH,uBAAuBA,CAAC7C,MAAM,EAAEjB,KAAK,EAAE;MACrD;MACA;MACA;;MAEA;MACA,IAAIR,kCAAkC,IAAIyB,MAAM,CAACiD,OAAO,CAAC,CAAC,CAACvB,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;QAC5E;MACF,CAAC,CAAC;;MAGF,IAAItC,QAAQ,GAAG,IAAI,CAAC8D,oBAAoB,CAAClD,MAAM,EAAEjB,KAAK,CAAC,CAAC,CAAC;MACzD;;MAEA,IAAIK,QAAQ,EAAE;QACZ,IAAI,CAACsB,yBAAyB,CAACtB,QAAQ,EAAEL,KAAK,CAAC;QAC/C,OAAO,IAAI;MACb;IACF;EACF,CAAC,EAAE;IACD9B,GAAG,EAAE,iCAAiC;IACtC3B,KAAK,EAAE,SAASmF,+BAA+BA,CAACT,MAAM,EAAE;MACtD;MACA;MACA;MACA;MACA;MACA,IAAI,IAAI,CAACN,MAAM,EAAE;QACf,OAAO,GAAG;MACZ,CAAC,CAAC;MACF;MACA;MACA;;MAGA,IAAIM,MAAM,IAAIA,MAAM,CAACmD,4BAA4B,CAAC,CAAC,IAAI7E,kCAAkC,CAACpC,IAAI,CAAC8D,MAAM,CAACmD,4BAA4B,CAAC,CAAC,CAAC,EAAE;QACrI,OAAO,GAAG;MACZ,CAAC,CAAC;MACF;MACA;;MAGA,OAAO,EAAE;IACX;EACF,CAAC,EAAE;IACDlG,GAAG,EAAE,gDAAgD;IACrD3B,KAAK,EAAE,SAAS8H,8CAA8CA,CAACC,KAAK,EAAEC,OAAO,EAAE;MAC7E,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAS;QAC3BC,WAAW,GAAGH,KAAK,CAACG,WAAW;MAEnC,IAAID,SAAS,EAAE;QACb,OAAOD,OAAO,IAAIA,OAAO,CAACG,OAAO,KAAK,KAAK,GAAGF,SAAS,GAAGA,SAAS,GAAG,GAAG;MAC3E;MAEA,IAAIC,WAAW,EAAE;QACf,OAAO,EAAE;MACX;MAEA,OAAO,GAAG;IACZ;EACF,CAAC,EAAE;IACDvG,GAAG,EAAE,aAAa;IAClB3B,KAAK,EAAE,SAASoI,WAAWA,CAAC3E,KAAK,EAAE;MACjC,IAAI,CAAC,IAAI,CAACK,QAAQ,EAAE;QAClB;MACF,CAAC,CAAC;MACF;MACA;;MAGA,IAAIuE,KAAK,GAAG,CAAC,CAAC;MACd,IAAIvI,CAAC,GAAG,CAAC;MACT,IAAIwI,mBAAmB,GAAG7E,KAAK,CAACwB,aAAa,GAAG,IAAI,CAAC6C,8CAA8C,CAACrE,KAAK,EAAE;QACzG0E,OAAO,EAAE;MACX,CAAC,CAAC,GAAG,EAAE;MAEP,OAAOrI,CAAC,GAAGwI,mBAAmB,CAACzI,MAAM,GAAG4D,KAAK,CAAC8E,mCAAmC,CAAC,CAAC,CAAC1I,MAAM,EAAE;QAC1FwI,KAAK,GAAG,IAAI,CAACvE,QAAQ,CAACsC,OAAO,CAACrE,iBAAiB,EAAEsG,KAAK,GAAG,CAAC,CAAC;QAC3DvI,CAAC,EAAE;MACL;MAEA,OAAOoC,0BAA0B,CAAC,IAAI,CAAC4B,QAAQ,EAAEuE,KAAK,GAAG,CAAC,CAAC;IAC7D;EACF,CAAC,EAAE;IACD1G,GAAG,EAAE,2BAA2B;IAChC3B,KAAK,EAAE,SAASoF,yBAAyBA,CAACtB,QAAQ,EAAEL,KAAK,EAAE;MACzD,IAAI,CAACM,sBAAsB,GAAGD,QAAQ;MACtC,IAAI,CAACE,+BAA+B,GAAGF,QAAQ,CAAC,CAAC;MACjD;;MAEA,IAAI,CAACG,uCAAuC,GAAG,CAAC,CAAC,CAAC,CAAC;MACnD;MACA;MACA;MACA;;MAEA,IAAIR,KAAK,CAACwB,aAAa,EAAE;QACvB,IAAI,CAACnB,QAAQ,GAAG,IAAI,CAACgE,8CAA8C,CAACrE,KAAK,CAAC,CAAC4B,OAAO,CAAC,SAAS,EAAEtD,iBAAiB,CAAC,GAAGE,MAAM,CAACF,iBAAiB,EAAE0B,KAAK,CAACY,WAAW,CAACxE,MAAM,CAAC,GAAG,GAAG,GAAGiE,QAAQ;MACzL,CAAC,MAAM;QACL,IAAI,CAACA,QAAQ,GAAGA,QAAQ;MAC1B;IACF;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;EAEE,CAAC,EAAE;IACDnC,GAAG,EAAE,sBAAsB;IAC3B3B,KAAK,EAAE,SAAS4H,oBAAoBA,CAAClD,MAAM,EAAE8D,KAAK,EAAE;MAClD,IAAIhE,yBAAyB,GAAGgE,KAAK,CAAChE,yBAAyB;QAC3DS,aAAa,GAAGuD,KAAK,CAACvD,aAAa;QACnCC,cAAc,GAAGsD,KAAK,CAACtD,cAAc;QACrCuD,4CAA4C,GAAGD,KAAK,CAACC,4CAA4C;MACrG,IAAId,OAAO,GAAGjD,MAAM,CAACiD,OAAO,CAAC,CAAC;MAC9B;;MAEA,IAAI1E,kCAAkC,EAAE;QACtC0E,OAAO,GAAGA,OAAO,CAAC;QAAA,CACjBtC,OAAO,CAACnC,8BAA8B,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAAA,CACjDmC,OAAO,CAAClC,+BAA+B,CAAC,CAAC,EAAE,KAAK,CAAC;MACpD,CAAC,CAAC;MACF;MACA;MACA;MACA;MACA;MACA;MACA;;MAGA,IAAIuF,MAAM,GAAG3F,0BAA0B,CAAC8D,KAAK,CAACc,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3D;;MAEA,IAAInD,yBAAyB,CAAC3E,MAAM,GAAG6I,MAAM,CAAC7I,MAAM,EAAE;QACpD;MACF,CAAC,CAAC;MACF;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAGA,IAAI8I,aAAa,GAAG,IAAItF,MAAM,CAAC,GAAG,GAAGsE,OAAO,GAAG,GAAG,CAAC;MACnD,IAAIiB,yBAAyB,GAAGpE,yBAAyB,CAACa,OAAO,CAAC,KAAK,EAAExC,WAAW,CAAC,CAAC,CAAC;MACvF;MACA;;MAEA,IAAI8F,aAAa,CAAC/H,IAAI,CAACgI,yBAAyB,CAAC,EAAE;QACjDF,MAAM,GAAGE,yBAAyB;MACpC;MAEA,IAAIC,YAAY,GAAG,IAAI,CAAC3B,eAAe,CAACxC,MAAM,EAAEO,aAAa,CAAC;MAC9D,IAAI6D,gCAAgC,CAAC,CAAC;MACtC;MACA;MACA;MACA;MACA;MACA;;MAEA,IAAI,IAAI,CAAC9D,qCAAqC,CAACN,MAAM,EAAE;QACrDO,aAAa,EAAEA,aAAa;QAC5BC,cAAc,EAAEA;MAClB,CAAC,CAAC,EAAE;QACF,IAAI6D,8BAA8B,GAAGF,YAAY,CAACxD,OAAO,CAAC3C,mBAAmB,EAAEgC,MAAM,CAACmD,4BAA4B,CAAC,CAAC,CAAC,CAAC,CAAC;QACvH;QACA;QACA;QACA;QACA;QACA;;QAEA;;QAEA,IAAIpF,WAAW,CAACiC,MAAM,CAACmD,4BAA4B,CAAC,CAAC,CAAC,KAAK,CAAC3C,cAAc,IAAI,EAAE,IAAIzC,WAAW,CAAC,IAAI,CAAC,EAAE;UACrGoG,YAAY,GAAGE,8BAA8B;UAC7CD,gCAAgC,GAAG,IAAI,CAAC,CAAC;UACzC;;UAEA,IAAI5D,cAAc,EAAE;YAClB,IAAIpF,CAAC,GAAGoF,cAAc,CAACrF,MAAM;YAE7B,OAAOC,CAAC,GAAG,CAAC,EAAE;cACZ+I,YAAY,GAAGA,YAAY,CAACxD,OAAO,CAAC,IAAI,EAAEtD,iBAAiB,CAAC;cAC5DjC,CAAC,EAAE;YACL;UACF;QACF;MACF,CAAC,CAAC;;MAGF,IAAIgE,QAAQ,GAAG4E,MAAM,CAAC;MAAA,CACrBrD,OAAO,CAAC,IAAIhC,MAAM,CAACsE,OAAO,CAAC,EAAEkB,YAAY,CAAC,CAAC;MAAA,CAC3CxD,OAAO,CAAC,IAAIhC,MAAM,CAACR,WAAW,EAAE,GAAG,CAAC,EAAEd,iBAAiB,CAAC,CAAC,CAAC;MAC3D;MACA;MACA;;MAEA,IAAI,CAAC+G,gCAAgC,EAAE;QACrC,IAAIL,4CAA4C,EAAE;UAChD;UACA3E,QAAQ,GAAG7B,MAAM,CAACF,iBAAiB,EAAE0G,4CAA4C,CAAC5I,MAAM,CAAC,GAAG,GAAG,GAAGiE,QAAQ;QAC5G,CAAC,MAAM,IAAIoB,cAAc,EAAE;UACzB;UACApB,QAAQ,GAAG7B,MAAM,CAACF,iBAAiB,EAAEmD,cAAc,CAACrF,MAAM,CAAC,GAAG,IAAI,CAACsF,+BAA+B,CAACT,MAAM,CAAC,GAAGZ,QAAQ;QACvH;MACF;MAEA,IAAImB,aAAa,EAAE;QACjBnB,QAAQ,GAAGlB,gCAAgC,CAACkB,QAAQ,CAAC;MACvD;MAEA,OAAOA,QAAQ;IACjB;EACF,CAAC,EAAE;IACDnC,GAAG,EAAE,gCAAgC;IACrC3B,KAAK,EAAE,SAAS2F,8BAA8BA,CAAC+C,MAAM,EAAE;MACrD,IAAIM,MAAM,GAAG3G,0BAA0B,CAAC,IAAI,CAAC2B,+BAA+B,EAAE,IAAI,CAACC,uCAAuC,EAAEyE,MAAM,CAAC;MAEnI,IAAI,CAACM,MAAM,EAAE;QACX;QACA,IAAI,CAACrF,WAAW,CAAC,CAAC;QAClB;MACF;MAEA,IAAI,CAACK,+BAA+B,GAAGgF,MAAM,CAAC,CAAC,CAAC;MAChD,IAAI,CAAC/E,uCAAuC,GAAG+E,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE1D,OAAO9G,0BAA0B,CAAC,IAAI,CAAC8B,+BAA+B,EAAE,IAAI,CAACC,uCAAuC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC3H;MACA;MACA;IACF;EACF,CAAC,EAAE;IACDtC,GAAG,EAAE,uCAAuC;IAC5C3B,KAAK,EAAE,SAASgF,qCAAqCA,CAACN,MAAM,EAAEuE,KAAK,EAAE;MACnE,IAAIhE,aAAa,GAAGgE,KAAK,CAAChE,aAAa;QACnCC,cAAc,GAAG+D,KAAK,CAAC/D,cAAc;MAEzC,IAAIR,MAAM,CAACmD,4BAA4B,CAAC,CAAC,EAAE;QACzC;QACA;QACA;QACA,IAAIxB,kBAAkB,GAAG3B,MAAM,CAAC2B,kBAAkB,CAAC,CAAC;QAEpD,IAAIA,kBAAkB,IAAInB,cAAc,IAAI,CAACmB,kBAAkB,IAAI,CAACpB,aAAa,EAAE;UACjF,OAAO,IAAI;QACb;MACF;IACF;EACF,CAAC,CAAC,CAAC;EAEH,OAAO1B,kBAAkB;AAC3B,CAAC,CAAC,CAAC;AAEH,SAASA,kBAAkB,IAAI2F,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}