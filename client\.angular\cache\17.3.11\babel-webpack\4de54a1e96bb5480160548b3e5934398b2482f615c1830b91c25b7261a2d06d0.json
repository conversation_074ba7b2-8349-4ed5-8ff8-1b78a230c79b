{"ast": null, "code": "function _createForOfIteratorHelperLoose(o, allowArrayLike) {\n  var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"];\n  if (it) return (it = it.call(o)).next.bind(it);\n  if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") {\n    if (it) o = it;\n    var i = 0;\n    return function () {\n      if (i >= o.length) return {\n        done: true\n      };\n      return {\n        done: false,\n        value: o[i++]\n      };\n    };\n  }\n  throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n  return arr2;\n}\nimport Metadata from '../metadata.js';\nimport matchesEntirely from './matchesEntirely.js';\nvar NON_FIXED_LINE_PHONE_TYPES = ['MOBILE', 'PREMIUM_RATE', 'TOLL_FREE', 'SHARED_COST', 'VOIP', 'PERSONAL_NUMBER', 'PAGER', 'UAN', 'VOICEMAIL']; // Finds out national phone number type (fixed line, mobile, etc)\n\nexport default function getNumberType(input, options, metadata) {\n  // If assigning the `{}` default value is moved to the arguments above,\n  // code coverage would decrease for some weird reason.\n  options = options || {}; // When `parse()` returns an empty object — `{}` —\n  // that means that the phone number is malformed,\n  // so it can't possibly be valid.\n\n  if (!input.country && !input.countryCallingCode) {\n    return;\n  }\n  metadata = new Metadata(metadata);\n  metadata.selectNumberingPlan(input.country, input.countryCallingCode);\n  var nationalNumber = options.v2 ? input.nationalNumber : input.phone; // The following is copy-pasted from the original function:\n  // https://github.com/googlei18n/libphonenumber/blob/3ea547d4fbaa2d0b67588904dfa5d3f2557c27ff/javascript/i18n/phonenumbers/phonenumberutil.js#L2835\n  // Is this national number even valid for this country\n\n  if (!matchesEntirely(nationalNumber, metadata.nationalNumberPattern())) {\n    return;\n  } // Is it fixed line number\n\n  if (isNumberTypeEqualTo(nationalNumber, 'FIXED_LINE', metadata)) {\n    // Because duplicate regular expressions are removed\n    // to reduce metadata size, if \"mobile\" pattern is \"\"\n    // then it means it was removed due to being a duplicate of the fixed-line pattern.\n    //\n    if (metadata.type('MOBILE') && metadata.type('MOBILE').pattern() === '') {\n      return 'FIXED_LINE_OR_MOBILE';\n    } // `MOBILE` type pattern isn't included if it matched `FIXED_LINE` one.\n    // For example, for \"US\" country.\n    // Old metadata (< `1.0.18`) had a specific \"types\" data structure\n    // that happened to be `undefined` for `MOBILE` in that case.\n    // Newer metadata (>= `1.0.18`) has another data structure that is\n    // not `undefined` for `MOBILE` in that case (it's just an empty array).\n    // So this `if` is just for backwards compatibility with old metadata.\n\n    if (!metadata.type('MOBILE')) {\n      return 'FIXED_LINE_OR_MOBILE';\n    } // Check if the number happens to qualify as both fixed line and mobile.\n    // (no such country in the minimal metadata set)\n\n    /* istanbul ignore if */\n\n    if (isNumberTypeEqualTo(nationalNumber, 'MOBILE', metadata)) {\n      return 'FIXED_LINE_OR_MOBILE';\n    }\n    return 'FIXED_LINE';\n  }\n  for (var _iterator = _createForOfIteratorHelperLoose(NON_FIXED_LINE_PHONE_TYPES), _step; !(_step = _iterator()).done;) {\n    var type = _step.value;\n    if (isNumberTypeEqualTo(nationalNumber, type, metadata)) {\n      return type;\n    }\n  }\n}\nexport function isNumberTypeEqualTo(nationalNumber, type, metadata) {\n  type = metadata.type(type);\n  if (!type || !type.pattern()) {\n    return false;\n  } // Check if any possible number lengths are present;\n  // if so, we use them to avoid checking\n  // the validation pattern if they don't match.\n  // If they are absent, this means they match\n  // the general description, which we have\n  // already checked before a specific number type.\n\n  if (type.possibleLengths() && type.possibleLengths().indexOf(nationalNumber.length) < 0) {\n    return false;\n  }\n  return matchesEntirely(nationalNumber, type.pattern());\n}", "map": {"version": 3, "names": ["_createForOfIteratorHelperLoose", "o", "allowArrayLike", "it", "Symbol", "iterator", "call", "next", "bind", "Array", "isArray", "_unsupportedIterableToArray", "length", "i", "done", "value", "TypeError", "minLen", "_arrayLikeToArray", "n", "Object", "prototype", "toString", "slice", "constructor", "name", "from", "test", "arr", "len", "arr2", "<PERSON><PERSON><PERSON>", "matchesEntirely", "NON_FIXED_LINE_PHONE_TYPES", "getNumberType", "input", "options", "metadata", "country", "countryCallingCode", "selectNumberingPlan", "nationalNumber", "v2", "phone", "nationalNumberPattern", "isNumberTypeEqualTo", "type", "pattern", "_iterator", "_step", "possibleLengths", "indexOf"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/helpers/getNumberType.js"], "sourcesContent": ["function _createForOfIteratorHelperLoose(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (it) return (it = it.call(o)).next.bind(it); if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; return function () { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nimport Metadata from '../metadata.js';\nimport matchesEntirely from './matchesEntirely.js';\nvar NON_FIXED_LINE_PHONE_TYPES = ['MOBILE', 'PREMIUM_RATE', 'TOLL_FREE', 'SHARED_COST', 'VOIP', 'PERSONAL_NUMBER', 'PAGER', 'UAN', 'VOICEMAIL']; // Finds out national phone number type (fixed line, mobile, etc)\n\nexport default function getNumberType(input, options, metadata) {\n  // If assigning the `{}` default value is moved to the arguments above,\n  // code coverage would decrease for some weird reason.\n  options = options || {}; // When `parse()` returns an empty object — `{}` —\n  // that means that the phone number is malformed,\n  // so it can't possibly be valid.\n\n  if (!input.country && !input.countryCallingCode) {\n    return;\n  }\n\n  metadata = new Metadata(metadata);\n  metadata.selectNumberingPlan(input.country, input.countryCallingCode);\n  var nationalNumber = options.v2 ? input.nationalNumber : input.phone; // The following is copy-pasted from the original function:\n  // https://github.com/googlei18n/libphonenumber/blob/3ea547d4fbaa2d0b67588904dfa5d3f2557c27ff/javascript/i18n/phonenumbers/phonenumberutil.js#L2835\n  // Is this national number even valid for this country\n\n  if (!matchesEntirely(nationalNumber, metadata.nationalNumberPattern())) {\n    return;\n  } // Is it fixed line number\n\n\n  if (isNumberTypeEqualTo(nationalNumber, 'FIXED_LINE', metadata)) {\n    // Because duplicate regular expressions are removed\n    // to reduce metadata size, if \"mobile\" pattern is \"\"\n    // then it means it was removed due to being a duplicate of the fixed-line pattern.\n    //\n    if (metadata.type('MOBILE') && metadata.type('MOBILE').pattern() === '') {\n      return 'FIXED_LINE_OR_MOBILE';\n    } // `MOBILE` type pattern isn't included if it matched `FIXED_LINE` one.\n    // For example, for \"US\" country.\n    // Old metadata (< `1.0.18`) had a specific \"types\" data structure\n    // that happened to be `undefined` for `MOBILE` in that case.\n    // Newer metadata (>= `1.0.18`) has another data structure that is\n    // not `undefined` for `MOBILE` in that case (it's just an empty array).\n    // So this `if` is just for backwards compatibility with old metadata.\n\n\n    if (!metadata.type('MOBILE')) {\n      return 'FIXED_LINE_OR_MOBILE';\n    } // Check if the number happens to qualify as both fixed line and mobile.\n    // (no such country in the minimal metadata set)\n\n    /* istanbul ignore if */\n\n\n    if (isNumberTypeEqualTo(nationalNumber, 'MOBILE', metadata)) {\n      return 'FIXED_LINE_OR_MOBILE';\n    }\n\n    return 'FIXED_LINE';\n  }\n\n  for (var _iterator = _createForOfIteratorHelperLoose(NON_FIXED_LINE_PHONE_TYPES), _step; !(_step = _iterator()).done;) {\n    var type = _step.value;\n\n    if (isNumberTypeEqualTo(nationalNumber, type, metadata)) {\n      return type;\n    }\n  }\n}\nexport function isNumberTypeEqualTo(nationalNumber, type, metadata) {\n  type = metadata.type(type);\n\n  if (!type || !type.pattern()) {\n    return false;\n  } // Check if any possible number lengths are present;\n  // if so, we use them to avoid checking\n  // the validation pattern if they don't match.\n  // If they are absent, this means they match\n  // the general description, which we have\n  // already checked before a specific number type.\n\n\n  if (type.possibleLengths() && type.possibleLengths().indexOf(nationalNumber.length) < 0) {\n    return false;\n  }\n\n  return matchesEntirely(nationalNumber, type.pattern());\n}\n"], "mappings": "AAAA,SAASA,+BAA+BA,CAACC,CAAC,EAAEC,cAAc,EAAE;EAAE,IAAIC,EAAE,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAIH,CAAC,CAACG,MAAM,CAACC,QAAQ,CAAC,IAAIJ,CAAC,CAAC,YAAY,CAAC;EAAE,IAAIE,EAAE,EAAE,OAAO,CAACA,EAAE,GAAGA,EAAE,CAACG,IAAI,CAACL,CAAC,CAAC,EAAEM,IAAI,CAACC,IAAI,CAACL,EAAE,CAAC;EAAE,IAAIM,KAAK,CAACC,OAAO,CAACT,CAAC,CAAC,KAAKE,EAAE,GAAGQ,2BAA2B,CAACV,CAAC,CAAC,CAAC,IAAIC,cAAc,IAAID,CAAC,IAAI,OAAOA,CAAC,CAACW,MAAM,KAAK,QAAQ,EAAE;IAAE,IAAIT,EAAE,EAAEF,CAAC,GAAGE,EAAE;IAAE,IAAIU,CAAC,GAAG,CAAC;IAAE,OAAO,YAAY;MAAE,IAAIA,CAAC,IAAIZ,CAAC,CAACW,MAAM,EAAE,OAAO;QAAEE,IAAI,EAAE;MAAK,CAAC;MAAE,OAAO;QAAEA,IAAI,EAAE,KAAK;QAAEC,KAAK,EAAEd,CAAC,CAACY,CAAC,EAAE;MAAE,CAAC;IAAE,CAAC;EAAE;EAAE,MAAM,IAAIG,SAAS,CAAC,uIAAuI,CAAC;AAAE;AAE3lB,SAASL,2BAA2BA,CAACV,CAAC,EAAEgB,MAAM,EAAE;EAAE,IAAI,CAAChB,CAAC,EAAE;EAAQ,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOiB,iBAAiB,CAACjB,CAAC,EAAEgB,MAAM,CAAC;EAAE,IAAIE,CAAC,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAAChB,IAAI,CAACL,CAAC,CAAC,CAACsB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAE,IAAIJ,CAAC,KAAK,QAAQ,IAAIlB,CAAC,CAACuB,WAAW,EAAEL,CAAC,GAAGlB,CAAC,CAACuB,WAAW,CAACC,IAAI;EAAE,IAAIN,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOV,KAAK,CAACiB,IAAI,CAACzB,CAAC,CAAC;EAAE,IAAIkB,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACQ,IAAI,CAACR,CAAC,CAAC,EAAE,OAAOD,iBAAiB,CAACjB,CAAC,EAAEgB,MAAM,CAAC;AAAE;AAE/Z,SAASC,iBAAiBA,CAACU,GAAG,EAAEC,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGD,GAAG,CAAChB,MAAM,EAAEiB,GAAG,GAAGD,GAAG,CAAChB,MAAM;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEiB,IAAI,GAAG,IAAIrB,KAAK,CAACoB,GAAG,CAAC,EAAEhB,CAAC,GAAGgB,GAAG,EAAEhB,CAAC,EAAE,EAAE;IAAEiB,IAAI,CAACjB,CAAC,CAAC,GAAGe,GAAG,CAACf,CAAC,CAAC;EAAE;EAAE,OAAOiB,IAAI;AAAE;AAEtL,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,eAAe,MAAM,sBAAsB;AAClD,IAAIC,0BAA0B,GAAG,CAAC,QAAQ,EAAE,cAAc,EAAE,WAAW,EAAE,aAAa,EAAE,MAAM,EAAE,iBAAiB,EAAE,OAAO,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC,CAAC;;AAEjJ,eAAe,SAASC,aAAaA,CAACC,KAAK,EAAEC,OAAO,EAAEC,QAAQ,EAAE;EAC9D;EACA;EACAD,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC;EACzB;EACA;;EAEA,IAAI,CAACD,KAAK,CAACG,OAAO,IAAI,CAACH,KAAK,CAACI,kBAAkB,EAAE;IAC/C;EACF;EAEAF,QAAQ,GAAG,IAAIN,QAAQ,CAACM,QAAQ,CAAC;EACjCA,QAAQ,CAACG,mBAAmB,CAACL,KAAK,CAACG,OAAO,EAAEH,KAAK,CAACI,kBAAkB,CAAC;EACrE,IAAIE,cAAc,GAAGL,OAAO,CAACM,EAAE,GAAGP,KAAK,CAACM,cAAc,GAAGN,KAAK,CAACQ,KAAK,CAAC,CAAC;EACtE;EACA;;EAEA,IAAI,CAACX,eAAe,CAACS,cAAc,EAAEJ,QAAQ,CAACO,qBAAqB,CAAC,CAAC,CAAC,EAAE;IACtE;EACF,CAAC,CAAC;;EAGF,IAAIC,mBAAmB,CAACJ,cAAc,EAAE,YAAY,EAAEJ,QAAQ,CAAC,EAAE;IAC/D;IACA;IACA;IACA;IACA,IAAIA,QAAQ,CAACS,IAAI,CAAC,QAAQ,CAAC,IAAIT,QAAQ,CAACS,IAAI,CAAC,QAAQ,CAAC,CAACC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;MACvE,OAAO,sBAAsB;IAC/B,CAAC,CAAC;IACF;IACA;IACA;IACA;IACA;IACA;;IAGA,IAAI,CAACV,QAAQ,CAACS,IAAI,CAAC,QAAQ,CAAC,EAAE;MAC5B,OAAO,sBAAsB;IAC/B,CAAC,CAAC;IACF;;IAEA;;IAGA,IAAID,mBAAmB,CAACJ,cAAc,EAAE,QAAQ,EAAEJ,QAAQ,CAAC,EAAE;MAC3D,OAAO,sBAAsB;IAC/B;IAEA,OAAO,YAAY;EACrB;EAEA,KAAK,IAAIW,SAAS,GAAGhD,+BAA+B,CAACiC,0BAA0B,CAAC,EAAEgB,KAAK,EAAE,CAAC,CAACA,KAAK,GAAGD,SAAS,CAAC,CAAC,EAAElC,IAAI,GAAG;IACrH,IAAIgC,IAAI,GAAGG,KAAK,CAAClC,KAAK;IAEtB,IAAI8B,mBAAmB,CAACJ,cAAc,EAAEK,IAAI,EAAET,QAAQ,CAAC,EAAE;MACvD,OAAOS,IAAI;IACb;EACF;AACF;AACA,OAAO,SAASD,mBAAmBA,CAACJ,cAAc,EAAEK,IAAI,EAAET,QAAQ,EAAE;EAClES,IAAI,GAAGT,QAAQ,CAACS,IAAI,CAACA,IAAI,CAAC;EAE1B,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAACC,OAAO,CAAC,CAAC,EAAE;IAC5B,OAAO,KAAK;EACd,CAAC,CAAC;EACF;EACA;EACA;EACA;EACA;;EAGA,IAAID,IAAI,CAACI,eAAe,CAAC,CAAC,IAAIJ,IAAI,CAACI,eAAe,CAAC,CAAC,CAACC,OAAO,CAACV,cAAc,CAAC7B,MAAM,CAAC,GAAG,CAAC,EAAE;IACvF,OAAO,KAAK;EACd;EAEA,OAAOoB,eAAe,CAACS,cAAc,EAAEK,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC;AACxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}