{"ast": null, "code": "// Deprecated.\n\nimport withMetadataArgument from '../min/exports/withMetadataArgument.js';\nimport _isPossibleNumber from '../es6/legacy/isPossibleNumber.js';\nexport function isPossibleNumber() {\n  return withMetadataArgument(_isPossibleNumber, arguments);\n}", "map": {"version": 3, "names": ["withMetadataArgument", "_isPossibleNumber", "isPossibleNumber", "arguments"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/index.es6.exports/isPossibleNumber.js"], "sourcesContent": ["// Deprecated.\r\n\r\nimport withMetadataArgument from '../min/exports/withMetadataArgument.js'\r\n\r\nimport _isPossibleNumber from '../es6/legacy/isPossibleNumber.js'\r\n\r\nexport function isPossibleNumber() {\r\n\treturn withMetadataArgument(_isPossibleNumber, arguments)\r\n}\r\n"], "mappings": "AAAA;;AAEA,OAAOA,oBAAoB,MAAM,wCAAwC;AAEzE,OAAOC,iBAAiB,MAAM,mCAAmC;AAEjE,OAAO,SAASC,gBAAgBA,CAAA,EAAG;EAClC,OAAOF,oBAAoB,CAACC,iBAAiB,EAAEE,SAAS,CAAC;AAC1D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}