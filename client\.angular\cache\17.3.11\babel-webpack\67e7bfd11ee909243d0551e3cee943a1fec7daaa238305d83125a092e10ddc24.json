{"ast": null, "code": "import createExtensionPattern from './createExtensionPattern.js'; // Regexp of all known extension prefixes used by different regions followed by\n// 1 or more valid digits, for use when parsing.\n\nvar EXTN_PATTERN = new RegExp('(?:' + createExtensionPattern() + ')$', 'i'); // Strips any extension (as in, the part of the number dialled after the call is\n// connected, usually indicated with extn, ext, x or similar) from the end of\n// the number, and returns it.\n\nexport default function extractExtension(number) {\n  var start = number.search(EXTN_PATTERN);\n  if (start < 0) {\n    return {};\n  } // If we find a potential extension, and the number preceding this is a viable\n  // number, we assume it is an extension.\n\n  var numberWithoutExtension = number.slice(0, start);\n  var matches = number.match(EXTN_PATTERN);\n  var i = 1;\n  while (i < matches.length) {\n    if (matches[i]) {\n      return {\n        number: numberWithoutExtension,\n        ext: matches[i]\n      };\n    }\n    i++;\n  }\n}", "map": {"version": 3, "names": ["createExtensionPattern", "EXTN_PATTERN", "RegExp", "extractExtension", "number", "start", "search", "numberWithoutExtension", "slice", "matches", "match", "i", "length", "ext"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/helpers/extension/extractExtension.js"], "sourcesContent": ["import createExtensionPattern from './createExtensionPattern.js'; // Regexp of all known extension prefixes used by different regions followed by\n// 1 or more valid digits, for use when parsing.\n\nvar EXTN_PATTERN = new RegExp('(?:' + createExtensionPattern() + ')$', 'i'); // Strips any extension (as in, the part of the number dialled after the call is\n// connected, usually indicated with extn, ext, x or similar) from the end of\n// the number, and returns it.\n\nexport default function extractExtension(number) {\n  var start = number.search(EXTN_PATTERN);\n\n  if (start < 0) {\n    return {};\n  } // If we find a potential extension, and the number preceding this is a viable\n  // number, we assume it is an extension.\n\n\n  var numberWithoutExtension = number.slice(0, start);\n  var matches = number.match(EXTN_PATTERN);\n  var i = 1;\n\n  while (i < matches.length) {\n    if (matches[i]) {\n      return {\n        number: numberWithoutExtension,\n        ext: matches[i]\n      };\n    }\n\n    i++;\n  }\n}\n"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,6BAA6B,CAAC,CAAC;AAClE;;AAEA,IAAIC,YAAY,GAAG,IAAIC,MAAM,CAAC,KAAK,GAAGF,sBAAsB,CAAC,CAAC,GAAG,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;AAC7E;AACA;;AAEA,eAAe,SAASG,gBAAgBA,CAACC,MAAM,EAAE;EAC/C,IAAIC,KAAK,GAAGD,MAAM,CAACE,MAAM,CAACL,YAAY,CAAC;EAEvC,IAAII,KAAK,GAAG,CAAC,EAAE;IACb,OAAO,CAAC,CAAC;EACX,CAAC,CAAC;EACF;;EAGA,IAAIE,sBAAsB,GAAGH,MAAM,CAACI,KAAK,CAAC,CAAC,EAAEH,KAAK,CAAC;EACnD,IAAII,OAAO,GAAGL,MAAM,CAACM,KAAK,CAACT,YAAY,CAAC;EACxC,IAAIU,CAAC,GAAG,CAAC;EAET,OAAOA,CAAC,GAAGF,OAAO,CAACG,MAAM,EAAE;IACzB,IAAIH,OAAO,CAACE,CAAC,CAAC,EAAE;MACd,OAAO;QACLP,MAAM,EAAEG,sBAAsB;QAC9BM,GAAG,EAAEJ,OAAO,CAACE,CAAC;MAChB,CAAC;IACH;IAEAA,CAAC,EAAE;EACL;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}