{"ast": null, "code": "import getCountryByNationalNumber from './getCountryByNationalNumber.js';\nvar USE_NON_GEOGRAPHIC_COUNTRY_CODE = false;\nexport default function getCountryByCallingCode(callingCode, _ref) {\n  var nationalPhoneNumber = _ref.nationalNumber,\n    defaultCountry = _ref.defaultCountry,\n    metadata = _ref.metadata;\n\n  /* istanbul ignore if */\n  if (USE_NON_GEOGRAPHIC_COUNTRY_CODE) {\n    if (metadata.isNonGeographicCallingCode(callingCode)) {\n      return '001';\n    }\n  }\n  var possibleCountries = metadata.getCountryCodesForCallingCode(callingCode);\n  if (!possibleCountries) {\n    return;\n  } // If there's just one country corresponding to the country code,\n  // then just return it, without further phone number digits validation.\n\n  if (possibleCountries.length === 1) {\n    return possibleCountries[0];\n  }\n  return getCountryByNationalNumber(nationalPhoneNumber, {\n    countries: possibleCountries,\n    defaultCountry: defaultCountry,\n    metadata: metadata.metadata\n  });\n}", "map": {"version": 3, "names": ["getCountryByNationalNumber", "USE_NON_GEOGRAPHIC_COUNTRY_CODE", "getCountryByCallingCode", "callingCode", "_ref", "nationalPhoneNumber", "nationalNumber", "defaultCountry", "metadata", "isNonGeographicCallingCode", "possibleCountries", "getCountryCodesForCallingCode", "length", "countries"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/helpers/getCountryByCallingCode.js"], "sourcesContent": ["import getCountryByNationalNumber from './getCountryByNationalNumber.js';\nvar USE_NON_GEOGRAPHIC_COUNTRY_CODE = false;\nexport default function getCountryByCallingCode(callingCode, _ref) {\n  var nationalPhoneNumber = _ref.nationalNumber,\n      defaultCountry = _ref.defaultCountry,\n      metadata = _ref.metadata;\n\n  /* istanbul ignore if */\n  if (USE_NON_GEOGRAPHIC_COUNTRY_CODE) {\n    if (metadata.isNonGeographicCallingCode(callingCode)) {\n      return '001';\n    }\n  }\n\n  var possibleCountries = metadata.getCountryCodesForCallingCode(callingCode);\n\n  if (!possibleCountries) {\n    return;\n  } // If there's just one country corresponding to the country code,\n  // then just return it, without further phone number digits validation.\n\n\n  if (possibleCountries.length === 1) {\n    return possibleCountries[0];\n  }\n\n  return getCountryByNationalNumber(nationalPhoneNumber, {\n    countries: possibleCountries,\n    defaultCountry: defaultCountry,\n    metadata: metadata.metadata\n  });\n}\n"], "mappings": "AAAA,OAAOA,0BAA0B,MAAM,iCAAiC;AACxE,IAAIC,+BAA+B,GAAG,KAAK;AAC3C,eAAe,SAASC,uBAAuBA,CAACC,WAAW,EAAEC,IAAI,EAAE;EACjE,IAAIC,mBAAmB,GAAGD,IAAI,CAACE,cAAc;IACzCC,cAAc,GAAGH,IAAI,CAACG,cAAc;IACpCC,QAAQ,GAAGJ,IAAI,CAACI,QAAQ;;EAE5B;EACA,IAAIP,+BAA+B,EAAE;IACnC,IAAIO,QAAQ,CAACC,0BAA0B,CAACN,WAAW,CAAC,EAAE;MACpD,OAAO,KAAK;IACd;EACF;EAEA,IAAIO,iBAAiB,GAAGF,QAAQ,CAACG,6BAA6B,CAACR,WAAW,CAAC;EAE3E,IAAI,CAACO,iBAAiB,EAAE;IACtB;EACF,CAAC,CAAC;EACF;;EAGA,IAAIA,iBAAiB,CAACE,MAAM,KAAK,CAAC,EAAE;IAClC,OAAOF,iBAAiB,CAAC,CAAC,CAAC;EAC7B;EAEA,OAAOV,0BAA0B,CAACK,mBAAmB,EAAE;IACrDQ,SAAS,EAAEH,iBAAiB;IAC5BH,cAAc,EAAEA,cAAc;IAC9BC,QAAQ,EAAEA,QAAQ,CAACA;EACrB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}