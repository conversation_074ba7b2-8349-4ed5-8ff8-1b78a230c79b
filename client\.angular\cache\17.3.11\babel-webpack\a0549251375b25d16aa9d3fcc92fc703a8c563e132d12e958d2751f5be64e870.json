{"ast": null, "code": "import withMetadataArgument from './withMetadataArgument.js';\nimport { isValidPhoneNumber as _isValidPhoneNumber } from '../../core/index.js';\nexport function isValidPhoneNumber() {\n  return withMetadataArgument(_isValidPhoneNumber, arguments);\n}", "map": {"version": 3, "names": ["withMetadataArgument", "isValidPhoneNumber", "_isValidPhoneNumber", "arguments"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/min/exports/isValidPhoneNumber.js"], "sourcesContent": ["import withMetadataArgument from './withMetadataArgument.js'\r\nimport { isValidPhoneNumber as _isValidPhoneNumber } from '../../core/index.js'\r\n\r\nexport function isValidPhoneNumber() {\r\n\treturn withMetadataArgument(_isValidPhoneNumber, arguments)\r\n}"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,2BAA2B;AAC5D,SAASC,kBAAkB,IAAIC,mBAAmB,QAAQ,qBAAqB;AAE/E,OAAO,SAASD,kBAAkBA,CAAA,EAAG;EACpC,OAAOD,oBAAoB,CAACE,mBAAmB,EAAEC,SAAS,CAAC;AAC5D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}