{"version": 3, "file": "parsePhoneNumberWithError.test.js", "names": ["_parsePhoneNumber", "metadata", "type", "metadataFull", "parsePhoneNumber", "parameters", "push", "apply", "parsePhoneNumberFull", "describe", "it", "phoneNumber", "country", "should", "equal", "countryCallingCode", "nationalNumber", "number", "isPossible", "<PERSON><PERSON><PERSON><PERSON>", "getType", "expect", "to", "be", "undefined", "carrierCode", "ext", "format", "formatNational", "formatInternational", "getURI"], "sources": ["../source/parsePhoneNumberWithError.test.js"], "sourcesContent": ["import _parsePhoneNumber from './parsePhoneNumberWithError.js'\r\nimport metadata from '../metadata.min.json' assert { type: 'json' }\r\nimport metadataFull from '../metadata.max.json' assert { type: 'json' }\r\n\r\nfunction parsePhoneNumber(...parameters) {\r\n\tparameters.push(metadata)\r\n\treturn _parsePhoneNumber.apply(this, parameters)\r\n}\r\n\r\nfunction parsePhoneNumberFull(...parameters) {\r\n\tparameters.push(metadataFull)\r\n\treturn _parsePhoneNumber.apply(this, parameters)\r\n}\r\n\r\ndescribe('parsePhoneNumberWithError', () => {\r\n\tit('should parse phone numbers', () => {\r\n\t\tconst phoneNumber = parsePhoneNumber('The phone number is: 8 (800) 555 35 35. Some other text.', 'RU')\r\n\t\tphoneNumber.country.should.equal('RU')\r\n\t\tphoneNumber.countryCallingCode.should.equal('7')\r\n\t\tphoneNumber.nationalNumber.should.equal('8005553535')\r\n\t\tphoneNumber.number.should.equal('+78005553535')\r\n\t\tphoneNumber.isPossible().should.equal(true)\r\n\t\tphoneNumber.isValid().should.equal(true)\r\n\t\t// phoneNumber.isValidForRegion('RU').should.equal(true)\r\n\t\t// Russian phone type regexps aren't included in default metadata.\r\n\t\tparsePhoneNumberFull('Phone: 8 (800) 555 35 35.', 'RU').getType().should.equal('TOLL_FREE')\r\n\t})\r\n\r\n\tit('shouldn\\'t set country when it\\'s non-derivable', () => {\r\n\t\tconst phoneNumber = parsePhoneNumber('****** 555 35 35')\r\n\t\texpect(phoneNumber.country).to.be.undefined\r\n\t\tphoneNumber.countryCallingCode.should.equal('7')\r\n\t\tphoneNumber.nationalNumber.should.equal('1115553535')\r\n\t})\r\n\r\n\tit('should parse carrier code', () => {\r\n\t\tconst phoneNumber = parsePhoneNumber('0 15 21 5555-5555', 'BR')\r\n\t\tphoneNumber.carrierCode.should.equal('15')\r\n\t})\r\n\r\n\tit('should parse phone extension', () => {\r\n\t\tconst phoneNumber = parsePhoneNumber('Phone: 8 (800) 555 35 35 ext. 1234.', 'RU')\r\n\t\tphoneNumber.ext.should.equal('1234')\r\n\t})\r\n\r\n\tit('should validate numbers for countries with no type regular expressions', () => {\r\n\t\tparsePhoneNumber('+380391234567').isValid().should.equal(true)\r\n\t\tparsePhoneNumber('+380191234567').isValid().should.equal(false)\r\n\t})\r\n\r\n\tit('should format numbers', () => {\r\n\t\tconst phoneNumber = parsePhoneNumber('Phone: 8 (800) 555 35 35.', 'RU')\r\n\t\tphoneNumber.format('NATIONAL').should.equal('8 (800) 555-35-35')\r\n\t\tphoneNumber.formatNational().should.equal('8 (800) 555-35-35')\r\n\t\tphoneNumber.format('INTERNATIONAL').should.equal('****** 555 35 35')\r\n\t\tphoneNumber.formatInternational().should.equal('****** 555 35 35')\r\n\t})\r\n\r\n\tit('should get tel: URI', () => {\r\n\t\tconst phoneNumber = parsePhoneNumber('Phone: 8 (800) 555 35 35 ext. 1234.', 'RU')\r\n\t\tphoneNumber.getURI().should.equal('tel:+78005553535;ext=1234')\r\n\t})\r\n\r\n\tit('should work in edge cases', () => {\r\n\t\texpect(() => parsePhoneNumber('+78005553535', -1, {})).to.throw('Invalid second argument')\r\n\t})\r\n\r\n\tit('should throw parse errors', () => {\r\n\t\texpect(() => parsePhoneNumber('8005553535', 'XX')).to.throw('INVALID_COUNTRY')\r\n\t\texpect(() => parsePhoneNumber('+', 'RU')).to.throw('NOT_A_NUMBER')\r\n\t\texpect(() => parsePhoneNumber('a', 'RU')).to.throw('NOT_A_NUMBER')\r\n\t\texpect(() => parsePhoneNumber('1', 'RU')).to.throw('TOO_SHORT')\r\n\t\texpect(() => parsePhoneNumber('+4')).to.throw('TOO_SHORT')\r\n\t\texpect(() => parsePhoneNumber('+44')).to.throw('TOO_SHORT')\r\n\t\texpect(() => parsePhoneNumber('+443')).to.throw('TOO_SHORT')\r\n\t\texpect(() => parsePhoneNumber('+370')).to.throw('TOO_SHORT')\r\n\t\texpect(() => parsePhoneNumber('88888888888888888888', 'RU')).to.throw('TOO_LONG')\r\n\t\texpect(() => parsePhoneNumber('8 (800) 555 35 35')).to.throw('INVALID_COUNTRY')\r\n\t\texpect(() => parsePhoneNumber('+9991112233')).to.throw('INVALID_COUNTRY')\r\n\t\texpect(() => parsePhoneNumber('+9991112233', 'US')).to.throw('INVALID_COUNTRY')\r\n\t\texpect(() => parsePhoneNumber('8005553535                                                                                                                                                                                                                                                 ', 'RU')).to.throw('TOO_LONG')\r\n\t})\r\n\r\n\tit('should parse incorrect international phone numbers', () => {\r\n\t\t// Parsing national prefixes and carrier codes\r\n\t\t// is only required for local phone numbers\r\n\t\t// but some people don't understand that\r\n\t\t// and sometimes write international phone numbers\r\n\t\t// with national prefixes (or maybe even carrier codes).\r\n\t\t// http://ucken.blogspot.ru/2016/03/trunk-prefixes-in-skype4b.html\r\n\t\t// Google's original library forgives such mistakes\r\n\t\t// and so does this library, because it has been requested:\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/127\r\n\r\n\t\tlet phoneNumber\r\n\r\n\t\t// For complete numbers it should strip national prefix.\r\n\t\tphoneNumber = parsePhoneNumber('******* 215 5230')\r\n\t\tphoneNumber.nationalNumber.should.equal('8772155230')\r\n\t\tphoneNumber.country.should.equal('US')\r\n\r\n\t\t// For complete numbers it should strip national prefix.\r\n\t\tphoneNumber = parsePhoneNumber('******* 555 3535')\r\n\t\tphoneNumber.nationalNumber.should.equal('8005553535')\r\n\t\tphoneNumber.country.should.equal('RU')\r\n\r\n\t\t// For incomplete numbers it shouldn't strip national prefix.\r\n\t\tphoneNumber = parsePhoneNumber('******* 555 353')\r\n\t\tphoneNumber.nationalNumber.should.equal('8800555353')\r\n\t\tphoneNumber.country.should.equal('RU')\r\n\t})\r\n})\r\n"], "mappings": "AAAA,OAAOA,iBAAP,MAA8B,gCAA9B;AACA,OAAOC,QAAP,MAAqB,sBAArB,UAAqDC,IAAI,EAAE,MAA3D;AACA,OAAOC,YAAP,MAAyB,sBAAzB,UAAyDD,IAAI,EAAE,MAA/D;;AAEA,SAASE,gBAAT,GAAyC;EAAA,kCAAZC,UAAY;IAAZA,UAAY;EAAA;;EACxCA,UAAU,CAACC,IAAX,CAAgBL,QAAhB;EACA,OAAOD,iBAAiB,CAACO,KAAlB,CAAwB,IAAxB,EAA8BF,UAA9B,CAAP;AACA;;AAED,SAASG,oBAAT,GAA6C;EAAA,mCAAZH,UAAY;IAAZA,UAAY;EAAA;;EAC5CA,UAAU,CAACC,IAAX,CAAgBH,YAAhB;EACA,OAAOH,iBAAiB,CAACO,KAAlB,CAAwB,IAAxB,EAA8BF,UAA9B,CAAP;AACA;;AAEDI,QAAQ,CAAC,2BAAD,EAA8B,YAAM;EAC3CC,EAAE,CAAC,4BAAD,EAA+B,YAAM;IACtC,IAAMC,WAAW,GAAGP,gBAAgB,CAAC,0DAAD,EAA6D,IAA7D,CAApC;IACAO,WAAW,CAACC,OAAZ,CAAoBC,MAApB,CAA2BC,KAA3B,CAAiC,IAAjC;IACAH,WAAW,CAACI,kBAAZ,CAA+BF,MAA/B,CAAsCC,KAAtC,CAA4C,GAA5C;IACAH,WAAW,CAACK,cAAZ,CAA2BH,MAA3B,CAAkCC,KAAlC,CAAwC,YAAxC;IACAH,WAAW,CAACM,MAAZ,CAAmBJ,MAAnB,CAA0BC,KAA1B,CAAgC,cAAhC;IACAH,WAAW,CAACO,UAAZ,GAAyBL,MAAzB,CAAgCC,KAAhC,CAAsC,IAAtC;IACAH,WAAW,CAACQ,OAAZ,GAAsBN,MAAtB,CAA6BC,KAA7B,CAAmC,IAAnC,EAPsC,CAQtC;IACA;;IACAN,oBAAoB,CAAC,2BAAD,EAA8B,IAA9B,CAApB,CAAwDY,OAAxD,GAAkEP,MAAlE,CAAyEC,KAAzE,CAA+E,WAA/E;EACA,CAXC,CAAF;EAaAJ,EAAE,CAAC,iDAAD,EAAoD,YAAM;IAC3D,IAAMC,WAAW,GAAGP,gBAAgB,CAAC,kBAAD,CAApC;IACAiB,MAAM,CAACV,WAAW,CAACC,OAAb,CAAN,CAA4BU,EAA5B,CAA+BC,EAA/B,CAAkCC,SAAlC;IACAb,WAAW,CAACI,kBAAZ,CAA+BF,MAA/B,CAAsCC,KAAtC,CAA4C,GAA5C;IACAH,WAAW,CAACK,cAAZ,CAA2BH,MAA3B,CAAkCC,KAAlC,CAAwC,YAAxC;EACA,CALC,CAAF;EAOAJ,EAAE,CAAC,2BAAD,EAA8B,YAAM;IACrC,IAAMC,WAAW,GAAGP,gBAAgB,CAAC,mBAAD,EAAsB,IAAtB,CAApC;IACAO,WAAW,CAACc,WAAZ,CAAwBZ,MAAxB,CAA+BC,KAA/B,CAAqC,IAArC;EACA,CAHC,CAAF;EAKAJ,EAAE,CAAC,8BAAD,EAAiC,YAAM;IACxC,IAAMC,WAAW,GAAGP,gBAAgB,CAAC,qCAAD,EAAwC,IAAxC,CAApC;IACAO,WAAW,CAACe,GAAZ,CAAgBb,MAAhB,CAAuBC,KAAvB,CAA6B,MAA7B;EACA,CAHC,CAAF;EAKAJ,EAAE,CAAC,wEAAD,EAA2E,YAAM;IAClFN,gBAAgB,CAAC,eAAD,CAAhB,CAAkCe,OAAlC,GAA4CN,MAA5C,CAAmDC,KAAnD,CAAyD,IAAzD;IACAV,gBAAgB,CAAC,eAAD,CAAhB,CAAkCe,OAAlC,GAA4CN,MAA5C,CAAmDC,KAAnD,CAAyD,KAAzD;EACA,CAHC,CAAF;EAKAJ,EAAE,CAAC,uBAAD,EAA0B,YAAM;IACjC,IAAMC,WAAW,GAAGP,gBAAgB,CAAC,2BAAD,EAA8B,IAA9B,CAApC;IACAO,WAAW,CAACgB,MAAZ,CAAmB,UAAnB,EAA+Bd,MAA/B,CAAsCC,KAAtC,CAA4C,mBAA5C;IACAH,WAAW,CAACiB,cAAZ,GAA6Bf,MAA7B,CAAoCC,KAApC,CAA0C,mBAA1C;IACAH,WAAW,CAACgB,MAAZ,CAAmB,eAAnB,EAAoCd,MAApC,CAA2CC,KAA3C,CAAiD,kBAAjD;IACAH,WAAW,CAACkB,mBAAZ,GAAkChB,MAAlC,CAAyCC,KAAzC,CAA+C,kBAA/C;EACA,CANC,CAAF;EAQAJ,EAAE,CAAC,qBAAD,EAAwB,YAAM;IAC/B,IAAMC,WAAW,GAAGP,gBAAgB,CAAC,qCAAD,EAAwC,IAAxC,CAApC;IACAO,WAAW,CAACmB,MAAZ,GAAqBjB,MAArB,CAA4BC,KAA5B,CAAkC,2BAAlC;EACA,CAHC,CAAF;EAKAJ,EAAE,CAAC,2BAAD,EAA8B,YAAM;IACrCW,MAAM,CAAC;MAAA,OAAMjB,gBAAgB,CAAC,cAAD,EAAiB,CAAC,CAAlB,EAAqB,EAArB,CAAtB;IAAA,CAAD,CAAN,CAAuDkB,EAAvD,UAAgE,yBAAhE;EACA,CAFC,CAAF;EAIAZ,EAAE,CAAC,2BAAD,EAA8B,YAAM;IACrCW,MAAM,CAAC;MAAA,OAAMjB,gBAAgB,CAAC,YAAD,EAAe,IAAf,CAAtB;IAAA,CAAD,CAAN,CAAmDkB,EAAnD,UAA4D,iBAA5D;IACAD,MAAM,CAAC;MAAA,OAAMjB,gBAAgB,CAAC,GAAD,EAAM,IAAN,CAAtB;IAAA,CAAD,CAAN,CAA0CkB,EAA1C,UAAmD,cAAnD;IACAD,MAAM,CAAC;MAAA,OAAMjB,gBAAgB,CAAC,GAAD,EAAM,IAAN,CAAtB;IAAA,CAAD,CAAN,CAA0CkB,EAA1C,UAAmD,cAAnD;IACAD,MAAM,CAAC;MAAA,OAAMjB,gBAAgB,CAAC,GAAD,EAAM,IAAN,CAAtB;IAAA,CAAD,CAAN,CAA0CkB,EAA1C,UAAmD,WAAnD;IACAD,MAAM,CAAC;MAAA,OAAMjB,gBAAgB,CAAC,IAAD,CAAtB;IAAA,CAAD,CAAN,CAAqCkB,EAArC,UAA8C,WAA9C;IACAD,MAAM,CAAC;MAAA,OAAMjB,gBAAgB,CAAC,KAAD,CAAtB;IAAA,CAAD,CAAN,CAAsCkB,EAAtC,UAA+C,WAA/C;IACAD,MAAM,CAAC;MAAA,OAAMjB,gBAAgB,CAAC,MAAD,CAAtB;IAAA,CAAD,CAAN,CAAuCkB,EAAvC,UAAgD,WAAhD;IACAD,MAAM,CAAC;MAAA,OAAMjB,gBAAgB,CAAC,MAAD,CAAtB;IAAA,CAAD,CAAN,CAAuCkB,EAAvC,UAAgD,WAAhD;IACAD,MAAM,CAAC;MAAA,OAAMjB,gBAAgB,CAAC,sBAAD,EAAyB,IAAzB,CAAtB;IAAA,CAAD,CAAN,CAA6DkB,EAA7D,UAAsE,UAAtE;IACAD,MAAM,CAAC;MAAA,OAAMjB,gBAAgB,CAAC,mBAAD,CAAtB;IAAA,CAAD,CAAN,CAAoDkB,EAApD,UAA6D,iBAA7D;IACAD,MAAM,CAAC;MAAA,OAAMjB,gBAAgB,CAAC,aAAD,CAAtB;IAAA,CAAD,CAAN,CAA8CkB,EAA9C,UAAuD,iBAAvD;IACAD,MAAM,CAAC;MAAA,OAAMjB,gBAAgB,CAAC,aAAD,EAAgB,IAAhB,CAAtB;IAAA,CAAD,CAAN,CAAoDkB,EAApD,UAA6D,iBAA7D;IACAD,MAAM,CAAC;MAAA,OAAMjB,gBAAgB,CAAC,6PAAD,EAAgQ,IAAhQ,CAAtB;IAAA,CAAD,CAAN,CAAoSkB,EAApS,UAA6S,UAA7S;EACA,CAdC,CAAF;EAgBAZ,EAAE,CAAC,oDAAD,EAAuD,YAAM;IAC9D;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA,IAAIC,WAAJ,CAX8D,CAa9D;;IACAA,WAAW,GAAGP,gBAAgB,CAAC,kBAAD,CAA9B;IACAO,WAAW,CAACK,cAAZ,CAA2BH,MAA3B,CAAkCC,KAAlC,CAAwC,YAAxC;IACAH,WAAW,CAACC,OAAZ,CAAoBC,MAApB,CAA2BC,KAA3B,CAAiC,IAAjC,EAhB8D,CAkB9D;;IACAH,WAAW,GAAGP,gBAAgB,CAAC,kBAAD,CAA9B;IACAO,WAAW,CAACK,cAAZ,CAA2BH,MAA3B,CAAkCC,KAAlC,CAAwC,YAAxC;IACAH,WAAW,CAACC,OAAZ,CAAoBC,MAApB,CAA2BC,KAA3B,CAAiC,IAAjC,EArB8D,CAuB9D;;IACAH,WAAW,GAAGP,gBAAgB,CAAC,iBAAD,CAA9B;IACAO,WAAW,CAACK,cAAZ,CAA2BH,MAA3B,CAAkCC,KAAlC,CAAwC,YAAxC;IACAH,WAAW,CAACC,OAAZ,CAAoBC,MAApB,CAA2BC,KAA3B,CAAiC,IAAjC;EACA,CA3BC,CAAF;AA4BA,CAjGO,CAAR"}