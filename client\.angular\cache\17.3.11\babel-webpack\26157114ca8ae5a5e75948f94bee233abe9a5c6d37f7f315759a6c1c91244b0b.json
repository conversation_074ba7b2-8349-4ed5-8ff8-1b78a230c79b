{"ast": null, "code": "function _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n  return arr2;\n}\nfunction _iterableToArrayLimit(arr, i) {\n  var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n  if (_i == null) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n  var _s, _e;\n  try {\n    for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n  return _arr;\n}\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nimport _extractCountryCallingCode from './helpers/extractCountryCallingCode.js';\nimport extractCountryCallingCodeFromInternationalNumberWithoutPlusSign from './helpers/extractCountryCallingCodeFromInternationalNumberWithoutPlusSign.js';\nimport extractNationalNumberFromPossiblyIncompleteNumber from './helpers/extractNationalNumberFromPossiblyIncompleteNumber.js';\nimport stripIddPrefix from './helpers/stripIddPrefix.js';\nimport parseDigits from './helpers/parseDigits.js';\nimport { VALID_DIGITS, VALID_PUNCTUATION, PLUS_CHARS } from './constants.js';\nvar VALID_FORMATTED_PHONE_NUMBER_DIGITS_PART = '[' + VALID_PUNCTUATION + VALID_DIGITS + ']+';\nvar VALID_FORMATTED_PHONE_NUMBER_DIGITS_PART_PATTERN = new RegExp('^' + VALID_FORMATTED_PHONE_NUMBER_DIGITS_PART + '$', 'i');\nvar VALID_FORMATTED_PHONE_NUMBER_PART = '(?:' + '[' + PLUS_CHARS + ']' + '[' + VALID_PUNCTUATION + VALID_DIGITS + ']*' + '|' + '[' + VALID_PUNCTUATION + VALID_DIGITS + ']+' + ')';\nvar AFTER_PHONE_NUMBER_DIGITS_END_PATTERN = new RegExp('[^' + VALID_PUNCTUATION + VALID_DIGITS + ']+' + '.*' + '$'); // Tests whether `national_prefix_for_parsing` could match\n// different national prefixes.\n// Matches anything that's not a digit or a square bracket.\n\nvar COMPLEX_NATIONAL_PREFIX = /[^\\d\\[\\]]/;\nvar AsYouTypeParser = /*#__PURE__*/function () {\n  function AsYouTypeParser(_ref) {\n    var defaultCountry = _ref.defaultCountry,\n      defaultCallingCode = _ref.defaultCallingCode,\n      metadata = _ref.metadata,\n      onNationalSignificantNumberChange = _ref.onNationalSignificantNumberChange;\n    _classCallCheck(this, AsYouTypeParser);\n    this.defaultCountry = defaultCountry;\n    this.defaultCallingCode = defaultCallingCode;\n    this.metadata = metadata;\n    this.onNationalSignificantNumberChange = onNationalSignificantNumberChange;\n  }\n  _createClass(AsYouTypeParser, [{\n    key: \"input\",\n    value: function input(text, state) {\n      var _extractFormattedDigi = extractFormattedDigitsAndPlus(text),\n        _extractFormattedDigi2 = _slicedToArray(_extractFormattedDigi, 2),\n        formattedDigits = _extractFormattedDigi2[0],\n        hasPlus = _extractFormattedDigi2[1];\n      var digits = parseDigits(formattedDigits); // Checks for a special case: just a leading `+` has been entered.\n\n      var justLeadingPlus;\n      if (hasPlus) {\n        if (!state.digits) {\n          state.startInternationalNumber();\n          if (!digits) {\n            justLeadingPlus = true;\n          }\n        }\n      }\n      if (digits) {\n        this.inputDigits(digits, state);\n      }\n      return {\n        digits: digits,\n        justLeadingPlus: justLeadingPlus\n      };\n    }\n    /**\r\n     * Inputs \"next\" phone number digits.\r\n     * @param  {string} digits\r\n     * @return {string} [formattedNumber] Formatted national phone number (if it can be formatted at this stage). Returning `undefined` means \"don't format the national phone number at this stage\".\r\n     */\n  }, {\n    key: \"inputDigits\",\n    value: function inputDigits(nextDigits, state) {\n      var digits = state.digits;\n      var hasReceivedThreeLeadingDigits = digits.length < 3 && digits.length + nextDigits.length >= 3; // Append phone number digits.\n\n      state.appendDigits(nextDigits); // Attempt to extract IDD prefix:\n      // Some users input their phone number in international format,\n      // but in an \"out-of-country\" dialing format instead of using the leading `+`.\n      // https://github.com/catamphetamine/libphonenumber-js/issues/185\n      // Detect such numbers as soon as there're at least 3 digits.\n      // Google's library attempts to extract IDD prefix at 3 digits,\n      // so this library just copies that behavior.\n      // I guess that's because the most commot IDD prefixes are\n      // `00` (Europe) and `011` (US).\n      // There exist really long IDD prefixes too:\n      // for example, in Australia the default IDD prefix is `0011`,\n      // and it could even be as long as `14880011`.\n      // An IDD prefix is extracted here, and then every time when\n      // there's a new digit and the number couldn't be formatted.\n\n      if (hasReceivedThreeLeadingDigits) {\n        this.extractIddPrefix(state);\n      }\n      if (this.isWaitingForCountryCallingCode(state)) {\n        if (!this.extractCountryCallingCode(state)) {\n          return;\n        }\n      } else {\n        state.appendNationalSignificantNumberDigits(nextDigits);\n      } // If a phone number is being input in international format,\n      // then it's not valid for it to have a national prefix.\n      // Still, some people incorrectly input such numbers with a national prefix.\n      // In such cases, only attempt to strip a national prefix if the number becomes too long.\n      // (but that is done later, not here)\n\n      if (!state.international) {\n        if (!this.hasExtractedNationalSignificantNumber) {\n          this.extractNationalSignificantNumber(state.getNationalDigits(), function (stateUpdate) {\n            return state.update(stateUpdate);\n          });\n        }\n      }\n    }\n  }, {\n    key: \"isWaitingForCountryCallingCode\",\n    value: function isWaitingForCountryCallingCode(_ref2) {\n      var international = _ref2.international,\n        callingCode = _ref2.callingCode;\n      return international && !callingCode;\n    } // Extracts a country calling code from a number\n    // being entered in internatonal format.\n  }, {\n    key: \"extractCountryCallingCode\",\n    value: function extractCountryCallingCode(state) {\n      var _extractCountryCallin = _extractCountryCallingCode('+' + state.getDigitsWithoutInternationalPrefix(), this.defaultCountry, this.defaultCallingCode, this.metadata.metadata),\n        countryCallingCode = _extractCountryCallin.countryCallingCode,\n        number = _extractCountryCallin.number;\n      if (countryCallingCode) {\n        state.setCallingCode(countryCallingCode);\n        state.update({\n          nationalSignificantNumber: number\n        });\n        return true;\n      }\n    }\n  }, {\n    key: \"reset\",\n    value: function reset(numberingPlan) {\n      if (numberingPlan) {\n        this.hasSelectedNumberingPlan = true;\n        var nationalPrefixForParsing = numberingPlan._nationalPrefixForParsing();\n        this.couldPossiblyExtractAnotherNationalSignificantNumber = nationalPrefixForParsing && COMPLEX_NATIONAL_PREFIX.test(nationalPrefixForParsing);\n      } else {\n        this.hasSelectedNumberingPlan = undefined;\n        this.couldPossiblyExtractAnotherNationalSignificantNumber = undefined;\n      }\n    }\n    /**\r\n     * Extracts a national (significant) number from user input.\r\n     * Google's library is different in that it only applies `national_prefix_for_parsing`\r\n     * and doesn't apply `national_prefix_transform_rule` after that.\r\n     * https://github.com/google/libphonenumber/blob/a3d70b0487875475e6ad659af404943211d26456/java/libphonenumber/src/com/google/i18n/phonenumbers/AsYouTypeFormatter.java#L539\r\n     * @return {boolean} [extracted]\r\n     */\n  }, {\n    key: \"extractNationalSignificantNumber\",\n    value: function extractNationalSignificantNumber(nationalDigits, setState) {\n      if (!this.hasSelectedNumberingPlan) {\n        return;\n      }\n      var _extractNationalNumbe = extractNationalNumberFromPossiblyIncompleteNumber(nationalDigits, this.metadata),\n        nationalPrefix = _extractNationalNumbe.nationalPrefix,\n        nationalNumber = _extractNationalNumbe.nationalNumber,\n        carrierCode = _extractNationalNumbe.carrierCode;\n      if (nationalNumber === nationalDigits) {\n        return;\n      }\n      this.onExtractedNationalNumber(nationalPrefix, carrierCode, nationalNumber, nationalDigits, setState);\n      return true;\n    }\n    /**\r\n     * In Google's code this function is called \"attempt to extract longer NDD\".\r\n     * \"Some national prefixes are a substring of others\", they say.\r\n     * @return {boolean} [result] — Returns `true` if extracting a national prefix produced different results from what they were.\r\n     */\n  }, {\n    key: \"extractAnotherNationalSignificantNumber\",\n    value: function extractAnotherNationalSignificantNumber(nationalDigits, prevNationalSignificantNumber, setState) {\n      if (!this.hasExtractedNationalSignificantNumber) {\n        return this.extractNationalSignificantNumber(nationalDigits, setState);\n      }\n      if (!this.couldPossiblyExtractAnotherNationalSignificantNumber) {\n        return;\n      }\n      var _extractNationalNumbe2 = extractNationalNumberFromPossiblyIncompleteNumber(nationalDigits, this.metadata),\n        nationalPrefix = _extractNationalNumbe2.nationalPrefix,\n        nationalNumber = _extractNationalNumbe2.nationalNumber,\n        carrierCode = _extractNationalNumbe2.carrierCode; // If a national prefix has been extracted previously,\n      // then it's always extracted as additional digits are added.\n      // That's assuming `extractNationalNumberFromPossiblyIncompleteNumber()`\n      // doesn't do anything different from what it currently does.\n      // So, just in case, here's this check, though it doesn't occur.\n\n      /* istanbul ignore if */\n\n      if (nationalNumber === prevNationalSignificantNumber) {\n        return;\n      }\n      this.onExtractedNationalNumber(nationalPrefix, carrierCode, nationalNumber, nationalDigits, setState);\n      return true;\n    }\n  }, {\n    key: \"onExtractedNationalNumber\",\n    value: function onExtractedNationalNumber(nationalPrefix, carrierCode, nationalSignificantNumber, nationalDigits, setState) {\n      var complexPrefixBeforeNationalSignificantNumber;\n      var nationalSignificantNumberMatchesInput; // This check also works with empty `this.nationalSignificantNumber`.\n\n      var nationalSignificantNumberIndex = nationalDigits.lastIndexOf(nationalSignificantNumber); // If the extracted national (significant) number is the\n      // last substring of the `digits`, then it means that it hasn't been altered:\n      // no digits have been removed from the national (significant) number\n      // while applying `national_prefix_transform_rule`.\n      // https://gitlab.com/catamphetamine/libphonenumber-js/-/blob/master/METADATA.md#national_prefix_for_parsing--national_prefix_transform_rule\n\n      if (nationalSignificantNumberIndex >= 0 && nationalSignificantNumberIndex === nationalDigits.length - nationalSignificantNumber.length) {\n        nationalSignificantNumberMatchesInput = true; // If a prefix of a national (significant) number is not as simple\n        // as just a basic national prefix, then such prefix is stored in\n        // `this.complexPrefixBeforeNationalSignificantNumber` property and will be\n        // prepended \"as is\" to the national (significant) number to produce\n        // a formatted result.\n\n        var prefixBeforeNationalNumber = nationalDigits.slice(0, nationalSignificantNumberIndex); // `prefixBeforeNationalNumber` is always non-empty,\n        // because `onExtractedNationalNumber()` isn't called\n        // when a national (significant) number hasn't been actually \"extracted\":\n        // when a national (significant) number is equal to the national part of `digits`,\n        // then `onExtractedNationalNumber()` doesn't get called.\n\n        if (prefixBeforeNationalNumber !== nationalPrefix) {\n          complexPrefixBeforeNationalSignificantNumber = prefixBeforeNationalNumber;\n        }\n      }\n      setState({\n        nationalPrefix: nationalPrefix,\n        carrierCode: carrierCode,\n        nationalSignificantNumber: nationalSignificantNumber,\n        nationalSignificantNumberMatchesInput: nationalSignificantNumberMatchesInput,\n        complexPrefixBeforeNationalSignificantNumber: complexPrefixBeforeNationalSignificantNumber\n      }); // `onExtractedNationalNumber()` is only called when\n      // the national (significant) number actually did change.\n\n      this.hasExtractedNationalSignificantNumber = true;\n      this.onNationalSignificantNumberChange();\n    }\n  }, {\n    key: \"reExtractNationalSignificantNumber\",\n    value: function reExtractNationalSignificantNumber(state) {\n      // Attempt to extract a national prefix.\n      //\n      // Some people incorrectly input national prefix\n      // in an international phone number.\n      // For example, some people write British phone numbers as `+44(0)...`.\n      //\n      // Also, in some rare cases, it is valid for a national prefix\n      // to be a part of an international phone number.\n      // For example, mobile phone numbers in Mexico are supposed to be\n      // dialled internationally using a `1` national prefix,\n      // so the national prefix will be part of an international number.\n      //\n      // Quote from:\n      // https://www.mexperience.com/dialing-cell-phones-in-mexico/\n      //\n      // \"Dialing a Mexican cell phone from abroad\n      // When you are calling a cell phone number in Mexico from outside Mexico,\n      // it’s necessary to dial an additional “1” after Mexico’s country code\n      // (which is “52”) and before the area code.\n      // You also ignore the 045, and simply dial the area code and the\n      // cell phone’s number.\n      //\n      // If you don’t add the “1”, you’ll receive a recorded announcement\n      // asking you to redial using it.\n      //\n      // For example, if you are calling from the USA to a cell phone\n      // in Mexico City, you would dial +52 – 1 – 55 – 1234 5678.\n      // (Note that this is different to calling a land line in Mexico City\n      // from abroad, where the number dialed would be +52 – 55 – 1234 5678)\".\n      //\n      // Google's demo output:\n      // https://libphonenumber.appspot.com/phonenumberparser?number=%2b5215512345678&country=MX\n      //\n      if (this.extractAnotherNationalSignificantNumber(state.getNationalDigits(), state.nationalSignificantNumber, function (stateUpdate) {\n        return state.update(stateUpdate);\n      })) {\n        return true;\n      } // If no format matches the phone number, then it could be\n      // \"a really long IDD\" (quote from a comment in Google's library).\n      // An IDD prefix is first extracted when the user has entered at least 3 digits,\n      // and then here — every time when there's a new digit and the number\n      // couldn't be formatted.\n      // For example, in Australia the default IDD prefix is `0011`,\n      // and it could even be as long as `14880011`.\n      //\n      // Could also check `!hasReceivedThreeLeadingDigits` here\n      // to filter out the case when this check duplicates the one\n      // already performed when there're 3 leading digits,\n      // but it's not a big deal, and in most cases there\n      // will be a suitable `format` when there're 3 leading digits.\n      //\n\n      if (this.extractIddPrefix(state)) {\n        this.extractCallingCodeAndNationalSignificantNumber(state);\n        return true;\n      } // Google's AsYouType formatter supports sort of an \"autocorrection\" feature\n      // when it \"autocorrects\" numbers that have been input for a country\n      // with that country's calling code.\n      // Such \"autocorrection\" feature looks weird, but different people have been requesting it:\n      // https://github.com/catamphetamine/libphonenumber-js/issues/376\n      // https://github.com/catamphetamine/libphonenumber-js/issues/375\n      // https://github.com/catamphetamine/libphonenumber-js/issues/316\n\n      if (this.fixMissingPlus(state)) {\n        this.extractCallingCodeAndNationalSignificantNumber(state);\n        return true;\n      }\n    }\n  }, {\n    key: \"extractIddPrefix\",\n    value: function extractIddPrefix(state) {\n      // An IDD prefix can't be present in a number written with a `+`.\n      // Also, don't re-extract an IDD prefix if has already been extracted.\n      var international = state.international,\n        IDDPrefix = state.IDDPrefix,\n        digits = state.digits,\n        nationalSignificantNumber = state.nationalSignificantNumber;\n      if (international || IDDPrefix) {\n        return;\n      } // Some users input their phone number in \"out-of-country\"\n      // dialing format instead of using the leading `+`.\n      // https://github.com/catamphetamine/libphonenumber-js/issues/185\n      // Detect such numbers.\n\n      var numberWithoutIDD = stripIddPrefix(digits, this.defaultCountry, this.defaultCallingCode, this.metadata.metadata);\n      if (numberWithoutIDD !== undefined && numberWithoutIDD !== digits) {\n        // If an IDD prefix was stripped then convert the IDD-prefixed number\n        // to international number for subsequent parsing.\n        state.update({\n          IDDPrefix: digits.slice(0, digits.length - numberWithoutIDD.length)\n        });\n        this.startInternationalNumber(state, {\n          country: undefined,\n          callingCode: undefined\n        });\n        return true;\n      }\n    }\n  }, {\n    key: \"fixMissingPlus\",\n    value: function fixMissingPlus(state) {\n      if (!state.international) {\n        var _extractCountryCallin2 = extractCountryCallingCodeFromInternationalNumberWithoutPlusSign(state.digits, this.defaultCountry, this.defaultCallingCode, this.metadata.metadata),\n          newCallingCode = _extractCountryCallin2.countryCallingCode,\n          number = _extractCountryCallin2.number;\n        if (newCallingCode) {\n          state.update({\n            missingPlus: true\n          });\n          this.startInternationalNumber(state, {\n            country: state.country,\n            callingCode: newCallingCode\n          });\n          return true;\n        }\n      }\n    }\n  }, {\n    key: \"startInternationalNumber\",\n    value: function startInternationalNumber(state, _ref3) {\n      var country = _ref3.country,\n        callingCode = _ref3.callingCode;\n      state.startInternationalNumber(country, callingCode); // If a national (significant) number has been extracted before, reset it.\n\n      if (state.nationalSignificantNumber) {\n        state.resetNationalSignificantNumber();\n        this.onNationalSignificantNumberChange();\n        this.hasExtractedNationalSignificantNumber = undefined;\n      }\n    }\n  }, {\n    key: \"extractCallingCodeAndNationalSignificantNumber\",\n    value: function extractCallingCodeAndNationalSignificantNumber(state) {\n      if (this.extractCountryCallingCode(state)) {\n        // `this.extractCallingCode()` is currently called when the number\n        // couldn't be formatted during the standard procedure.\n        // Normally, the national prefix would be re-extracted\n        // for an international number if such number couldn't be formatted,\n        // but since it's already not able to be formatted,\n        // there won't be yet another retry, so also extract national prefix here.\n        this.extractNationalSignificantNumber(state.getNationalDigits(), function (stateUpdate) {\n          return state.update(stateUpdate);\n        });\n      }\n    }\n  }]);\n  return AsYouTypeParser;\n}();\n/**\r\n * Extracts formatted phone number from text (if there's any).\r\n * @param  {string} text\r\n * @return {string} [formattedPhoneNumber]\r\n */\n\nexport { AsYouTypeParser as default };\nfunction extractFormattedPhoneNumber(text) {\n  // Attempt to extract a possible number from the string passed in.\n  var startsAt = text.search(VALID_FORMATTED_PHONE_NUMBER_PART);\n  if (startsAt < 0) {\n    return;\n  } // Trim everything to the left of the phone number.\n\n  text = text.slice(startsAt); // Trim the `+`.\n\n  var hasPlus;\n  if (text[0] === '+') {\n    hasPlus = true;\n    text = text.slice('+'.length);\n  } // Trim everything to the right of the phone number.\n\n  text = text.replace(AFTER_PHONE_NUMBER_DIGITS_END_PATTERN, ''); // Re-add the previously trimmed `+`.\n\n  if (hasPlus) {\n    text = '+' + text;\n  }\n  return text;\n}\n/**\r\n * Extracts formatted phone number digits (and a `+`) from text (if there're any).\r\n * @param  {string} text\r\n * @return {any[]}\r\n */\n\nfunction _extractFormattedDigitsAndPlus(text) {\n  // Extract a formatted phone number part from text.\n  var extractedNumber = extractFormattedPhoneNumber(text) || ''; // Trim a `+`.\n\n  if (extractedNumber[0] === '+') {\n    return [extractedNumber.slice('+'.length), true];\n  }\n  return [extractedNumber];\n}\n/**\r\n * Extracts formatted phone number digits (and a `+`) from text (if there're any).\r\n * @param  {string} text\r\n * @return {any[]}\r\n */\n\nexport function extractFormattedDigitsAndPlus(text) {\n  var _extractFormattedDigi3 = _extractFormattedDigitsAndPlus(text),\n    _extractFormattedDigi4 = _slicedToArray(_extractFormattedDigi3, 2),\n    formattedDigits = _extractFormattedDigi4[0],\n    hasPlus = _extractFormattedDigi4[1]; // If the extracted phone number part\n  // can possibly be a part of some valid phone number\n  // then parse phone number characters from a formatted phone number.\n\n  if (!VALID_FORMATTED_PHONE_NUMBER_DIGITS_PART_PATTERN.test(formattedDigits)) {\n    formattedDigits = '';\n  }\n  return [formattedDigits, hasPlus];\n}", "map": {"version": 3, "names": ["_slicedToArray", "arr", "i", "_arrayWithHoles", "_iterableToArrayLimit", "_unsupportedIterableToArray", "_nonIterableRest", "TypeError", "o", "minLen", "_arrayLikeToArray", "n", "Object", "prototype", "toString", "call", "slice", "constructor", "name", "Array", "from", "test", "len", "length", "arr2", "_i", "Symbol", "iterator", "_arr", "_n", "_d", "_s", "_e", "next", "done", "push", "value", "err", "isArray", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "_defineProperties", "target", "props", "descriptor", "enumerable", "configurable", "writable", "defineProperty", "key", "_createClass", "protoProps", "staticProps", "_extractCountryCallingCode", "extractCountryCallingCodeFromInternationalNumberWithoutPlusSign", "extractNationalNumberFromPossiblyIncompleteNumber", "stripIddPrefix", "parseDigits", "VALID_DIGITS", "VALID_PUNCTUATION", "PLUS_CHARS", "VALID_FORMATTED_PHONE_NUMBER_DIGITS_PART", "VALID_FORMATTED_PHONE_NUMBER_DIGITS_PART_PATTERN", "RegExp", "VALID_FORMATTED_PHONE_NUMBER_PART", "AFTER_PHONE_NUMBER_DIGITS_END_PATTERN", "COMPLEX_NATIONAL_PREFIX", "AsYouType<PERSON><PERSON><PERSON>", "_ref", "defaultCountry", "defaultCallingCode", "metadata", "onNationalSignificantNumberChange", "input", "text", "state", "_extractFormattedDigi", "extractFormattedDigitsAndPlus", "_extractFormattedDigi2", "formattedDigits", "hasPlus", "digits", "justLeadingPlus", "startInternationalNumber", "inputDigits", "nextDigits", "hasReceivedThreeLeadingDigits", "appendDigits", "extractIddPrefix", "isWaitingForCountryCallingCode", "extractCountryCallingCode", "appendNationalSignificantNumberDigits", "international", "hasExtractedNationalSignificantNumber", "extractNationalSignificantNumber", "getNationalDigits", "stateUpdate", "update", "_ref2", "callingCode", "_extractCountryCallin", "getDigitsWithoutInternationalPrefix", "countryCallingCode", "number", "setCallingCode", "nationalSignificantNumber", "reset", "numberingPlan", "hasSelectedNumberingPlan", "nationalPrefixForParsing", "_nationalPrefixForParsing", "couldPossiblyExtractAnotherNationalSignificantNumber", "undefined", "nationalDigits", "setState", "_extractNationalNumbe", "nationalPrefix", "nationalNumber", "carrierCode", "onExtractedNationalNumber", "extractAnotherNationalSignificantNumber", "prevNationalSignificantNumber", "_extractNationalNumbe2", "complexPrefixBeforeNationalSignificantNumber", "nationalSignificantNumberMatchesInput", "nationalSignificantNumberIndex", "lastIndexOf", "prefixBeforeNationalNumber", "reExtractNationalSignificantNumber", "extractCallingCodeAndNationalSignificantNumber", "fixMissingPlus", "IDDPrefix", "numberWithoutIDD", "country", "_extractCountryCallin2", "newCallingCode", "missingPlus", "_ref3", "resetNationalSignificantNumber", "default", "extractFormattedPhoneNumber", "startsAt", "search", "replace", "_extractFormattedDigitsAndPlus", "extractedNumber", "_extractFormattedDigi3", "_extractFormattedDigi4"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/AsYouTypeParser.js"], "sourcesContent": ["function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\n\nimport _extractCountryCallingCode from './helpers/extractCountryCallingCode.js';\nimport extractCountryCallingCodeFromInternationalNumberWithoutPlusSign from './helpers/extractCountryCallingCodeFromInternationalNumberWithoutPlusSign.js';\nimport extractNationalNumberFromPossiblyIncompleteNumber from './helpers/extractNationalNumberFromPossiblyIncompleteNumber.js';\nimport stripIddPrefix from './helpers/stripIddPrefix.js';\nimport parseDigits from './helpers/parseDigits.js';\nimport { VALID_DIGITS, VALID_PUNCTUATION, PLUS_CHARS } from './constants.js';\nvar VALID_FORMATTED_PHONE_NUMBER_DIGITS_PART = '[' + VALID_PUNCTUATION + VALID_DIGITS + ']+';\nvar VALID_FORMATTED_PHONE_NUMBER_DIGITS_PART_PATTERN = new RegExp('^' + VALID_FORMATTED_PHONE_NUMBER_DIGITS_PART + '$', 'i');\nvar VALID_FORMATTED_PHONE_NUMBER_PART = '(?:' + '[' + PLUS_CHARS + ']' + '[' + VALID_PUNCTUATION + VALID_DIGITS + ']*' + '|' + '[' + VALID_PUNCTUATION + VALID_DIGITS + ']+' + ')';\nvar AFTER_PHONE_NUMBER_DIGITS_END_PATTERN = new RegExp('[^' + VALID_PUNCTUATION + VALID_DIGITS + ']+' + '.*' + '$'); // Tests whether `national_prefix_for_parsing` could match\n// different national prefixes.\n// Matches anything that's not a digit or a square bracket.\n\nvar COMPLEX_NATIONAL_PREFIX = /[^\\d\\[\\]]/;\n\nvar AsYouTypeParser = /*#__PURE__*/function () {\n  function AsYouTypeParser(_ref) {\n    var defaultCountry = _ref.defaultCountry,\n        defaultCallingCode = _ref.defaultCallingCode,\n        metadata = _ref.metadata,\n        onNationalSignificantNumberChange = _ref.onNationalSignificantNumberChange;\n\n    _classCallCheck(this, AsYouTypeParser);\n\n    this.defaultCountry = defaultCountry;\n    this.defaultCallingCode = defaultCallingCode;\n    this.metadata = metadata;\n    this.onNationalSignificantNumberChange = onNationalSignificantNumberChange;\n  }\n\n  _createClass(AsYouTypeParser, [{\n    key: \"input\",\n    value: function input(text, state) {\n      var _extractFormattedDigi = extractFormattedDigitsAndPlus(text),\n          _extractFormattedDigi2 = _slicedToArray(_extractFormattedDigi, 2),\n          formattedDigits = _extractFormattedDigi2[0],\n          hasPlus = _extractFormattedDigi2[1];\n\n      var digits = parseDigits(formattedDigits); // Checks for a special case: just a leading `+` has been entered.\n\n      var justLeadingPlus;\n\n      if (hasPlus) {\n        if (!state.digits) {\n          state.startInternationalNumber();\n\n          if (!digits) {\n            justLeadingPlus = true;\n          }\n        }\n      }\n\n      if (digits) {\n        this.inputDigits(digits, state);\n      }\n\n      return {\n        digits: digits,\n        justLeadingPlus: justLeadingPlus\n      };\n    }\n    /**\r\n     * Inputs \"next\" phone number digits.\r\n     * @param  {string} digits\r\n     * @return {string} [formattedNumber] Formatted national phone number (if it can be formatted at this stage). Returning `undefined` means \"don't format the national phone number at this stage\".\r\n     */\n\n  }, {\n    key: \"inputDigits\",\n    value: function inputDigits(nextDigits, state) {\n      var digits = state.digits;\n      var hasReceivedThreeLeadingDigits = digits.length < 3 && digits.length + nextDigits.length >= 3; // Append phone number digits.\n\n      state.appendDigits(nextDigits); // Attempt to extract IDD prefix:\n      // Some users input their phone number in international format,\n      // but in an \"out-of-country\" dialing format instead of using the leading `+`.\n      // https://github.com/catamphetamine/libphonenumber-js/issues/185\n      // Detect such numbers as soon as there're at least 3 digits.\n      // Google's library attempts to extract IDD prefix at 3 digits,\n      // so this library just copies that behavior.\n      // I guess that's because the most commot IDD prefixes are\n      // `00` (Europe) and `011` (US).\n      // There exist really long IDD prefixes too:\n      // for example, in Australia the default IDD prefix is `0011`,\n      // and it could even be as long as `14880011`.\n      // An IDD prefix is extracted here, and then every time when\n      // there's a new digit and the number couldn't be formatted.\n\n      if (hasReceivedThreeLeadingDigits) {\n        this.extractIddPrefix(state);\n      }\n\n      if (this.isWaitingForCountryCallingCode(state)) {\n        if (!this.extractCountryCallingCode(state)) {\n          return;\n        }\n      } else {\n        state.appendNationalSignificantNumberDigits(nextDigits);\n      } // If a phone number is being input in international format,\n      // then it's not valid for it to have a national prefix.\n      // Still, some people incorrectly input such numbers with a national prefix.\n      // In such cases, only attempt to strip a national prefix if the number becomes too long.\n      // (but that is done later, not here)\n\n\n      if (!state.international) {\n        if (!this.hasExtractedNationalSignificantNumber) {\n          this.extractNationalSignificantNumber(state.getNationalDigits(), function (stateUpdate) {\n            return state.update(stateUpdate);\n          });\n        }\n      }\n    }\n  }, {\n    key: \"isWaitingForCountryCallingCode\",\n    value: function isWaitingForCountryCallingCode(_ref2) {\n      var international = _ref2.international,\n          callingCode = _ref2.callingCode;\n      return international && !callingCode;\n    } // Extracts a country calling code from a number\n    // being entered in internatonal format.\n\n  }, {\n    key: \"extractCountryCallingCode\",\n    value: function extractCountryCallingCode(state) {\n      var _extractCountryCallin = _extractCountryCallingCode('+' + state.getDigitsWithoutInternationalPrefix(), this.defaultCountry, this.defaultCallingCode, this.metadata.metadata),\n          countryCallingCode = _extractCountryCallin.countryCallingCode,\n          number = _extractCountryCallin.number;\n\n      if (countryCallingCode) {\n        state.setCallingCode(countryCallingCode);\n        state.update({\n          nationalSignificantNumber: number\n        });\n        return true;\n      }\n    }\n  }, {\n    key: \"reset\",\n    value: function reset(numberingPlan) {\n      if (numberingPlan) {\n        this.hasSelectedNumberingPlan = true;\n\n        var nationalPrefixForParsing = numberingPlan._nationalPrefixForParsing();\n\n        this.couldPossiblyExtractAnotherNationalSignificantNumber = nationalPrefixForParsing && COMPLEX_NATIONAL_PREFIX.test(nationalPrefixForParsing);\n      } else {\n        this.hasSelectedNumberingPlan = undefined;\n        this.couldPossiblyExtractAnotherNationalSignificantNumber = undefined;\n      }\n    }\n    /**\r\n     * Extracts a national (significant) number from user input.\r\n     * Google's library is different in that it only applies `national_prefix_for_parsing`\r\n     * and doesn't apply `national_prefix_transform_rule` after that.\r\n     * https://github.com/google/libphonenumber/blob/a3d70b0487875475e6ad659af404943211d26456/java/libphonenumber/src/com/google/i18n/phonenumbers/AsYouTypeFormatter.java#L539\r\n     * @return {boolean} [extracted]\r\n     */\n\n  }, {\n    key: \"extractNationalSignificantNumber\",\n    value: function extractNationalSignificantNumber(nationalDigits, setState) {\n      if (!this.hasSelectedNumberingPlan) {\n        return;\n      }\n\n      var _extractNationalNumbe = extractNationalNumberFromPossiblyIncompleteNumber(nationalDigits, this.metadata),\n          nationalPrefix = _extractNationalNumbe.nationalPrefix,\n          nationalNumber = _extractNationalNumbe.nationalNumber,\n          carrierCode = _extractNationalNumbe.carrierCode;\n\n      if (nationalNumber === nationalDigits) {\n        return;\n      }\n\n      this.onExtractedNationalNumber(nationalPrefix, carrierCode, nationalNumber, nationalDigits, setState);\n      return true;\n    }\n    /**\r\n     * In Google's code this function is called \"attempt to extract longer NDD\".\r\n     * \"Some national prefixes are a substring of others\", they say.\r\n     * @return {boolean} [result] — Returns `true` if extracting a national prefix produced different results from what they were.\r\n     */\n\n  }, {\n    key: \"extractAnotherNationalSignificantNumber\",\n    value: function extractAnotherNationalSignificantNumber(nationalDigits, prevNationalSignificantNumber, setState) {\n      if (!this.hasExtractedNationalSignificantNumber) {\n        return this.extractNationalSignificantNumber(nationalDigits, setState);\n      }\n\n      if (!this.couldPossiblyExtractAnotherNationalSignificantNumber) {\n        return;\n      }\n\n      var _extractNationalNumbe2 = extractNationalNumberFromPossiblyIncompleteNumber(nationalDigits, this.metadata),\n          nationalPrefix = _extractNationalNumbe2.nationalPrefix,\n          nationalNumber = _extractNationalNumbe2.nationalNumber,\n          carrierCode = _extractNationalNumbe2.carrierCode; // If a national prefix has been extracted previously,\n      // then it's always extracted as additional digits are added.\n      // That's assuming `extractNationalNumberFromPossiblyIncompleteNumber()`\n      // doesn't do anything different from what it currently does.\n      // So, just in case, here's this check, though it doesn't occur.\n\n      /* istanbul ignore if */\n\n\n      if (nationalNumber === prevNationalSignificantNumber) {\n        return;\n      }\n\n      this.onExtractedNationalNumber(nationalPrefix, carrierCode, nationalNumber, nationalDigits, setState);\n      return true;\n    }\n  }, {\n    key: \"onExtractedNationalNumber\",\n    value: function onExtractedNationalNumber(nationalPrefix, carrierCode, nationalSignificantNumber, nationalDigits, setState) {\n      var complexPrefixBeforeNationalSignificantNumber;\n      var nationalSignificantNumberMatchesInput; // This check also works with empty `this.nationalSignificantNumber`.\n\n      var nationalSignificantNumberIndex = nationalDigits.lastIndexOf(nationalSignificantNumber); // If the extracted national (significant) number is the\n      // last substring of the `digits`, then it means that it hasn't been altered:\n      // no digits have been removed from the national (significant) number\n      // while applying `national_prefix_transform_rule`.\n      // https://gitlab.com/catamphetamine/libphonenumber-js/-/blob/master/METADATA.md#national_prefix_for_parsing--national_prefix_transform_rule\n\n      if (nationalSignificantNumberIndex >= 0 && nationalSignificantNumberIndex === nationalDigits.length - nationalSignificantNumber.length) {\n        nationalSignificantNumberMatchesInput = true; // If a prefix of a national (significant) number is not as simple\n        // as just a basic national prefix, then such prefix is stored in\n        // `this.complexPrefixBeforeNationalSignificantNumber` property and will be\n        // prepended \"as is\" to the national (significant) number to produce\n        // a formatted result.\n\n        var prefixBeforeNationalNumber = nationalDigits.slice(0, nationalSignificantNumberIndex); // `prefixBeforeNationalNumber` is always non-empty,\n        // because `onExtractedNationalNumber()` isn't called\n        // when a national (significant) number hasn't been actually \"extracted\":\n        // when a national (significant) number is equal to the national part of `digits`,\n        // then `onExtractedNationalNumber()` doesn't get called.\n\n        if (prefixBeforeNationalNumber !== nationalPrefix) {\n          complexPrefixBeforeNationalSignificantNumber = prefixBeforeNationalNumber;\n        }\n      }\n\n      setState({\n        nationalPrefix: nationalPrefix,\n        carrierCode: carrierCode,\n        nationalSignificantNumber: nationalSignificantNumber,\n        nationalSignificantNumberMatchesInput: nationalSignificantNumberMatchesInput,\n        complexPrefixBeforeNationalSignificantNumber: complexPrefixBeforeNationalSignificantNumber\n      }); // `onExtractedNationalNumber()` is only called when\n      // the national (significant) number actually did change.\n\n      this.hasExtractedNationalSignificantNumber = true;\n      this.onNationalSignificantNumberChange();\n    }\n  }, {\n    key: \"reExtractNationalSignificantNumber\",\n    value: function reExtractNationalSignificantNumber(state) {\n      // Attempt to extract a national prefix.\n      //\n      // Some people incorrectly input national prefix\n      // in an international phone number.\n      // For example, some people write British phone numbers as `+44(0)...`.\n      //\n      // Also, in some rare cases, it is valid for a national prefix\n      // to be a part of an international phone number.\n      // For example, mobile phone numbers in Mexico are supposed to be\n      // dialled internationally using a `1` national prefix,\n      // so the national prefix will be part of an international number.\n      //\n      // Quote from:\n      // https://www.mexperience.com/dialing-cell-phones-in-mexico/\n      //\n      // \"Dialing a Mexican cell phone from abroad\n      // When you are calling a cell phone number in Mexico from outside Mexico,\n      // it’s necessary to dial an additional “1” after Mexico’s country code\n      // (which is “52”) and before the area code.\n      // You also ignore the 045, and simply dial the area code and the\n      // cell phone’s number.\n      //\n      // If you don’t add the “1”, you’ll receive a recorded announcement\n      // asking you to redial using it.\n      //\n      // For example, if you are calling from the USA to a cell phone\n      // in Mexico City, you would dial +52 – 1 – 55 – 1234 5678.\n      // (Note that this is different to calling a land line in Mexico City\n      // from abroad, where the number dialed would be +52 – 55 – 1234 5678)\".\n      //\n      // Google's demo output:\n      // https://libphonenumber.appspot.com/phonenumberparser?number=%2b5215512345678&country=MX\n      //\n      if (this.extractAnotherNationalSignificantNumber(state.getNationalDigits(), state.nationalSignificantNumber, function (stateUpdate) {\n        return state.update(stateUpdate);\n      })) {\n        return true;\n      } // If no format matches the phone number, then it could be\n      // \"a really long IDD\" (quote from a comment in Google's library).\n      // An IDD prefix is first extracted when the user has entered at least 3 digits,\n      // and then here — every time when there's a new digit and the number\n      // couldn't be formatted.\n      // For example, in Australia the default IDD prefix is `0011`,\n      // and it could even be as long as `14880011`.\n      //\n      // Could also check `!hasReceivedThreeLeadingDigits` here\n      // to filter out the case when this check duplicates the one\n      // already performed when there're 3 leading digits,\n      // but it's not a big deal, and in most cases there\n      // will be a suitable `format` when there're 3 leading digits.\n      //\n\n\n      if (this.extractIddPrefix(state)) {\n        this.extractCallingCodeAndNationalSignificantNumber(state);\n        return true;\n      } // Google's AsYouType formatter supports sort of an \"autocorrection\" feature\n      // when it \"autocorrects\" numbers that have been input for a country\n      // with that country's calling code.\n      // Such \"autocorrection\" feature looks weird, but different people have been requesting it:\n      // https://github.com/catamphetamine/libphonenumber-js/issues/376\n      // https://github.com/catamphetamine/libphonenumber-js/issues/375\n      // https://github.com/catamphetamine/libphonenumber-js/issues/316\n\n\n      if (this.fixMissingPlus(state)) {\n        this.extractCallingCodeAndNationalSignificantNumber(state);\n        return true;\n      }\n    }\n  }, {\n    key: \"extractIddPrefix\",\n    value: function extractIddPrefix(state) {\n      // An IDD prefix can't be present in a number written with a `+`.\n      // Also, don't re-extract an IDD prefix if has already been extracted.\n      var international = state.international,\n          IDDPrefix = state.IDDPrefix,\n          digits = state.digits,\n          nationalSignificantNumber = state.nationalSignificantNumber;\n\n      if (international || IDDPrefix) {\n        return;\n      } // Some users input their phone number in \"out-of-country\"\n      // dialing format instead of using the leading `+`.\n      // https://github.com/catamphetamine/libphonenumber-js/issues/185\n      // Detect such numbers.\n\n\n      var numberWithoutIDD = stripIddPrefix(digits, this.defaultCountry, this.defaultCallingCode, this.metadata.metadata);\n\n      if (numberWithoutIDD !== undefined && numberWithoutIDD !== digits) {\n        // If an IDD prefix was stripped then convert the IDD-prefixed number\n        // to international number for subsequent parsing.\n        state.update({\n          IDDPrefix: digits.slice(0, digits.length - numberWithoutIDD.length)\n        });\n        this.startInternationalNumber(state, {\n          country: undefined,\n          callingCode: undefined\n        });\n        return true;\n      }\n    }\n  }, {\n    key: \"fixMissingPlus\",\n    value: function fixMissingPlus(state) {\n      if (!state.international) {\n        var _extractCountryCallin2 = extractCountryCallingCodeFromInternationalNumberWithoutPlusSign(state.digits, this.defaultCountry, this.defaultCallingCode, this.metadata.metadata),\n            newCallingCode = _extractCountryCallin2.countryCallingCode,\n            number = _extractCountryCallin2.number;\n\n        if (newCallingCode) {\n          state.update({\n            missingPlus: true\n          });\n          this.startInternationalNumber(state, {\n            country: state.country,\n            callingCode: newCallingCode\n          });\n          return true;\n        }\n      }\n    }\n  }, {\n    key: \"startInternationalNumber\",\n    value: function startInternationalNumber(state, _ref3) {\n      var country = _ref3.country,\n          callingCode = _ref3.callingCode;\n      state.startInternationalNumber(country, callingCode); // If a national (significant) number has been extracted before, reset it.\n\n      if (state.nationalSignificantNumber) {\n        state.resetNationalSignificantNumber();\n        this.onNationalSignificantNumberChange();\n        this.hasExtractedNationalSignificantNumber = undefined;\n      }\n    }\n  }, {\n    key: \"extractCallingCodeAndNationalSignificantNumber\",\n    value: function extractCallingCodeAndNationalSignificantNumber(state) {\n      if (this.extractCountryCallingCode(state)) {\n        // `this.extractCallingCode()` is currently called when the number\n        // couldn't be formatted during the standard procedure.\n        // Normally, the national prefix would be re-extracted\n        // for an international number if such number couldn't be formatted,\n        // but since it's already not able to be formatted,\n        // there won't be yet another retry, so also extract national prefix here.\n        this.extractNationalSignificantNumber(state.getNationalDigits(), function (stateUpdate) {\n          return state.update(stateUpdate);\n        });\n      }\n    }\n  }]);\n\n  return AsYouTypeParser;\n}();\n/**\r\n * Extracts formatted phone number from text (if there's any).\r\n * @param  {string} text\r\n * @return {string} [formattedPhoneNumber]\r\n */\n\n\nexport { AsYouTypeParser as default };\n\nfunction extractFormattedPhoneNumber(text) {\n  // Attempt to extract a possible number from the string passed in.\n  var startsAt = text.search(VALID_FORMATTED_PHONE_NUMBER_PART);\n\n  if (startsAt < 0) {\n    return;\n  } // Trim everything to the left of the phone number.\n\n\n  text = text.slice(startsAt); // Trim the `+`.\n\n  var hasPlus;\n\n  if (text[0] === '+') {\n    hasPlus = true;\n    text = text.slice('+'.length);\n  } // Trim everything to the right of the phone number.\n\n\n  text = text.replace(AFTER_PHONE_NUMBER_DIGITS_END_PATTERN, ''); // Re-add the previously trimmed `+`.\n\n  if (hasPlus) {\n    text = '+' + text;\n  }\n\n  return text;\n}\n/**\r\n * Extracts formatted phone number digits (and a `+`) from text (if there're any).\r\n * @param  {string} text\r\n * @return {any[]}\r\n */\n\n\nfunction _extractFormattedDigitsAndPlus(text) {\n  // Extract a formatted phone number part from text.\n  var extractedNumber = extractFormattedPhoneNumber(text) || ''; // Trim a `+`.\n\n  if (extractedNumber[0] === '+') {\n    return [extractedNumber.slice('+'.length), true];\n  }\n\n  return [extractedNumber];\n}\n/**\r\n * Extracts formatted phone number digits (and a `+`) from text (if there're any).\r\n * @param  {string} text\r\n * @return {any[]}\r\n */\n\n\nexport function extractFormattedDigitsAndPlus(text) {\n  var _extractFormattedDigi3 = _extractFormattedDigitsAndPlus(text),\n      _extractFormattedDigi4 = _slicedToArray(_extractFormattedDigi3, 2),\n      formattedDigits = _extractFormattedDigi4[0],\n      hasPlus = _extractFormattedDigi4[1]; // If the extracted phone number part\n  // can possibly be a part of some valid phone number\n  // then parse phone number characters from a formatted phone number.\n\n\n  if (!VALID_FORMATTED_PHONE_NUMBER_DIGITS_PART_PATTERN.test(formattedDigits)) {\n    formattedDigits = '';\n  }\n\n  return [formattedDigits, hasPlus];\n}\n"], "mappings": "AAAA,SAASA,cAAcA,CAACC,GAAG,EAAEC,CAAC,EAAE;EAAE,OAAOC,eAAe,CAACF,GAAG,CAAC,IAAIG,qBAAqB,CAACH,GAAG,EAAEC,CAAC,CAAC,IAAIG,2BAA2B,CAACJ,GAAG,EAAEC,CAAC,CAAC,IAAII,gBAAgB,CAAC,CAAC;AAAE;AAE7J,SAASA,gBAAgBA,CAAA,EAAG;EAAE,MAAM,IAAIC,SAAS,CAAC,2IAA2I,CAAC;AAAE;AAEhM,SAASF,2BAA2BA,CAACG,CAAC,EAAEC,MAAM,EAAE;EAAE,IAAI,CAACD,CAAC,EAAE;EAAQ,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOE,iBAAiB,CAACF,CAAC,EAAEC,MAAM,CAAC;EAAE,IAAIE,CAAC,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACP,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAE,IAAIL,CAAC,KAAK,QAAQ,IAAIH,CAAC,CAACS,WAAW,EAAEN,CAAC,GAAGH,CAAC,CAACS,WAAW,CAACC,IAAI;EAAE,IAAIP,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOQ,KAAK,CAACC,IAAI,CAACZ,CAAC,CAAC;EAAE,IAAIG,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACU,IAAI,CAACV,CAAC,CAAC,EAAE,OAAOD,iBAAiB,CAACF,CAAC,EAAEC,MAAM,CAAC;AAAE;AAE/Z,SAASC,iBAAiBA,CAACT,GAAG,EAAEqB,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGrB,GAAG,CAACsB,MAAM,EAAED,GAAG,GAAGrB,GAAG,CAACsB,MAAM;EAAE,KAAK,IAAIrB,CAAC,GAAG,CAAC,EAAEsB,IAAI,GAAG,IAAIL,KAAK,CAACG,GAAG,CAAC,EAAEpB,CAAC,GAAGoB,GAAG,EAAEpB,CAAC,EAAE,EAAE;IAAEsB,IAAI,CAACtB,CAAC,CAAC,GAAGD,GAAG,CAACC,CAAC,CAAC;EAAE;EAAE,OAAOsB,IAAI;AAAE;AAEtL,SAASpB,qBAAqBA,CAACH,GAAG,EAAEC,CAAC,EAAE;EAAE,IAAIuB,EAAE,GAAGxB,GAAG,IAAI,IAAI,GAAG,IAAI,GAAG,OAAOyB,MAAM,KAAK,WAAW,IAAIzB,GAAG,CAACyB,MAAM,CAACC,QAAQ,CAAC,IAAI1B,GAAG,CAAC,YAAY,CAAC;EAAE,IAAIwB,EAAE,IAAI,IAAI,EAAE;EAAQ,IAAIG,IAAI,GAAG,EAAE;EAAE,IAAIC,EAAE,GAAG,IAAI;EAAE,IAAIC,EAAE,GAAG,KAAK;EAAE,IAAIC,EAAE,EAAEC,EAAE;EAAE,IAAI;IAAE,KAAKP,EAAE,GAAGA,EAAE,CAACV,IAAI,CAACd,GAAG,CAAC,EAAE,EAAE4B,EAAE,GAAG,CAACE,EAAE,GAAGN,EAAE,CAACQ,IAAI,CAAC,CAAC,EAAEC,IAAI,CAAC,EAAEL,EAAE,GAAG,IAAI,EAAE;MAAED,IAAI,CAACO,IAAI,CAACJ,EAAE,CAACK,KAAK,CAAC;MAAE,IAAIlC,CAAC,IAAI0B,IAAI,CAACL,MAAM,KAAKrB,CAAC,EAAE;IAAO;EAAE,CAAC,CAAC,OAAOmC,GAAG,EAAE;IAAEP,EAAE,GAAG,IAAI;IAAEE,EAAE,GAAGK,GAAG;EAAE,CAAC,SAAS;IAAE,IAAI;MAAE,IAAI,CAACR,EAAE,IAAIJ,EAAE,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAEA,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAAE,CAAC,SAAS;MAAE,IAAIK,EAAE,EAAE,MAAME,EAAE;IAAE;EAAE;EAAE,OAAOJ,IAAI;AAAE;AAEhgB,SAASzB,eAAeA,CAACF,GAAG,EAAE;EAAE,IAAIkB,KAAK,CAACmB,OAAO,CAACrC,GAAG,CAAC,EAAE,OAAOA,GAAG;AAAE;AAEpE,SAASsC,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIlC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AAExJ,SAASmC,iBAAiBA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAAE,KAAK,IAAI1C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0C,KAAK,CAACrB,MAAM,EAAErB,CAAC,EAAE,EAAE;IAAE,IAAI2C,UAAU,GAAGD,KAAK,CAAC1C,CAAC,CAAC;IAAE2C,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;IAAED,UAAU,CAACE,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;IAAEpC,MAAM,CAACqC,cAAc,CAACN,MAAM,EAAEE,UAAU,CAACK,GAAG,EAAEL,UAAU,CAAC;EAAE;AAAE;AAE5T,SAASM,YAAYA,CAACV,WAAW,EAAEW,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEV,iBAAiB,CAACD,WAAW,CAAC5B,SAAS,EAAEuC,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAEX,iBAAiB,CAACD,WAAW,EAAEY,WAAW,CAAC;EAAEzC,MAAM,CAACqC,cAAc,CAACR,WAAW,EAAE,WAAW,EAAE;IAAEO,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAOP,WAAW;AAAE;AAE5R,OAAOa,0BAA0B,MAAM,wCAAwC;AAC/E,OAAOC,+DAA+D,MAAM,8EAA8E;AAC1J,OAAOC,iDAAiD,MAAM,gEAAgE;AAC9H,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,SAASC,YAAY,EAAEC,iBAAiB,EAAEC,UAAU,QAAQ,gBAAgB;AAC5E,IAAIC,wCAAwC,GAAG,GAAG,GAAGF,iBAAiB,GAAGD,YAAY,GAAG,IAAI;AAC5F,IAAII,gDAAgD,GAAG,IAAIC,MAAM,CAAC,GAAG,GAAGF,wCAAwC,GAAG,GAAG,EAAE,GAAG,CAAC;AAC5H,IAAIG,iCAAiC,GAAG,KAAK,GAAG,GAAG,GAAGJ,UAAU,GAAG,GAAG,GAAG,GAAG,GAAGD,iBAAiB,GAAGD,YAAY,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAGC,iBAAiB,GAAGD,YAAY,GAAG,IAAI,GAAG,GAAG;AAClL,IAAIO,qCAAqC,GAAG,IAAIF,MAAM,CAAC,IAAI,GAAGJ,iBAAiB,GAAGD,YAAY,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC;AACrH;AACA;;AAEA,IAAIQ,uBAAuB,GAAG,WAAW;AAEzC,IAAIC,eAAe,GAAG,aAAa,YAAY;EAC7C,SAASA,eAAeA,CAACC,IAAI,EAAE;IAC7B,IAAIC,cAAc,GAAGD,IAAI,CAACC,cAAc;MACpCC,kBAAkB,GAAGF,IAAI,CAACE,kBAAkB;MAC5CC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;MACxBC,iCAAiC,GAAGJ,IAAI,CAACI,iCAAiC;IAE9ElC,eAAe,CAAC,IAAI,EAAE6B,eAAe,CAAC;IAEtC,IAAI,CAACE,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,iCAAiC,GAAGA,iCAAiC;EAC5E;EAEAtB,YAAY,CAACiB,eAAe,EAAE,CAAC;IAC7BlB,GAAG,EAAE,OAAO;IACZd,KAAK,EAAE,SAASsC,KAAKA,CAACC,IAAI,EAAEC,KAAK,EAAE;MACjC,IAAIC,qBAAqB,GAAGC,6BAA6B,CAACH,IAAI,CAAC;QAC3DI,sBAAsB,GAAG/E,cAAc,CAAC6E,qBAAqB,EAAE,CAAC,CAAC;QACjEG,eAAe,GAAGD,sBAAsB,CAAC,CAAC,CAAC;QAC3CE,OAAO,GAAGF,sBAAsB,CAAC,CAAC,CAAC;MAEvC,IAAIG,MAAM,GAAGxB,WAAW,CAACsB,eAAe,CAAC,CAAC,CAAC;;MAE3C,IAAIG,eAAe;MAEnB,IAAIF,OAAO,EAAE;QACX,IAAI,CAACL,KAAK,CAACM,MAAM,EAAE;UACjBN,KAAK,CAACQ,wBAAwB,CAAC,CAAC;UAEhC,IAAI,CAACF,MAAM,EAAE;YACXC,eAAe,GAAG,IAAI;UACxB;QACF;MACF;MAEA,IAAID,MAAM,EAAE;QACV,IAAI,CAACG,WAAW,CAACH,MAAM,EAAEN,KAAK,CAAC;MACjC;MAEA,OAAO;QACLM,MAAM,EAAEA,MAAM;QACdC,eAAe,EAAEA;MACnB,CAAC;IACH;IACA;AACJ;AACA;AACA;AACA;EAEE,CAAC,EAAE;IACDjC,GAAG,EAAE,aAAa;IAClBd,KAAK,EAAE,SAASiD,WAAWA,CAACC,UAAU,EAAEV,KAAK,EAAE;MAC7C,IAAIM,MAAM,GAAGN,KAAK,CAACM,MAAM;MACzB,IAAIK,6BAA6B,GAAGL,MAAM,CAAC3D,MAAM,GAAG,CAAC,IAAI2D,MAAM,CAAC3D,MAAM,GAAG+D,UAAU,CAAC/D,MAAM,IAAI,CAAC,CAAC,CAAC;;MAEjGqD,KAAK,CAACY,YAAY,CAACF,UAAU,CAAC,CAAC,CAAC;MAChC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA,IAAIC,6BAA6B,EAAE;QACjC,IAAI,CAACE,gBAAgB,CAACb,KAAK,CAAC;MAC9B;MAEA,IAAI,IAAI,CAACc,8BAA8B,CAACd,KAAK,CAAC,EAAE;QAC9C,IAAI,CAAC,IAAI,CAACe,yBAAyB,CAACf,KAAK,CAAC,EAAE;UAC1C;QACF;MACF,CAAC,MAAM;QACLA,KAAK,CAACgB,qCAAqC,CAACN,UAAU,CAAC;MACzD,CAAC,CAAC;MACF;MACA;MACA;MACA;;MAGA,IAAI,CAACV,KAAK,CAACiB,aAAa,EAAE;QACxB,IAAI,CAAC,IAAI,CAACC,qCAAqC,EAAE;UAC/C,IAAI,CAACC,gCAAgC,CAACnB,KAAK,CAACoB,iBAAiB,CAAC,CAAC,EAAE,UAAUC,WAAW,EAAE;YACtF,OAAOrB,KAAK,CAACsB,MAAM,CAACD,WAAW,CAAC;UAClC,CAAC,CAAC;QACJ;MACF;IACF;EACF,CAAC,EAAE;IACD/C,GAAG,EAAE,gCAAgC;IACrCd,KAAK,EAAE,SAASsD,8BAA8BA,CAACS,KAAK,EAAE;MACpD,IAAIN,aAAa,GAAGM,KAAK,CAACN,aAAa;QACnCO,WAAW,GAAGD,KAAK,CAACC,WAAW;MACnC,OAAOP,aAAa,IAAI,CAACO,WAAW;IACtC,CAAC,CAAC;IACF;EAEF,CAAC,EAAE;IACDlD,GAAG,EAAE,2BAA2B;IAChCd,KAAK,EAAE,SAASuD,yBAAyBA,CAACf,KAAK,EAAE;MAC/C,IAAIyB,qBAAqB,GAAG/C,0BAA0B,CAAC,GAAG,GAAGsB,KAAK,CAAC0B,mCAAmC,CAAC,CAAC,EAAE,IAAI,CAAChC,cAAc,EAAE,IAAI,CAACC,kBAAkB,EAAE,IAAI,CAACC,QAAQ,CAACA,QAAQ,CAAC;QAC3K+B,kBAAkB,GAAGF,qBAAqB,CAACE,kBAAkB;QAC7DC,MAAM,GAAGH,qBAAqB,CAACG,MAAM;MAEzC,IAAID,kBAAkB,EAAE;QACtB3B,KAAK,CAAC6B,cAAc,CAACF,kBAAkB,CAAC;QACxC3B,KAAK,CAACsB,MAAM,CAAC;UACXQ,yBAAyB,EAAEF;QAC7B,CAAC,CAAC;QACF,OAAO,IAAI;MACb;IACF;EACF,CAAC,EAAE;IACDtD,GAAG,EAAE,OAAO;IACZd,KAAK,EAAE,SAASuE,KAAKA,CAACC,aAAa,EAAE;MACnC,IAAIA,aAAa,EAAE;QACjB,IAAI,CAACC,wBAAwB,GAAG,IAAI;QAEpC,IAAIC,wBAAwB,GAAGF,aAAa,CAACG,yBAAyB,CAAC,CAAC;QAExE,IAAI,CAACC,oDAAoD,GAAGF,wBAAwB,IAAI3C,uBAAuB,CAAC9C,IAAI,CAACyF,wBAAwB,CAAC;MAChJ,CAAC,MAAM;QACL,IAAI,CAACD,wBAAwB,GAAGI,SAAS;QACzC,IAAI,CAACD,oDAAoD,GAAGC,SAAS;MACvE;IACF;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;EAEE,CAAC,EAAE;IACD/D,GAAG,EAAE,kCAAkC;IACvCd,KAAK,EAAE,SAAS2D,gCAAgCA,CAACmB,cAAc,EAAEC,QAAQ,EAAE;MACzE,IAAI,CAAC,IAAI,CAACN,wBAAwB,EAAE;QAClC;MACF;MAEA,IAAIO,qBAAqB,GAAG5D,iDAAiD,CAAC0D,cAAc,EAAE,IAAI,CAAC1C,QAAQ,CAAC;QACxG6C,cAAc,GAAGD,qBAAqB,CAACC,cAAc;QACrDC,cAAc,GAAGF,qBAAqB,CAACE,cAAc;QACrDC,WAAW,GAAGH,qBAAqB,CAACG,WAAW;MAEnD,IAAID,cAAc,KAAKJ,cAAc,EAAE;QACrC;MACF;MAEA,IAAI,CAACM,yBAAyB,CAACH,cAAc,EAAEE,WAAW,EAAED,cAAc,EAAEJ,cAAc,EAAEC,QAAQ,CAAC;MACrG,OAAO,IAAI;IACb;IACA;AACJ;AACA;AACA;AACA;EAEE,CAAC,EAAE;IACDjE,GAAG,EAAE,yCAAyC;IAC9Cd,KAAK,EAAE,SAASqF,uCAAuCA,CAACP,cAAc,EAAEQ,6BAA6B,EAAEP,QAAQ,EAAE;MAC/G,IAAI,CAAC,IAAI,CAACrB,qCAAqC,EAAE;QAC/C,OAAO,IAAI,CAACC,gCAAgC,CAACmB,cAAc,EAAEC,QAAQ,CAAC;MACxE;MAEA,IAAI,CAAC,IAAI,CAACH,oDAAoD,EAAE;QAC9D;MACF;MAEA,IAAIW,sBAAsB,GAAGnE,iDAAiD,CAAC0D,cAAc,EAAE,IAAI,CAAC1C,QAAQ,CAAC;QACzG6C,cAAc,GAAGM,sBAAsB,CAACN,cAAc;QACtDC,cAAc,GAAGK,sBAAsB,CAACL,cAAc;QACtDC,WAAW,GAAGI,sBAAsB,CAACJ,WAAW,CAAC,CAAC;MACtD;MACA;MACA;MACA;;MAEA;;MAGA,IAAID,cAAc,KAAKI,6BAA6B,EAAE;QACpD;MACF;MAEA,IAAI,CAACF,yBAAyB,CAACH,cAAc,EAAEE,WAAW,EAAED,cAAc,EAAEJ,cAAc,EAAEC,QAAQ,CAAC;MACrG,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACDjE,GAAG,EAAE,2BAA2B;IAChCd,KAAK,EAAE,SAASoF,yBAAyBA,CAACH,cAAc,EAAEE,WAAW,EAAEb,yBAAyB,EAAEQ,cAAc,EAAEC,QAAQ,EAAE;MAC1H,IAAIS,4CAA4C;MAChD,IAAIC,qCAAqC,CAAC,CAAC;;MAE3C,IAAIC,8BAA8B,GAAGZ,cAAc,CAACa,WAAW,CAACrB,yBAAyB,CAAC,CAAC,CAAC;MAC5F;MACA;MACA;MACA;;MAEA,IAAIoB,8BAA8B,IAAI,CAAC,IAAIA,8BAA8B,KAAKZ,cAAc,CAAC3F,MAAM,GAAGmF,yBAAyB,CAACnF,MAAM,EAAE;QACtIsG,qCAAqC,GAAG,IAAI,CAAC,CAAC;QAC9C;QACA;QACA;QACA;;QAEA,IAAIG,0BAA0B,GAAGd,cAAc,CAAClG,KAAK,CAAC,CAAC,EAAE8G,8BAA8B,CAAC,CAAC,CAAC;QAC1F;QACA;QACA;QACA;;QAEA,IAAIE,0BAA0B,KAAKX,cAAc,EAAE;UACjDO,4CAA4C,GAAGI,0BAA0B;QAC3E;MACF;MAEAb,QAAQ,CAAC;QACPE,cAAc,EAAEA,cAAc;QAC9BE,WAAW,EAAEA,WAAW;QACxBb,yBAAyB,EAAEA,yBAAyB;QACpDmB,qCAAqC,EAAEA,qCAAqC;QAC5ED,4CAA4C,EAAEA;MAChD,CAAC,CAAC,CAAC,CAAC;MACJ;;MAEA,IAAI,CAAC9B,qCAAqC,GAAG,IAAI;MACjD,IAAI,CAACrB,iCAAiC,CAAC,CAAC;IAC1C;EACF,CAAC,EAAE;IACDvB,GAAG,EAAE,oCAAoC;IACzCd,KAAK,EAAE,SAAS6F,kCAAkCA,CAACrD,KAAK,EAAE;MACxD;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,IAAI,CAAC6C,uCAAuC,CAAC7C,KAAK,CAACoB,iBAAiB,CAAC,CAAC,EAAEpB,KAAK,CAAC8B,yBAAyB,EAAE,UAAUT,WAAW,EAAE;QAClI,OAAOrB,KAAK,CAACsB,MAAM,CAACD,WAAW,CAAC;MAClC,CAAC,CAAC,EAAE;QACF,OAAO,IAAI;MACb,CAAC,CAAC;MACF;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAGA,IAAI,IAAI,CAACR,gBAAgB,CAACb,KAAK,CAAC,EAAE;QAChC,IAAI,CAACsD,8CAA8C,CAACtD,KAAK,CAAC;QAC1D,OAAO,IAAI;MACb,CAAC,CAAC;MACF;MACA;MACA;MACA;MACA;MACA;;MAGA,IAAI,IAAI,CAACuD,cAAc,CAACvD,KAAK,CAAC,EAAE;QAC9B,IAAI,CAACsD,8CAA8C,CAACtD,KAAK,CAAC;QAC1D,OAAO,IAAI;MACb;IACF;EACF,CAAC,EAAE;IACD1B,GAAG,EAAE,kBAAkB;IACvBd,KAAK,EAAE,SAASqD,gBAAgBA,CAACb,KAAK,EAAE;MACtC;MACA;MACA,IAAIiB,aAAa,GAAGjB,KAAK,CAACiB,aAAa;QACnCuC,SAAS,GAAGxD,KAAK,CAACwD,SAAS;QAC3BlD,MAAM,GAAGN,KAAK,CAACM,MAAM;QACrBwB,yBAAyB,GAAG9B,KAAK,CAAC8B,yBAAyB;MAE/D,IAAIb,aAAa,IAAIuC,SAAS,EAAE;QAC9B;MACF,CAAC,CAAC;MACF;MACA;MACA;;MAGA,IAAIC,gBAAgB,GAAG5E,cAAc,CAACyB,MAAM,EAAE,IAAI,CAACZ,cAAc,EAAE,IAAI,CAACC,kBAAkB,EAAE,IAAI,CAACC,QAAQ,CAACA,QAAQ,CAAC;MAEnH,IAAI6D,gBAAgB,KAAKpB,SAAS,IAAIoB,gBAAgB,KAAKnD,MAAM,EAAE;QACjE;QACA;QACAN,KAAK,CAACsB,MAAM,CAAC;UACXkC,SAAS,EAAElD,MAAM,CAAClE,KAAK,CAAC,CAAC,EAAEkE,MAAM,CAAC3D,MAAM,GAAG8G,gBAAgB,CAAC9G,MAAM;QACpE,CAAC,CAAC;QACF,IAAI,CAAC6D,wBAAwB,CAACR,KAAK,EAAE;UACnC0D,OAAO,EAAErB,SAAS;UAClBb,WAAW,EAAEa;QACf,CAAC,CAAC;QACF,OAAO,IAAI;MACb;IACF;EACF,CAAC,EAAE;IACD/D,GAAG,EAAE,gBAAgB;IACrBd,KAAK,EAAE,SAAS+F,cAAcA,CAACvD,KAAK,EAAE;MACpC,IAAI,CAACA,KAAK,CAACiB,aAAa,EAAE;QACxB,IAAI0C,sBAAsB,GAAGhF,+DAA+D,CAACqB,KAAK,CAACM,MAAM,EAAE,IAAI,CAACZ,cAAc,EAAE,IAAI,CAACC,kBAAkB,EAAE,IAAI,CAACC,QAAQ,CAACA,QAAQ,CAAC;UAC5KgE,cAAc,GAAGD,sBAAsB,CAAChC,kBAAkB;UAC1DC,MAAM,GAAG+B,sBAAsB,CAAC/B,MAAM;QAE1C,IAAIgC,cAAc,EAAE;UAClB5D,KAAK,CAACsB,MAAM,CAAC;YACXuC,WAAW,EAAE;UACf,CAAC,CAAC;UACF,IAAI,CAACrD,wBAAwB,CAACR,KAAK,EAAE;YACnC0D,OAAO,EAAE1D,KAAK,CAAC0D,OAAO;YACtBlC,WAAW,EAAEoC;UACf,CAAC,CAAC;UACF,OAAO,IAAI;QACb;MACF;IACF;EACF,CAAC,EAAE;IACDtF,GAAG,EAAE,0BAA0B;IAC/Bd,KAAK,EAAE,SAASgD,wBAAwBA,CAACR,KAAK,EAAE8D,KAAK,EAAE;MACrD,IAAIJ,OAAO,GAAGI,KAAK,CAACJ,OAAO;QACvBlC,WAAW,GAAGsC,KAAK,CAACtC,WAAW;MACnCxB,KAAK,CAACQ,wBAAwB,CAACkD,OAAO,EAAElC,WAAW,CAAC,CAAC,CAAC;;MAEtD,IAAIxB,KAAK,CAAC8B,yBAAyB,EAAE;QACnC9B,KAAK,CAAC+D,8BAA8B,CAAC,CAAC;QACtC,IAAI,CAAClE,iCAAiC,CAAC,CAAC;QACxC,IAAI,CAACqB,qCAAqC,GAAGmB,SAAS;MACxD;IACF;EACF,CAAC,EAAE;IACD/D,GAAG,EAAE,gDAAgD;IACrDd,KAAK,EAAE,SAAS8F,8CAA8CA,CAACtD,KAAK,EAAE;MACpE,IAAI,IAAI,CAACe,yBAAyB,CAACf,KAAK,CAAC,EAAE;QACzC;QACA;QACA;QACA;QACA;QACA;QACA,IAAI,CAACmB,gCAAgC,CAACnB,KAAK,CAACoB,iBAAiB,CAAC,CAAC,EAAE,UAAUC,WAAW,EAAE;UACtF,OAAOrB,KAAK,CAACsB,MAAM,CAACD,WAAW,CAAC;QAClC,CAAC,CAAC;MACJ;IACF;EACF,CAAC,CAAC,CAAC;EAEH,OAAO7B,eAAe;AACxB,CAAC,CAAC,CAAC;AACH;AACA;AACA;AACA;AACA;;AAGA,SAASA,eAAe,IAAIwE,OAAO;AAEnC,SAASC,2BAA2BA,CAAClE,IAAI,EAAE;EACzC;EACA,IAAImE,QAAQ,GAAGnE,IAAI,CAACoE,MAAM,CAAC9E,iCAAiC,CAAC;EAE7D,IAAI6E,QAAQ,GAAG,CAAC,EAAE;IAChB;EACF,CAAC,CAAC;;EAGFnE,IAAI,GAAGA,IAAI,CAAC3D,KAAK,CAAC8H,QAAQ,CAAC,CAAC,CAAC;;EAE7B,IAAI7D,OAAO;EAEX,IAAIN,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACnBM,OAAO,GAAG,IAAI;IACdN,IAAI,GAAGA,IAAI,CAAC3D,KAAK,CAAC,GAAG,CAACO,MAAM,CAAC;EAC/B,CAAC,CAAC;;EAGFoD,IAAI,GAAGA,IAAI,CAACqE,OAAO,CAAC9E,qCAAqC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAEhE,IAAIe,OAAO,EAAE;IACXN,IAAI,GAAG,GAAG,GAAGA,IAAI;EACnB;EAEA,OAAOA,IAAI;AACb;AACA;AACA;AACA;AACA;AACA;;AAGA,SAASsE,8BAA8BA,CAACtE,IAAI,EAAE;EAC5C;EACA,IAAIuE,eAAe,GAAGL,2BAA2B,CAAClE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;;EAE/D,IAAIuE,eAAe,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAC9B,OAAO,CAACA,eAAe,CAAClI,KAAK,CAAC,GAAG,CAACO,MAAM,CAAC,EAAE,IAAI,CAAC;EAClD;EAEA,OAAO,CAAC2H,eAAe,CAAC;AAC1B;AACA;AACA;AACA;AACA;AACA;;AAGA,OAAO,SAASpE,6BAA6BA,CAACH,IAAI,EAAE;EAClD,IAAIwE,sBAAsB,GAAGF,8BAA8B,CAACtE,IAAI,CAAC;IAC7DyE,sBAAsB,GAAGpJ,cAAc,CAACmJ,sBAAsB,EAAE,CAAC,CAAC;IAClEnE,eAAe,GAAGoE,sBAAsB,CAAC,CAAC,CAAC;IAC3CnE,OAAO,GAAGmE,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC;EACzC;EACA;;EAGA,IAAI,CAACrF,gDAAgD,CAAC1C,IAAI,CAAC2D,eAAe,CAAC,EAAE;IAC3EA,eAAe,GAAG,EAAE;EACtB;EAEA,OAAO,CAACA,eAAe,EAAEC,OAAO,CAAC;AACnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}