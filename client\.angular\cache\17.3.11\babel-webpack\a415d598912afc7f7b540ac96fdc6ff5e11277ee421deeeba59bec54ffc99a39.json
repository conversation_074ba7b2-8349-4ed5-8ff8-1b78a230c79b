{"ast": null, "code": "import PhoneNumberMatcher from '../PhoneNumberMatcher.js';\nimport normalizeArguments from '../normalizeArguments.js';\nexport default function findNumbers() {\n  var _normalizeArguments = normalizeArguments(arguments),\n    text = _normalizeArguments.text,\n    options = _normalizeArguments.options,\n    metadata = _normalizeArguments.metadata;\n  var matcher = new PhoneNumberMatcher(text, options, metadata);\n  var results = [];\n  while (matcher.hasNext()) {\n    results.push(matcher.next());\n  }\n  return results;\n}", "map": {"version": 3, "names": ["PhoneNumberMatcher", "normalizeArguments", "findNumbers", "_normalizeArguments", "arguments", "text", "options", "metadata", "matcher", "results", "hasNext", "push", "next"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/legacy/findNumbers.js"], "sourcesContent": ["import PhoneNumberMatcher from '../PhoneNumberMatcher.js';\nimport normalizeArguments from '../normalizeArguments.js';\nexport default function findNumbers() {\n  var _normalizeArguments = normalizeArguments(arguments),\n      text = _normalizeArguments.text,\n      options = _normalizeArguments.options,\n      metadata = _normalizeArguments.metadata;\n\n  var matcher = new PhoneNumberMatcher(text, options, metadata);\n  var results = [];\n\n  while (matcher.hasNext()) {\n    results.push(matcher.next());\n  }\n\n  return results;\n}\n"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,0BAA0B;AACzD,OAAOC,kBAAkB,MAAM,0BAA0B;AACzD,eAAe,SAASC,WAAWA,CAAA,EAAG;EACpC,IAAIC,mBAAmB,GAAGF,kBAAkB,CAACG,SAAS,CAAC;IACnDC,IAAI,GAAGF,mBAAmB,CAACE,IAAI;IAC/BC,OAAO,GAAGH,mBAAmB,CAACG,OAAO;IACrCC,QAAQ,GAAGJ,mBAAmB,CAACI,QAAQ;EAE3C,IAAIC,OAAO,GAAG,IAAIR,kBAAkB,CAACK,IAAI,EAAEC,OAAO,EAAEC,QAAQ,CAAC;EAC7D,IAAIE,OAAO,GAAG,EAAE;EAEhB,OAAOD,OAAO,CAACE,OAAO,CAAC,CAAC,EAAE;IACxBD,OAAO,CAACE,IAAI,CAACH,OAAO,CAACI,IAAI,CAAC,CAAC,CAAC;EAC9B;EAEA,OAAOH,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}