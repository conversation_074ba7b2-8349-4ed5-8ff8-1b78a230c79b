{"version": 3, "file": "normalizeArguments.js", "names": ["isObject", "normalizeArguments", "args", "Array", "prototype", "slice", "call", "arg_1", "arg_2", "arg_3", "arg_4", "text", "options", "metadata", "TypeError", "undefined", "defaultCountry", "Error"], "sources": ["../source/normalizeArguments.js"], "sourcesContent": ["import isObject from './helpers/isObject.js'\r\n\r\n// Extracts the following properties from function arguments:\r\n// * input `text`\r\n// * `options` object\r\n// * `metadata` JSON\r\nexport default function normalizeArguments(args) {\r\n\tconst [arg_1, arg_2, arg_3, arg_4] = Array.prototype.slice.call(args)\r\n\r\n\tlet text\r\n\tlet options\r\n\tlet metadata\r\n\r\n\t// If the phone number is passed as a string.\r\n\t// `parsePhoneNumber('88005553535', ...)`.\r\n\tif (typeof arg_1 === 'string') {\r\n\t\ttext = arg_1\r\n\t}\r\n\telse throw new TypeError('A text for parsing must be a string.')\r\n\r\n\t// If \"default country\" argument is being passed then move it to `options`.\r\n\t// `parsePhoneNumber('88005553535', 'RU', [options], metadata)`.\r\n\tif (!arg_2 || typeof arg_2 === 'string')\r\n\t{\r\n\t\tif (arg_4) {\r\n\t\t\toptions = arg_3\r\n\t\t\tmetadata = arg_4\r\n\t\t} else {\r\n\t\t\toptions = undefined\r\n\t\t\tmetadata = arg_3\r\n\t\t}\r\n\r\n\t\tif (arg_2) {\r\n\t\t\toptions = { defaultCountry: arg_2, ...options }\r\n\t\t}\r\n\t}\r\n\t// `defaultCountry` is not passed.\r\n\t// Example: `parsePhoneNumber('+78005553535', [options], metadata)`.\r\n\telse if (isObject(arg_2))\r\n\t{\r\n\t\tif (arg_3) {\r\n\t\t\toptions  = arg_2\r\n\t\t\tmetadata = arg_3\r\n\t\t} else {\r\n\t\t\tmetadata = arg_2\r\n\t\t}\r\n\t}\r\n\telse throw new Error(`Invalid second argument: ${arg_2}`)\r\n\r\n\treturn {\r\n\t\ttext,\r\n\t\toptions,\r\n\t\tmetadata\r\n\t}\r\n}"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,OAAOA,QAAP,MAAqB,uBAArB,C,CAEA;AACA;AACA;AACA;;AACA,eAAe,SAASC,kBAAT,CAA4BC,IAA5B,EAAkC;EAChD,4BAAqCC,KAAK,CAACC,SAAN,CAAgBC,KAAhB,CAAsBC,IAAtB,CAA2BJ,IAA3B,CAArC;EAAA;EAAA,IAAOK,KAAP;EAAA,IAAcC,KAAd;EAAA,IAAqBC,KAArB;EAAA,IAA4BC,KAA5B;;EAEA,IAAIC,IAAJ;EACA,IAAIC,OAAJ;EACA,IAAIC,QAAJ,CALgD,CAOhD;EACA;;EACA,IAAI,OAAON,KAAP,KAAiB,QAArB,EAA+B;IAC9BI,IAAI,GAAGJ,KAAP;EACA,CAFD,MAGK,MAAM,IAAIO,SAAJ,CAAc,sCAAd,CAAN,CAZ2C,CAchD;EACA;;;EACA,IAAI,CAACN,KAAD,IAAU,OAAOA,KAAP,KAAiB,QAA/B,EACA;IACC,IAAIE,KAAJ,EAAW;MACVE,OAAO,GAAGH,KAAV;MACAI,QAAQ,GAAGH,KAAX;IACA,CAHD,MAGO;MACNE,OAAO,GAAGG,SAAV;MACAF,QAAQ,GAAGJ,KAAX;IACA;;IAED,IAAID,KAAJ,EAAW;MACVI,OAAO;QAAKI,cAAc,EAAER;MAArB,GAA+BI,OAA/B,CAAP;IACA;EACD,CAbD,CAcA;EACA;EAfA,KAgBK,IAAIZ,QAAQ,CAACQ,KAAD,CAAZ,EACL;IACC,IAAIC,KAAJ,EAAW;MACVG,OAAO,GAAIJ,KAAX;MACAK,QAAQ,GAAGJ,KAAX;IACA,CAHD,MAGO;MACNI,QAAQ,GAAGL,KAAX;IACA;EACD,CARI,MASA,MAAM,IAAIS,KAAJ,oCAAsCT,KAAtC,EAAN;;EAEL,OAAO;IACNG,IAAI,EAAJA,IADM;IAENC,OAAO,EAAPA,OAFM;IAGNC,QAAQ,EAARA;EAHM,CAAP;AAKA"}