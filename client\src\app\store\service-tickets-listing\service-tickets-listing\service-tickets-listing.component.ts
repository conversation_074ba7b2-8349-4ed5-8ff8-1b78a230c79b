import { Component } from '@angular/core';
import { MenuItem, MessageService, SortEvent } from 'primeng/api';
import { ServiceTicketService } from '../../services/service-ticket.service';
import { AuthService } from 'src/app/core/authentication/auth.service';
import { forkJoin, map, tap } from 'rxjs';
import { stringify } from 'qs';
import { Router } from '@angular/router';
import { AccountService } from '../../account/account.service';

interface OrgColumn {
  field: string;
  header: string;
  width?: string;
}

@Component({
  selector: 'app-service-tickets-listing',
  templateUrl: './service-tickets-listing.component.html',
  styleUrl: './service-tickets-listing.component.scss'
})
export class ServiceTicketsListingComponent {
  items: MenuItem[] | any = [
    { label: 'Tickets', routerLink: ['/store/service-tickets'] },
  ];
  home: MenuItem | any = { icon: 'pi pi-home', routerLink: ['/'] };

  statuses: any[] = [];
  tickets: any[] = [];
  loading = false;
  totalRecords = 0;
  sellerDetails: any = {};
  searchParams: any = {
    fromDate: "",
    toDate: "",
    ticketNo: '',
    status: "all",
  };
  statusByCode: any = {};

  maxDate = new Date();

  constructor(
    private service: ServiceTicketService,
    private accountService: AccountService,
    private _snackBar: MessageService,
    private router: Router,
    public authService: AuthService,
  ) {
    this.sellerDetails = {
      ...this.authService.partnerFunction
    }
  }

  private _selectedOrgColumns: OrgColumn[] = [];


  public OrgCols: OrgColumn[] = [
    { field: 'account_id', header: 'Account Id', width: '130px' },
    { field: 'contact_id', header: 'Contact Id', width: '130px' },
    { field: 'support_team', header: 'Created By', width: '130px' },
    { field: 'assigned_to', header: 'Assigned To', width: '200px' },
    { field: 'status_id', header: 'Status', width: '130px' },
    { field: 'createdAt', header: 'Created On', width: '130px' },
    { field: 'description', header: 'Description' },
  ];

  sortFieldOrg: string = '';
  sortOrderOrg: number = 1;

  ngOnInit(): void {
    this.loadOptions();

    this._selectedOrgColumns = this.OrgCols;
  }

  loadTickets(event: any) {
    this.loading = true;
    const page = event.first / event.rows + 1;
    const pageSize = event.rows;
    const sortField = event.sortField;
    const sortOrder = event.sortOrder;

    // Build filters object
    const obj: any = {
      filters: {
        $and: []
      },
      pagination: {
        page: page,
        pageSize: pageSize
      }
    };

    // Add search filters
    if (this.searchParams.ticketNo) {
      obj.filters.$and.push({
        id: {
          $eq: this.searchParams.ticketNo
        }
      });
    } else {
      if (this.searchParams.fromDate) {
        obj.filters.$and.push({
          createdAt: {
            $gte: this.searchParams.fromDate
          }
        });
      }
      if (this.searchParams.toDate) {
        const to = new Date(this.searchParams.toDate);
        to.setHours(23, 59, 59, 999);
        obj.filters.$and.push({
          createdAt: {
            $lte: to
          }
        });
      }
      if (this.searchParams.status && this.searchParams.status != "all") {
        obj.filters.$and.push({
          status_id: {
            $eq: this.searchParams.status
          }
        });
      }
    }

    // Add sorting
    if (sortField && sortOrder !== undefined) {
      const order = sortOrder === 1 ? 'asc' : 'desc';
      obj.sort = `${sortField}:${order}`;
    }

    const query = stringify(obj);
    this.service.getAll(query).pipe(
      map((response) => {
        this.tickets = response.data || [];
        this.totalRecords = response.meta?.pagination?.total || 0;
        return response.data;
      }),
      tap((_) => (this.loading = false))
    ).subscribe({
      error: (error) => {
        console.error('Error fetching tickets', error);
        this.loading = false;
      }
    });
  }

  get selectedOrgColumns(): any[] {
    return this._selectedOrgColumns;
  }

  set selectedOrgColumns(val: any[]) {
    this._selectedOrgColumns = this.OrgCols.filter(col => val.includes(col));
  }

  onOrgColumnReorder(event: any) {
    const draggedCol = this.OrgCols[event.dragIndex];
    this.OrgCols.splice(event.dragIndex, 1);
    this.OrgCols.splice(event.dropIndex, 0, draggedCol);
  }

  customSort(field: string, data: any[], type: 'ORG') {
    if (type === 'ORG') {
      if (this.sortFieldOrg === field) {
        // Toggle sort order if same column is clicked
        this.sortOrderOrg = this.sortOrderOrg === 1 ? -1 : 1;
      } else {
        // Reset to ascending when changing columns
        this.sortFieldOrg = field;
        this.sortOrderOrg = 1;
      }
    }

    data.sort((a, b) => {
      const value1 = this.resolveFieldData(a, field);
      const value2 = this.resolveFieldData(b, field);

      let result = 0;

      if (value1 == null && value2 != null) result = -1;
      else if (value1 != null && value2 == null) result = 1;
      else if (value1 == null && value2 == null) result = 0;
      else if (typeof value1 === 'string' && typeof value2 === 'string')
        result = value1.localeCompare(value2);
      else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;

      return this.sortOrderOrg * result;
    });
  }

  resolveFieldData(data: any, field: string): any {
    if (!data || !field) return null;

    if (field.indexOf('.') === -1) {
      return data[field];
    } else {
      let fields = field.split('.');
      let value = data;
      for (let i = 0; i < fields.length; i++) {
        if (value == null) return null;
        value = value[fields[i]];
      }
      return value;
    }
  }

  search(): void {
    // Reset to first page and trigger lazy load
    this.loadTickets({ first: 0, rows: 10 });
  }

  loadOptions() {
    this.loading = true;
    forkJoin([
      this.service.getAllTicketStatus(),
    ]).subscribe({
      next: (results) => {
        this.statuses = [
          { code: "all", description: "All" },
          ...results[0].data,
        ];
        this.searchParams.status = this.statuses[0].code;
        this.statuses.reduce((acc: { [x: string]: any; }, value: { code: string | number; description: any; }) => {
          acc[value.code] = value.description;
          return acc;
        }, this.statusByCode);
        // Load initial data
        this.loadTickets({ first: 0, rows: 10 });
      },
      error: () => {
        this.loading = false;
      },
    });
  }

  clear() {
    this.searchParams = {
      fromDate: "",
      toDate: "",
      ticketNo: "",
      status: this.statuses[0].code,
    };
    // Reset to first page and reload data
    this.loadTickets({ first: 0, rows: 10 });
  }

  // customSort(event: SortEvent) {
  //   const sort = {
  //     DAYS_PAST_DUE: (a: any, b: any) => {
  //       return (parseInt(a.SD_DOC) - parseInt(b.SD_DOC)) * (event.order || 1);
  //     },
  //     All: (a: any, b: any) => {
  //       if (a[event.field || ''] < b[event.field || '']) return -1 * (event.order || 1);
  //       if (a[event.field || ''] > b[event.field || '']) return 1 * (event.order || 1);
  //       return 0;
  //     }
  //   };
  //   event.data?.sort(event.field == "DAYS_PAST_DUE" ? sort.DAYS_PAST_DUE : sort.All);
  // }

  goToTicket(event: any) {
    const params = stringify({
      filters: {
        $and: [
          {
            bp_id: {
              $eq: [event.data.account_id]
            }
          }
        ]
      },
    });
    this.accountService.search(params).subscribe((res: any) => {
      if (res?.length) {
        this.router.navigate(['/store/service-ticket-details', event.data.id, res[0].documentId]);
      }
    });
  }
}
