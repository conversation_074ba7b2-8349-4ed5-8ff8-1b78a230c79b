{"ast": null, "code": "function ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nimport normalizeArguments from './normalizeArguments.js';\nimport parsePhoneNumber from './parsePhoneNumber_.js';\nexport default function isPossiblePhoneNumber() {\n  var _normalizeArguments = normalizeArguments(arguments),\n    text = _normalizeArguments.text,\n    options = _normalizeArguments.options,\n    metadata = _normalizeArguments.metadata;\n  options = _objectSpread(_objectSpread({}, options), {}, {\n    extract: false\n  });\n  var phoneNumber = parsePhoneNumber(text, options, metadata);\n  return phoneNumber && phoneNumber.isPossible() || false;\n}", "map": {"version": 3, "names": ["ownKeys", "object", "enumerableOnly", "keys", "Object", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "target", "i", "arguments", "length", "source", "for<PERSON>ach", "key", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "obj", "value", "configurable", "writable", "normalizeArguments", "parsePhoneNumber", "isPossiblePhoneNumber", "_normalizeArguments", "text", "options", "metadata", "extract", "phoneNumber", "isPossible"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/isPossiblePhoneNumber.js"], "sourcesContent": ["function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nimport normalizeArguments from './normalizeArguments.js';\nimport parsePhoneNumber from './parsePhoneNumber_.js';\nexport default function isPossiblePhoneNumber() {\n  var _normalizeArguments = normalizeArguments(arguments),\n      text = _normalizeArguments.text,\n      options = _normalizeArguments.options,\n      metadata = _normalizeArguments.metadata;\n\n  options = _objectSpread(_objectSpread({}, options), {}, {\n    extract: false\n  });\n  var phoneNumber = parsePhoneNumber(text, options, metadata);\n  return phoneNumber && phoneNumber.isPossible() || false;\n}\n"], "mappings": "AAAA,SAASA,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACF,MAAM,CAAC;EAAE,IAAIG,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,OAAO,GAAGF,MAAM,CAACC,qBAAqB,CAACJ,MAAM,CAAC;IAAEC,cAAc,KAAKI,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;MAAE,OAAOJ,MAAM,CAACK,wBAAwB,CAACR,MAAM,EAAEO,GAAG,CAAC,CAACE,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEP,IAAI,CAACQ,IAAI,CAACC,KAAK,CAACT,IAAI,EAAEG,OAAO,CAAC;EAAE;EAAE,OAAOH,IAAI;AAAE;AAEpV,SAASU,aAAaA,CAACC,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAG,IAAI,IAAIF,SAAS,CAACD,CAAC,CAAC,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGf,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;MAAEC,eAAe,CAACP,MAAM,EAAEM,GAAG,EAAEF,MAAM,CAACE,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGhB,MAAM,CAACkB,yBAAyB,GAAGlB,MAAM,CAACmB,gBAAgB,CAACT,MAAM,EAAEV,MAAM,CAACkB,yBAAyB,CAACJ,MAAM,CAAC,CAAC,GAAGlB,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;MAAEhB,MAAM,CAACoB,cAAc,CAACV,MAAM,EAAEM,GAAG,EAAEhB,MAAM,CAACK,wBAAwB,CAACS,MAAM,EAAEE,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAON,MAAM;AAAE;AAEzf,SAASO,eAAeA,CAACI,GAAG,EAAEL,GAAG,EAAEM,KAAK,EAAE;EAAE,IAAIN,GAAG,IAAIK,GAAG,EAAE;IAAErB,MAAM,CAACoB,cAAc,CAACC,GAAG,EAAEL,GAAG,EAAE;MAAEM,KAAK,EAAEA,KAAK;MAAEhB,UAAU,EAAE,IAAI;MAAEiB,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEH,GAAG,CAACL,GAAG,CAAC,GAAGM,KAAK;EAAE;EAAE,OAAOD,GAAG;AAAE;AAEhN,OAAOI,kBAAkB,MAAM,yBAAyB;AACxD,OAAOC,gBAAgB,MAAM,wBAAwB;AACrD,eAAe,SAASC,qBAAqBA,CAAA,EAAG;EAC9C,IAAIC,mBAAmB,GAAGH,kBAAkB,CAACb,SAAS,CAAC;IACnDiB,IAAI,GAAGD,mBAAmB,CAACC,IAAI;IAC/BC,OAAO,GAAGF,mBAAmB,CAACE,OAAO;IACrCC,QAAQ,GAAGH,mBAAmB,CAACG,QAAQ;EAE3CD,OAAO,GAAGrB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqB,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE;IACtDE,OAAO,EAAE;EACX,CAAC,CAAC;EACF,IAAIC,WAAW,GAAGP,gBAAgB,CAACG,IAAI,EAAEC,OAAO,EAAEC,QAAQ,CAAC;EAC3D,OAAOE,WAAW,IAAIA,WAAW,CAACC,UAAU,CAAC,CAAC,IAAI,KAAK;AACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}