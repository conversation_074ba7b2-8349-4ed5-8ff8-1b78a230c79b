{"ast": null, "code": "import { trimAfterFirstMatch } from './util.js'; // Regular expression of characters typically used to start a second phone number for the purposes\n// of parsing. This allows us to strip off parts of the number that are actually the start of\n// another number, such as for: (************* x302/x2303 -> the second extension here makes this\n// actually two phone numbers, (************* x302 and (************* x2303. We remove the second\n// extension so that the first number is parsed correctly.\n//\n// Matches a slash (\\ or /) followed by a space followed by an `x`.\n//\n\nvar SECOND_NUMBER_START_PATTERN = /[\\\\/] *x/;\nexport default function parsePreCandidate(candidate) {\n  // Check for extra numbers at the end.\n  // TODO: This is the place to start when trying to support extraction of multiple phone number\n  // from split notations (+41 79 123 45 67 / 68).\n  return trimAfterFirstMatch(SECOND_NUMBER_START_PATTERN, candidate);\n}", "map": {"version": 3, "names": ["trimAfterFirstMatch", "SECOND_NUMBER_START_PATTERN", "parsePreCandidate", "candidate"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/findNumbers/parsePreCandidate.js"], "sourcesContent": ["import { trimAfterFirstMatch } from './util.js'; // Regular expression of characters typically used to start a second phone number for the purposes\n// of parsing. This allows us to strip off parts of the number that are actually the start of\n// another number, such as for: (************* x302/x2303 -> the second extension here makes this\n// actually two phone numbers, (************* x302 and (************* x2303. We remove the second\n// extension so that the first number is parsed correctly.\n//\n// Matches a slash (\\ or /) followed by a space followed by an `x`.\n//\n\nvar SECOND_NUMBER_START_PATTERN = /[\\\\/] *x/;\nexport default function parsePreCandidate(candidate) {\n  // Check for extra numbers at the end.\n  // TODO: This is the place to start when trying to support extraction of multiple phone number\n  // from split notations (+41 79 123 45 67 / 68).\n  return trimAfterFirstMatch(SECOND_NUMBER_START_PATTERN, candidate);\n}\n"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,WAAW,CAAC,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,2BAA2B,GAAG,UAAU;AAC5C,eAAe,SAASC,iBAAiBA,CAACC,SAAS,EAAE;EACnD;EACA;EACA;EACA,OAAOH,mBAAmB,CAACC,2BAA2B,EAAEE,SAAS,CAAC;AACpE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}