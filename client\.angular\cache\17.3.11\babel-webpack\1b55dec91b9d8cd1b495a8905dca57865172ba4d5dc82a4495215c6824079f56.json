{"ast": null, "code": "/**\r\n * Checks whether the entire input sequence can be matched\r\n * against the regular expression.\r\n * @return {boolean}\r\n */\nexport default function matchesEntirely(text, regular_expression) {\n  // If assigning the `''` default value is moved to the arguments above,\n  // code coverage would decrease for some weird reason.\n  text = text || '';\n  return new RegExp('^(?:' + regular_expression + ')$').test(text);\n}", "map": {"version": 3, "names": ["matchesEntirely", "text", "regular_expression", "RegExp", "test"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/helpers/matchesEntirely.js"], "sourcesContent": ["/**\r\n * Checks whether the entire input sequence can be matched\r\n * against the regular expression.\r\n * @return {boolean}\r\n */\nexport default function matchesEntirely(text, regular_expression) {\n  // If assigning the `''` default value is moved to the arguments above,\n  // code coverage would decrease for some weird reason.\n  text = text || '';\n  return new RegExp('^(?:' + regular_expression + ')$').test(text);\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASA,eAAeA,CAACC,IAAI,EAAEC,kBAAkB,EAAE;EAChE;EACA;EACAD,IAAI,GAAGA,IAAI,IAAI,EAAE;EACjB,OAAO,IAAIE,MAAM,CAAC,MAAM,GAAGD,kBAAkB,GAAG,IAAI,CAAC,CAACE,IAAI,CAACH,IAAI,CAAC;AAClE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}