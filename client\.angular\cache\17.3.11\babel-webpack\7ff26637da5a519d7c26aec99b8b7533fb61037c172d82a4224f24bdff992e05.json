{"ast": null, "code": "import parsePhoneNumberWithError_ from './parsePhoneNumberWithError_.js';\nimport normalizeArguments from './normalizeArguments.js';\nexport default function parsePhoneNumberWithError() {\n  var _normalizeArguments = normalizeArguments(arguments),\n    text = _normalizeArguments.text,\n    options = _normalizeArguments.options,\n    metadata = _normalizeArguments.metadata;\n  return parsePhoneNumberWithError_(text, options, metadata);\n}", "map": {"version": 3, "names": ["parsePhoneNumberWithError_", "normalizeArguments", "parsePhoneNumberWithError", "_normalizeArguments", "arguments", "text", "options", "metadata"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/parsePhoneNumberWithError.js"], "sourcesContent": ["import parsePhoneNumberWithError_ from './parsePhoneNumberWithError_.js';\nimport normalizeArguments from './normalizeArguments.js';\nexport default function parsePhoneNumberWithError() {\n  var _normalizeArguments = normalizeArguments(arguments),\n      text = _normalizeArguments.text,\n      options = _normalizeArguments.options,\n      metadata = _normalizeArguments.metadata;\n\n  return parsePhoneNumberWithError_(text, options, metadata);\n}\n"], "mappings": "AAAA,OAAOA,0BAA0B,MAAM,iCAAiC;AACxE,OAAOC,kBAAkB,MAAM,yBAAyB;AACxD,eAAe,SAASC,yBAAyBA,CAAA,EAAG;EAClD,IAAIC,mBAAmB,GAAGF,kBAAkB,CAACG,SAAS,CAAC;IACnDC,IAAI,GAAGF,mBAAmB,CAACE,IAAI;IAC/BC,OAAO,GAAGH,mBAAmB,CAACG,OAAO;IACrCC,QAAQ,GAAGJ,mBAAmB,CAACI,QAAQ;EAE3C,OAAOP,0BAA0B,CAACK,IAAI,EAAEC,OAAO,EAAEC,QAAQ,CAAC;AAC5D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}