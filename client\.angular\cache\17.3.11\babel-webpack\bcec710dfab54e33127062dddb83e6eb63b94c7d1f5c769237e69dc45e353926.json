{"ast": null, "code": "/**\r\n * Strips any national prefix (such as 0, 1) present in a\r\n * (possibly incomplete) number provided.\r\n * \"Carrier codes\" are only used  in Colombia and Brazil,\r\n * and only when dialing within those countries from a mobile phone to a fixed line number.\r\n * Sometimes it won't actually strip national prefix\r\n * and will instead prepend some digits to the `number`:\r\n * for example, when number `2345678` is passed with `VI` country selected,\r\n * it will return `{ number: \"3402345678\" }`, because `340` area code is prepended.\r\n * @param {string} number — National number digits.\r\n * @param {object} metadata — Metadata with country selected.\r\n * @return {object} `{ nationalNumber: string, nationalPrefix: string? carrierCode: string? }`. Even if a national prefix was extracted, it's not necessarily present in the returned object, so don't rely on its presence in the returned object in order to find out whether a national prefix has been extracted or not.\r\n */\nexport default function extractNationalNumberFromPossiblyIncompleteNumber(number, metadata) {\n  if (number && metadata.numberingPlan.nationalPrefixForParsing()) {\n    // See METADATA.md for the description of\n    // `national_prefix_for_parsing` and `national_prefix_transform_rule`.\n    // Attempt to parse the first digits as a national prefix.\n    var prefixPattern = new RegExp('^(?:' + metadata.numberingPlan.nationalPrefixForParsing() + ')');\n    var prefixMatch = prefixPattern.exec(number);\n    if (prefixMatch) {\n      var nationalNumber;\n      var carrierCode; // https://gitlab.com/catamphetamine/libphonenumber-js/-/blob/master/METADATA.md#national_prefix_for_parsing--national_prefix_transform_rule\n      // If a `national_prefix_for_parsing` has any \"capturing groups\"\n      // then it means that the national (significant) number is equal to\n      // those \"capturing groups\" transformed via `national_prefix_transform_rule`,\n      // and nothing could be said about the actual national prefix:\n      // what is it and was it even there.\n      // If a `national_prefix_for_parsing` doesn't have any \"capturing groups\",\n      // then everything it matches is a national prefix.\n      // To determine whether `national_prefix_for_parsing` matched any\n      // \"capturing groups\", the value of the result of calling `.exec()`\n      // is looked at, and if it has non-undefined values where there're\n      // \"capturing groups\" in the regular expression, then it means\n      // that \"capturing groups\" have been matched.\n      // It's not possible to tell whether there'll be any \"capturing gropus\"\n      // before the matching process, because a `national_prefix_for_parsing`\n      // could exhibit both behaviors.\n\n      var capturedGroupsCount = prefixMatch.length - 1;\n      var hasCapturedGroups = capturedGroupsCount > 0 && prefixMatch[capturedGroupsCount];\n      if (metadata.nationalPrefixTransformRule() && hasCapturedGroups) {\n        nationalNumber = number.replace(prefixPattern, metadata.nationalPrefixTransformRule()); // If there's more than one captured group,\n        // then carrier code is the second one.\n\n        if (capturedGroupsCount > 1) {\n          carrierCode = prefixMatch[1];\n        }\n      } // If there're no \"capturing groups\",\n      // or if there're \"capturing groups\" but no\n      // `national_prefix_transform_rule`,\n      // then just strip the national prefix from the number,\n      // and possibly a carrier code.\n      // Seems like there could be more.\n      else {\n        // `prefixBeforeNationalNumber` is the whole substring matched by\n        // the `national_prefix_for_parsing` regular expression.\n        // There seem to be no guarantees that it's just a national prefix.\n        // For example, if there's a carrier code, it's gonna be a\n        // part of `prefixBeforeNationalNumber` too.\n        var prefixBeforeNationalNumber = prefixMatch[0];\n        nationalNumber = number.slice(prefixBeforeNationalNumber.length); // If there's at least one captured group,\n        // then carrier code is the first one.\n\n        if (hasCapturedGroups) {\n          carrierCode = prefixMatch[1];\n        }\n      } // Tries to guess whether a national prefix was present in the input.\n      // This is not something copy-pasted from Google's library:\n      // they don't seem to have an equivalent for that.\n      // So this isn't an \"officially approved\" way of doing something like that.\n      // But since there seems no other existing method, this library uses it.\n\n      var nationalPrefix;\n      if (hasCapturedGroups) {\n        var possiblePositionOfTheFirstCapturedGroup = number.indexOf(prefixMatch[1]);\n        var possibleNationalPrefix = number.slice(0, possiblePositionOfTheFirstCapturedGroup); // Example: an Argentinian (AR) phone number `0111523456789`.\n        // `prefixMatch[0]` is `01115`, and `$1` is `11`,\n        // and the rest of the phone number is `23456789`.\n        // The national number is transformed via `9$1` to `91123456789`.\n        // National prefix `0` is detected being present at the start.\n        // if (possibleNationalPrefix.indexOf(metadata.numberingPlan.nationalPrefix()) === 0) {\n\n        if (possibleNationalPrefix === metadata.numberingPlan.nationalPrefix()) {\n          nationalPrefix = metadata.numberingPlan.nationalPrefix();\n        }\n      } else {\n        nationalPrefix = prefixMatch[0];\n      }\n      return {\n        nationalNumber: nationalNumber,\n        nationalPrefix: nationalPrefix,\n        carrierCode: carrierCode\n      };\n    }\n  }\n  return {\n    nationalNumber: number\n  };\n}", "map": {"version": 3, "names": ["extractNationalNumberFromPossiblyIncompleteNumber", "number", "metadata", "numberingPlan", "nationalPrefixForParsing", "prefixPattern", "RegExp", "prefixMatch", "exec", "nationalNumber", "carrierCode", "capturedGroupsCount", "length", "hasCapturedGroups", "nationalPrefixTransformRule", "replace", "prefixBeforeNationalNumber", "slice", "nationalPrefix", "possiblePositionOfTheFirstCapturedGroup", "indexOf", "possibleNationalPrefix"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/helpers/extractNationalNumberFromPossiblyIncompleteNumber.js"], "sourcesContent": ["/**\r\n * Strips any national prefix (such as 0, 1) present in a\r\n * (possibly incomplete) number provided.\r\n * \"Carrier codes\" are only used  in Colombia and Brazil,\r\n * and only when dialing within those countries from a mobile phone to a fixed line number.\r\n * Sometimes it won't actually strip national prefix\r\n * and will instead prepend some digits to the `number`:\r\n * for example, when number `2345678` is passed with `VI` country selected,\r\n * it will return `{ number: \"3402345678\" }`, because `340` area code is prepended.\r\n * @param {string} number — National number digits.\r\n * @param {object} metadata — Metadata with country selected.\r\n * @return {object} `{ nationalNumber: string, nationalPrefix: string? carrierCode: string? }`. Even if a national prefix was extracted, it's not necessarily present in the returned object, so don't rely on its presence in the returned object in order to find out whether a national prefix has been extracted or not.\r\n */\nexport default function extractNationalNumberFromPossiblyIncompleteNumber(number, metadata) {\n  if (number && metadata.numberingPlan.nationalPrefixForParsing()) {\n    // See METADATA.md for the description of\n    // `national_prefix_for_parsing` and `national_prefix_transform_rule`.\n    // Attempt to parse the first digits as a national prefix.\n    var prefixPattern = new RegExp('^(?:' + metadata.numberingPlan.nationalPrefixForParsing() + ')');\n    var prefixMatch = prefixPattern.exec(number);\n\n    if (prefixMatch) {\n      var nationalNumber;\n      var carrierCode; // https://gitlab.com/catamphetamine/libphonenumber-js/-/blob/master/METADATA.md#national_prefix_for_parsing--national_prefix_transform_rule\n      // If a `national_prefix_for_parsing` has any \"capturing groups\"\n      // then it means that the national (significant) number is equal to\n      // those \"capturing groups\" transformed via `national_prefix_transform_rule`,\n      // and nothing could be said about the actual national prefix:\n      // what is it and was it even there.\n      // If a `national_prefix_for_parsing` doesn't have any \"capturing groups\",\n      // then everything it matches is a national prefix.\n      // To determine whether `national_prefix_for_parsing` matched any\n      // \"capturing groups\", the value of the result of calling `.exec()`\n      // is looked at, and if it has non-undefined values where there're\n      // \"capturing groups\" in the regular expression, then it means\n      // that \"capturing groups\" have been matched.\n      // It's not possible to tell whether there'll be any \"capturing gropus\"\n      // before the matching process, because a `national_prefix_for_parsing`\n      // could exhibit both behaviors.\n\n      var capturedGroupsCount = prefixMatch.length - 1;\n      var hasCapturedGroups = capturedGroupsCount > 0 && prefixMatch[capturedGroupsCount];\n\n      if (metadata.nationalPrefixTransformRule() && hasCapturedGroups) {\n        nationalNumber = number.replace(prefixPattern, metadata.nationalPrefixTransformRule()); // If there's more than one captured group,\n        // then carrier code is the second one.\n\n        if (capturedGroupsCount > 1) {\n          carrierCode = prefixMatch[1];\n        }\n      } // If there're no \"capturing groups\",\n      // or if there're \"capturing groups\" but no\n      // `national_prefix_transform_rule`,\n      // then just strip the national prefix from the number,\n      // and possibly a carrier code.\n      // Seems like there could be more.\n      else {\n        // `prefixBeforeNationalNumber` is the whole substring matched by\n        // the `national_prefix_for_parsing` regular expression.\n        // There seem to be no guarantees that it's just a national prefix.\n        // For example, if there's a carrier code, it's gonna be a\n        // part of `prefixBeforeNationalNumber` too.\n        var prefixBeforeNationalNumber = prefixMatch[0];\n        nationalNumber = number.slice(prefixBeforeNationalNumber.length); // If there's at least one captured group,\n        // then carrier code is the first one.\n\n        if (hasCapturedGroups) {\n          carrierCode = prefixMatch[1];\n        }\n      } // Tries to guess whether a national prefix was present in the input.\n      // This is not something copy-pasted from Google's library:\n      // they don't seem to have an equivalent for that.\n      // So this isn't an \"officially approved\" way of doing something like that.\n      // But since there seems no other existing method, this library uses it.\n\n\n      var nationalPrefix;\n\n      if (hasCapturedGroups) {\n        var possiblePositionOfTheFirstCapturedGroup = number.indexOf(prefixMatch[1]);\n        var possibleNationalPrefix = number.slice(0, possiblePositionOfTheFirstCapturedGroup); // Example: an Argentinian (AR) phone number `0111523456789`.\n        // `prefixMatch[0]` is `01115`, and `$1` is `11`,\n        // and the rest of the phone number is `23456789`.\n        // The national number is transformed via `9$1` to `91123456789`.\n        // National prefix `0` is detected being present at the start.\n        // if (possibleNationalPrefix.indexOf(metadata.numberingPlan.nationalPrefix()) === 0) {\n\n        if (possibleNationalPrefix === metadata.numberingPlan.nationalPrefix()) {\n          nationalPrefix = metadata.numberingPlan.nationalPrefix();\n        }\n      } else {\n        nationalPrefix = prefixMatch[0];\n      }\n\n      return {\n        nationalNumber: nationalNumber,\n        nationalPrefix: nationalPrefix,\n        carrierCode: carrierCode\n      };\n    }\n  }\n\n  return {\n    nationalNumber: number\n  };\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASA,iDAAiDA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAC1F,IAAID,MAAM,IAAIC,QAAQ,CAACC,aAAa,CAACC,wBAAwB,CAAC,CAAC,EAAE;IAC/D;IACA;IACA;IACA,IAAIC,aAAa,GAAG,IAAIC,MAAM,CAAC,MAAM,GAAGJ,QAAQ,CAACC,aAAa,CAACC,wBAAwB,CAAC,CAAC,GAAG,GAAG,CAAC;IAChG,IAAIG,WAAW,GAAGF,aAAa,CAACG,IAAI,CAACP,MAAM,CAAC;IAE5C,IAAIM,WAAW,EAAE;MACf,IAAIE,cAAc;MAClB,IAAIC,WAAW,CAAC,CAAC;MACjB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA,IAAIC,mBAAmB,GAAGJ,WAAW,CAACK,MAAM,GAAG,CAAC;MAChD,IAAIC,iBAAiB,GAAGF,mBAAmB,GAAG,CAAC,IAAIJ,WAAW,CAACI,mBAAmB,CAAC;MAEnF,IAAIT,QAAQ,CAACY,2BAA2B,CAAC,CAAC,IAAID,iBAAiB,EAAE;QAC/DJ,cAAc,GAAGR,MAAM,CAACc,OAAO,CAACV,aAAa,EAAEH,QAAQ,CAACY,2BAA2B,CAAC,CAAC,CAAC,CAAC,CAAC;QACxF;;QAEA,IAAIH,mBAAmB,GAAG,CAAC,EAAE;UAC3BD,WAAW,GAAGH,WAAW,CAAC,CAAC,CAAC;QAC9B;MACF,CAAC,CAAC;MACF;MACA;MACA;MACA;MACA;MAAA,KACK;QACH;QACA;QACA;QACA;QACA;QACA,IAAIS,0BAA0B,GAAGT,WAAW,CAAC,CAAC,CAAC;QAC/CE,cAAc,GAAGR,MAAM,CAACgB,KAAK,CAACD,0BAA0B,CAACJ,MAAM,CAAC,CAAC,CAAC;QAClE;;QAEA,IAAIC,iBAAiB,EAAE;UACrBH,WAAW,GAAGH,WAAW,CAAC,CAAC,CAAC;QAC9B;MACF,CAAC,CAAC;MACF;MACA;MACA;MACA;;MAGA,IAAIW,cAAc;MAElB,IAAIL,iBAAiB,EAAE;QACrB,IAAIM,uCAAuC,GAAGlB,MAAM,CAACmB,OAAO,CAACb,WAAW,CAAC,CAAC,CAAC,CAAC;QAC5E,IAAIc,sBAAsB,GAAGpB,MAAM,CAACgB,KAAK,CAAC,CAAC,EAAEE,uCAAuC,CAAC,CAAC,CAAC;QACvF;QACA;QACA;QACA;QACA;;QAEA,IAAIE,sBAAsB,KAAKnB,QAAQ,CAACC,aAAa,CAACe,cAAc,CAAC,CAAC,EAAE;UACtEA,cAAc,GAAGhB,QAAQ,CAACC,aAAa,CAACe,cAAc,CAAC,CAAC;QAC1D;MACF,CAAC,MAAM;QACLA,cAAc,GAAGX,WAAW,CAAC,CAAC,CAAC;MACjC;MAEA,OAAO;QACLE,cAAc,EAAEA,cAAc;QAC9BS,cAAc,EAAEA,cAAc;QAC9BR,WAAW,EAAEA;MACf,CAAC;IACH;EACF;EAEA,OAAO;IACLD,cAAc,EAAER;EAClB,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}