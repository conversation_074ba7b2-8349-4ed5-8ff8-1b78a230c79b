{"ast": null, "code": "import { VALID_PUNCTUATION } from '../constants.js'; // Removes brackets and replaces dashes with spaces.\n//\n// E.g. \"(999) 111-22-33\" -> \"999 111 22 33\"\n//\n// For some reason Google's metadata contains `<intlFormat/>`s with brackets and dashes.\n// Meanwhile, there's no single opinion about using punctuation in international phone numbers.\n//\n// For example, Google's `<intlFormat/>` for USA is `******-373-4253`.\n// And here's a quote from WikiPedia's \"North American Numbering Plan\" page:\n// https://en.wikipedia.org/wiki/North_American_Numbering_Plan\n//\n// \"The country calling code for all countries participating in the NANP is 1.\n// In international format, an NANP number should be listed as ****** 555 01 00,\n// where 301 is an area code (Maryland).\"\n//\n// I personally prefer the international format without any punctuation.\n// For example, brackets are remnants of the old age, meaning that the\n// phone number part in brackets (so called \"area code\") can be omitted\n// if dialing within the same \"area\".\n// And hyphens were clearly introduced for splitting local numbers into memorizable groups.\n// For example, remembering \"5553535\" is difficult but \"555-35-35\" is much simpler.\n// Imagine a man taking a bus from home to work and seeing an ad with a phone number.\n// He has a couple of seconds to memorize that number until it passes by.\n// If it were spaces instead of hyphens the man wouldn't necessarily get it,\n// but with hyphens instead of spaces the grouping is more explicit.\n// I personally think that hyphens introduce visual clutter,\n// so I prefer replacing them with spaces in international numbers.\n// In the modern age all output is done on displays where spaces are clearly distinguishable\n// so hyphens can be safely replaced with spaces without losing any legibility.\n//\n\nexport default function applyInternationalSeparatorStyle(formattedNumber) {\n  return formattedNumber.replace(new RegExp(\"[\".concat(VALID_PUNCTUATION, \"]+\"), 'g'), ' ').trim();\n}", "map": {"version": 3, "names": ["VALID_PUNCTUATION", "applyInternationalSeparatorStyle", "formattedNumber", "replace", "RegExp", "concat", "trim"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/helpers/applyInternationalSeparatorStyle.js"], "sourcesContent": ["import { VALID_PUNCTUATION } from '../constants.js'; // Removes brackets and replaces dashes with spaces.\n//\n// E.g. \"(999) 111-22-33\" -> \"999 111 22 33\"\n//\n// For some reason Google's metadata contains `<intlFormat/>`s with brackets and dashes.\n// Meanwhile, there's no single opinion about using punctuation in international phone numbers.\n//\n// For example, Google's `<intlFormat/>` for USA is `******-373-4253`.\n// And here's a quote from WikiPedia's \"North American Numbering Plan\" page:\n// https://en.wikipedia.org/wiki/North_American_Numbering_Plan\n//\n// \"The country calling code for all countries participating in the NANP is 1.\n// In international format, an NANP number should be listed as ****** 555 01 00,\n// where 301 is an area code (Maryland).\"\n//\n// I personally prefer the international format without any punctuation.\n// For example, brackets are remnants of the old age, meaning that the\n// phone number part in brackets (so called \"area code\") can be omitted\n// if dialing within the same \"area\".\n// And hyphens were clearly introduced for splitting local numbers into memorizable groups.\n// For example, remembering \"5553535\" is difficult but \"555-35-35\" is much simpler.\n// Imagine a man taking a bus from home to work and seeing an ad with a phone number.\n// He has a couple of seconds to memorize that number until it passes by.\n// If it were spaces instead of hyphens the man wouldn't necessarily get it,\n// but with hyphens instead of spaces the grouping is more explicit.\n// I personally think that hyphens introduce visual clutter,\n// so I prefer replacing them with spaces in international numbers.\n// In the modern age all output is done on displays where spaces are clearly distinguishable\n// so hyphens can be safely replaced with spaces without losing any legibility.\n//\n\nexport default function applyInternationalSeparatorStyle(formattedNumber) {\n  return formattedNumber.replace(new RegExp(\"[\".concat(VALID_PUNCTUATION, \"]+\"), 'g'), ' ').trim();\n}\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,iBAAiB,CAAC,CAAC;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,gCAAgCA,CAACC,eAAe,EAAE;EACxE,OAAOA,eAAe,CAACC,OAAO,CAAC,IAAIC,MAAM,CAAC,GAAG,CAACC,MAAM,CAACL,iBAAiB,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,CAACM,IAAI,CAAC,CAAC;AAClG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}