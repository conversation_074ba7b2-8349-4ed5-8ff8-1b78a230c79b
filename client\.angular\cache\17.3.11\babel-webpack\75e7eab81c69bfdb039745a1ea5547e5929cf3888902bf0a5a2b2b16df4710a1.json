{"ast": null, "code": "import withMetadataArgument from './withMetadataArgument.js';\nimport { isPossiblePhoneNumber as _isPossiblePhoneNumber } from '../../core/index.js';\nexport function isPossiblePhoneNumber() {\n  return withMetadataArgument(_isPossiblePhoneNumber, arguments);\n}", "map": {"version": 3, "names": ["withMetadataArgument", "isPossiblePhoneNumber", "_isPossiblePhoneNumber", "arguments"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/min/exports/isPossiblePhoneNumber.js"], "sourcesContent": ["import withMetadataArgument from './withMetadataArgument.js'\r\nimport { isPossiblePhoneNumber as _isPossiblePhoneNumber } from '../../core/index.js'\r\n\r\nexport function isPossiblePhoneNumber() {\r\n\treturn withMetadataArgument(_isPossiblePhoneNumber, arguments)\r\n}"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,2BAA2B;AAC5D,SAASC,qBAAqB,IAAIC,sBAAsB,QAAQ,qBAAqB;AAErF,OAAO,SAASD,qBAAqBA,CAAA,EAAG;EACvC,OAAOD,oBAAoB,CAACE,sBAAsB,EAAEC,SAAS,CAAC;AAC/D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}