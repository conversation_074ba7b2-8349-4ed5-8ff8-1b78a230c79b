{"ast": null, "code": "import normalizeArguments from './normalizeArguments.js';\nimport parsePhoneNumber_ from './parsePhoneNumber_.js';\nexport default function parsePhoneNumber() {\n  var _normalizeArguments = normalizeArguments(arguments),\n    text = _normalizeArguments.text,\n    options = _normalizeArguments.options,\n    metadata = _normalizeArguments.metadata;\n  return parsePhoneNumber_(text, options, metadata);\n}", "map": {"version": 3, "names": ["normalizeArguments", "parsePhoneNumber_", "parsePhoneNumber", "_normalizeArguments", "arguments", "text", "options", "metadata"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/parsePhoneNumber.js"], "sourcesContent": ["import normalizeArguments from './normalizeArguments.js';\nimport parsePhoneNumber_ from './parsePhoneNumber_.js';\nexport default function parsePhoneNumber() {\n  var _normalizeArguments = normalizeArguments(arguments),\n      text = _normalizeArguments.text,\n      options = _normalizeArguments.options,\n      metadata = _normalizeArguments.metadata;\n\n  return parsePhoneNumber_(text, options, metadata);\n}\n"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,yBAAyB;AACxD,OAAOC,iBAAiB,MAAM,wBAAwB;AACtD,eAAe,SAASC,gBAAgBA,CAAA,EAAG;EACzC,IAAIC,mBAAmB,GAAGH,kBAAkB,CAACI,SAAS,CAAC;IACnDC,IAAI,GAAGF,mBAAmB,CAACE,IAAI;IAC/BC,OAAO,GAAGH,mBAAmB,CAACG,OAAO;IACrCC,QAAQ,GAAGJ,mBAAmB,CAACI,QAAQ;EAE3C,OAAON,iBAAiB,CAACI,IAAI,EAAEC,OAAO,EAAEC,QAAQ,CAAC;AACnD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}