{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ServiceTicketService {\n  constructor(http) {\n    this.http = http;\n  }\n  getAll(query) {\n    return this.http.get(`${CMS_APIContstant.TICKET}?${query}`);\n  }\n  getTickets(page, pageSize, sortField, sortOrder, filters) {\n    let params = new HttpParams().set('pagination[page]', Math.max(page, 1).toString()).set('pagination[pageSize]', Math.max(pageSize, 1).toString());\n    // Add sorting\n    if (sortField && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc';\n      params = params.set('sort', `${sortField}:${order}`);\n    }\n    // Add filters if provided\n    if (filters) {\n      // Convert filters object to query parameters\n      Object.keys(filters).forEach(key => {\n        if (filters[key] !== undefined && filters[key] !== null) {\n          params = params.set(key, filters[key]);\n        }\n      });\n    }\n    return this.http.get(CMS_APIContstant.TICKET, {\n      params\n    });\n  }\n  getById(id) {\n    return this.http.get(`${CMS_APIContstant.TICKET}?filters[id]=${id}`);\n  }\n  getByAccountId(id) {\n    return this.http.get(`${CMS_APIContstant.TICKET}?filters[account_id]=${id}`);\n  }\n  createTicket(data) {\n    return this.http.post(`${CMS_APIContstant.TICKET}`, data);\n  }\n  updateTicket(id, data) {\n    return this.http.put(`${CMS_APIContstant.TICKET}/${id}`, data);\n  }\n  getAllTicketStatus() {\n    return this.http.get(CMS_APIContstant.CONFIG_DATA + '?filters[type][$eq]=TICKET_STATUS');\n  }\n  static {\n    this.ɵfac = function ServiceTicketService_Factory(t) {\n      return new (t || ServiceTicketService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ServiceTicketService,\n      factory: ServiceTicketService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "CMS_APIContstant", "ServiceTicketService", "constructor", "http", "getAll", "query", "get", "TICKET", "getTickets", "page", "pageSize", "sortField", "sortOrder", "filters", "params", "set", "Math", "max", "toString", "undefined", "order", "Object", "keys", "for<PERSON>ach", "key", "getById", "id", "getByAccountId", "createTicket", "data", "post", "updateTicket", "put", "getAllTicketStatus", "CONFIG_DATA", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\services\\service-ticket.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\r\nimport { Observable } from 'rxjs';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class ServiceTicketService {\r\n\r\n  constructor(private http: HttpClient) { }\r\n\r\n  getAll(query: string) {\r\n    return this.http.get<any>(`${CMS_APIContstant.TICKET}?${query}`);\r\n  }\r\n\r\n  getTickets(\r\n    page: number,\r\n    pageSize: number,\r\n    sortField?: string,\r\n    sortOrder?: number,\r\n    filters?: any\r\n  ): Observable<any> {\r\n    let params = new HttpParams()\r\n      .set('pagination[page]', Math.max(page, 1).toString())\r\n      .set('pagination[pageSize]', Math.max(pageSize, 1).toString());\r\n\r\n    // Add sorting\r\n    if (sortField && sortOrder !== undefined) {\r\n      const order = sortOrder === 1 ? 'asc' : 'desc';\r\n      params = params.set('sort', `${sortField}:${order}`);\r\n    }\r\n\r\n    // Add filters if provided\r\n    if (filters) {\r\n      // Convert filters object to query parameters\r\n      Object.keys(filters).forEach(key => {\r\n        if (filters[key] !== undefined && filters[key] !== null) {\r\n          params = params.set(key, filters[key]);\r\n        }\r\n      });\r\n    }\r\n\r\n    return this.http.get<any>(CMS_APIContstant.TICKET, { params });\r\n  }\r\n\r\n  getById(id: string) {\r\n    return this.http.get<any>(`${CMS_APIContstant.TICKET}?filters[id]=${id}`);\r\n  }\r\n\r\n  getByAccountId(id: string) {\r\n    return this.http.get<any>(`${CMS_APIContstant.TICKET}?filters[account_id]=${id}`);\r\n  }\r\n\r\n  createTicket(data: any) {\r\n    return this.http.post<any>(`${CMS_APIContstant.TICKET}`, data);\r\n  }\r\n\r\n  updateTicket(id: string, data: any) {\r\n    return this.http.put<any>(`${CMS_APIContstant.TICKET}/${id}`, data);\r\n  }\r\n\r\n  getAllTicketStatus() {\r\n    return this.http.get<any>(CMS_APIContstant.CONFIG_DATA + '?filters[type][$eq]=TICKET_STATUS');\r\n  }\r\n}\r\n"], "mappings": "AACA,SAAqBA,UAAU,QAAQ,sBAAsB;AAC7D,SAASC,gBAAgB,QAAQ,iCAAiC;;;AAMlE,OAAM,MAAOC,oBAAoB;EAE/BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;EAAgB;EAExCC,MAAMA,CAACC,KAAa;IAClB,OAAO,IAAI,CAACF,IAAI,CAACG,GAAG,CAAM,GAAGN,gBAAgB,CAACO,MAAM,IAAIF,KAAK,EAAE,CAAC;EAClE;EAEAG,UAAUA,CACRC,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBC,OAAa;IAEb,IAAIC,MAAM,GAAG,IAAIf,UAAU,EAAE,CAC1BgB,GAAG,CAAC,kBAAkB,EAAEC,IAAI,CAACC,GAAG,CAACR,IAAI,EAAE,CAAC,CAAC,CAACS,QAAQ,EAAE,CAAC,CACrDH,GAAG,CAAC,sBAAsB,EAAEC,IAAI,CAACC,GAAG,CAACP,QAAQ,EAAE,CAAC,CAAC,CAACQ,QAAQ,EAAE,CAAC;IAEhE;IACA,IAAIP,SAAS,IAAIC,SAAS,KAAKO,SAAS,EAAE;MACxC,MAAMC,KAAK,GAAGR,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM;MAC9CE,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,GAAGJ,SAAS,IAAIS,KAAK,EAAE,CAAC;IACtD;IAEA;IACA,IAAIP,OAAO,EAAE;MACX;MACAQ,MAAM,CAACC,IAAI,CAACT,OAAO,CAAC,CAACU,OAAO,CAACC,GAAG,IAAG;QACjC,IAAIX,OAAO,CAACW,GAAG,CAAC,KAAKL,SAAS,IAAIN,OAAO,CAACW,GAAG,CAAC,KAAK,IAAI,EAAE;UACvDV,MAAM,GAAGA,MAAM,CAACC,GAAG,CAACS,GAAG,EAAEX,OAAO,CAACW,GAAG,CAAC,CAAC;QACxC;MACF,CAAC,CAAC;IACJ;IAEA,OAAO,IAAI,CAACrB,IAAI,CAACG,GAAG,CAAMN,gBAAgB,CAACO,MAAM,EAAE;MAAEO;IAAM,CAAE,CAAC;EAChE;EAEAW,OAAOA,CAACC,EAAU;IAChB,OAAO,IAAI,CAACvB,IAAI,CAACG,GAAG,CAAM,GAAGN,gBAAgB,CAACO,MAAM,gBAAgBmB,EAAE,EAAE,CAAC;EAC3E;EAEAC,cAAcA,CAACD,EAAU;IACvB,OAAO,IAAI,CAACvB,IAAI,CAACG,GAAG,CAAM,GAAGN,gBAAgB,CAACO,MAAM,wBAAwBmB,EAAE,EAAE,CAAC;EACnF;EAEAE,YAAYA,CAACC,IAAS;IACpB,OAAO,IAAI,CAAC1B,IAAI,CAAC2B,IAAI,CAAM,GAAG9B,gBAAgB,CAACO,MAAM,EAAE,EAAEsB,IAAI,CAAC;EAChE;EAEAE,YAAYA,CAACL,EAAU,EAAEG,IAAS;IAChC,OAAO,IAAI,CAAC1B,IAAI,CAAC6B,GAAG,CAAM,GAAGhC,gBAAgB,CAACO,MAAM,IAAImB,EAAE,EAAE,EAAEG,IAAI,CAAC;EACrE;EAEAI,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAAC9B,IAAI,CAACG,GAAG,CAAMN,gBAAgB,CAACkC,WAAW,GAAG,mCAAmC,CAAC;EAC/F;;;uBAxDWjC,oBAAoB,EAAAkC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAApBrC,oBAAoB;MAAAsC,OAAA,EAApBtC,oBAAoB,CAAAuC,IAAA;MAAAC,UAAA,EAFnB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}