{"ast": null, "code": "import { normalizeArguments } from './getNumberType.js';\nimport _isPossibleNumber from '../isPossible.js';\n/**\r\n * Checks if a given phone number is possible.\r\n * Which means it only checks phone number length\r\n * and doesn't test any regular expressions.\r\n *\r\n * Examples:\r\n *\r\n * ```js\r\n * isPossibleNumber('+78005553535', metadata)\r\n * isPossibleNumber('8005553535', 'RU', metadata)\r\n * isPossibleNumber('88005553535', 'RU', metadata)\r\n * isPossibleNumber({ phone: '8005553535', country: 'RU' }, metadata)\r\n * ```\r\n */\n\nexport default function isPossibleNumber() {\n  var _normalizeArguments = normalizeArguments(arguments),\n    input = _normalizeArguments.input,\n    options = _normalizeArguments.options,\n    metadata = _normalizeArguments.metadata; // `parseNumber()` would return `{}` when no phone number could be parsed from the input.\n\n  if (!input.phone && !(options && options.v2)) {\n    return false;\n  }\n  return _isPossibleNumber(input, options, metadata);\n}", "map": {"version": 3, "names": ["normalizeArguments", "_isPossibleNumber", "isPossibleNumber", "_normalizeArguments", "arguments", "input", "options", "metadata", "phone", "v2"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/legacy/isPossibleNumber.js"], "sourcesContent": ["import { normalizeArguments } from './getNumberType.js';\nimport _isPossibleNumber from '../isPossible.js';\n/**\r\n * Checks if a given phone number is possible.\r\n * Which means it only checks phone number length\r\n * and doesn't test any regular expressions.\r\n *\r\n * Examples:\r\n *\r\n * ```js\r\n * isPossibleNumber('+78005553535', metadata)\r\n * isPossibleNumber('8005553535', 'RU', metadata)\r\n * isPossibleNumber('88005553535', 'RU', metadata)\r\n * isPossibleNumber({ phone: '8005553535', country: 'RU' }, metadata)\r\n * ```\r\n */\n\nexport default function isPossibleNumber() {\n  var _normalizeArguments = normalizeArguments(arguments),\n      input = _normalizeArguments.input,\n      options = _normalizeArguments.options,\n      metadata = _normalizeArguments.metadata; // `parseNumber()` would return `{}` when no phone number could be parsed from the input.\n\n\n  if (!input.phone && !(options && options.v2)) {\n    return false;\n  }\n\n  return _isPossibleNumber(input, options, metadata);\n}\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,oBAAoB;AACvD,OAAOC,iBAAiB,MAAM,kBAAkB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,gBAAgBA,CAAA,EAAG;EACzC,IAAIC,mBAAmB,GAAGH,kBAAkB,CAACI,SAAS,CAAC;IACnDC,KAAK,GAAGF,mBAAmB,CAACE,KAAK;IACjCC,OAAO,GAAGH,mBAAmB,CAACG,OAAO;IACrCC,QAAQ,GAAGJ,mBAAmB,CAACI,QAAQ,CAAC,CAAC;;EAG7C,IAAI,CAACF,KAAK,CAACG,KAAK,IAAI,EAAEF,OAAO,IAAIA,OAAO,CAACG,EAAE,CAAC,EAAE;IAC5C,OAAO,KAAK;EACd;EAEA,OAAOR,iBAAiB,CAACI,KAAK,EAAEC,OAAO,EAAEC,QAAQ,CAAC;AACpD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}