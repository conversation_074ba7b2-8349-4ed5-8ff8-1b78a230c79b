{"ast": null, "code": "// The minimum length of the national significant number.\nexport var MIN_LENGTH_FOR_NSN = 2; // The ITU says the maximum length should be 15,\n// but one can find longer numbers in Germany.\n\nexport var MAX_LENGTH_FOR_NSN = 17; // The maximum length of the country calling code.\n\nexport var MAX_LENGTH_COUNTRY_CODE = 3; // Digits accepted in phone numbers\n// (ascii, fullwidth, arabic-indic, and eastern arabic digits).\n\nexport var VALID_DIGITS = \"0-9\\uFF10-\\uFF19\\u0660-\\u0669\\u06F0-\\u06F9\"; // `DASHES` will be right after the opening square bracket of the \"character class\"\n\nvar DASHES = \"-\\u2010-\\u2015\\u2212\\u30FC\\uFF0D\";\nvar SLASHES = \"\\uFF0F/\";\nvar DOTS = \"\\uFF0E.\";\nexport var WHITESPACE = \" \\xA0\\xAD\\u200B\\u2060\\u3000\";\nvar BRACKETS = \"()\\uFF08\\uFF09\\uFF3B\\uFF3D\\\\[\\\\]\"; // export const OPENING_BRACKETS = '(\\uFF08\\uFF3B\\\\\\['\n\nvar TILDES = \"~\\u2053\\u223C\\uFF5E\"; // Regular expression of acceptable punctuation found in phone numbers. This\n// excludes punctuation found as a leading character only. This consists of dash\n// characters, white space characters, full stops, slashes, square brackets,\n// parentheses and tildes. Full-width variants are also present.\n\nexport var VALID_PUNCTUATION = \"\".concat(DASHES).concat(SLASHES).concat(DOTS).concat(WHITESPACE).concat(BRACKETS).concat(TILDES);\nexport var PLUS_CHARS = \"+\\uFF0B\"; // const LEADING_PLUS_CHARS_PATTERN = new RegExp('^[' + PLUS_CHARS + ']+')", "map": {"version": 3, "names": ["MIN_LENGTH_FOR_NSN", "MAX_LENGTH_FOR_NSN", "MAX_LENGTH_COUNTRY_CODE", "VALID_DIGITS", "DASHES", "SLASHES", "DOTS", "WHITESPACE", "BRACKETS", "TILDES", "VALID_PUNCTUATION", "concat", "PLUS_CHARS"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/constants.js"], "sourcesContent": ["// The minimum length of the national significant number.\nexport var MIN_LENGTH_FOR_NSN = 2; // The ITU says the maximum length should be 15,\n// but one can find longer numbers in Germany.\n\nexport var MAX_LENGTH_FOR_NSN = 17; // The maximum length of the country calling code.\n\nexport var MAX_LENGTH_COUNTRY_CODE = 3; // Digits accepted in phone numbers\n// (ascii, fullwidth, arabic-indic, and eastern arabic digits).\n\nexport var VALID_DIGITS = \"0-9\\uFF10-\\uFF19\\u0660-\\u0669\\u06F0-\\u06F9\"; // `DASHES` will be right after the opening square bracket of the \"character class\"\n\nvar DASHES = \"-\\u2010-\\u2015\\u2212\\u30FC\\uFF0D\";\nvar SLASHES = \"\\uFF0F/\";\nvar DOTS = \"\\uFF0E.\";\nexport var WHITESPACE = \" \\xA0\\xAD\\u200B\\u2060\\u3000\";\nvar BRACKETS = \"()\\uFF08\\uFF09\\uFF3B\\uFF3D\\\\[\\\\]\"; // export const OPENING_BRACKETS = '(\\uFF08\\uFF3B\\\\\\['\n\nvar TILDES = \"~\\u2053\\u223C\\uFF5E\"; // Regular expression of acceptable punctuation found in phone numbers. This\n// excludes punctuation found as a leading character only. This consists of dash\n// characters, white space characters, full stops, slashes, square brackets,\n// parentheses and tildes. Full-width variants are also present.\n\nexport var VALID_PUNCTUATION = \"\".concat(DASHES).concat(SLASHES).concat(DOTS).concat(WHITESPACE).concat(BRACKETS).concat(TILDES);\nexport var PLUS_CHARS = \"+\\uFF0B\"; // const LEADING_PLUS_CHARS_PATTERN = new RegExp('^[' + PLUS_CHARS + ']+')\n"], "mappings": "AAAA;AACA,OAAO,IAAIA,kBAAkB,GAAG,CAAC,CAAC,CAAC;AACnC;;AAEA,OAAO,IAAIC,kBAAkB,GAAG,EAAE,CAAC,CAAC;;AAEpC,OAAO,IAAIC,uBAAuB,GAAG,CAAC,CAAC,CAAC;AACxC;;AAEA,OAAO,IAAIC,YAAY,GAAG,4CAA4C,CAAC,CAAC;;AAExE,IAAIC,MAAM,GAAG,kCAAkC;AAC/C,IAAIC,OAAO,GAAG,SAAS;AACvB,IAAIC,IAAI,GAAG,SAAS;AACpB,OAAO,IAAIC,UAAU,GAAG,6BAA6B;AACrD,IAAIC,QAAQ,GAAG,kCAAkC,CAAC,CAAC;;AAEnD,IAAIC,MAAM,GAAG,qBAAqB,CAAC,CAAC;AACpC;AACA;AACA;;AAEA,OAAO,IAAIC,iBAAiB,GAAG,EAAE,CAACC,MAAM,CAACP,MAAM,CAAC,CAACO,MAAM,CAACN,OAAO,CAAC,CAACM,MAAM,CAACL,IAAI,CAAC,CAACK,MAAM,CAACJ,UAAU,CAAC,CAACI,MAAM,CAACH,QAAQ,CAAC,CAACG,MAAM,CAACF,MAAM,CAAC;AAChI,OAAO,IAAIG,UAAU,GAAG,SAAS,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}