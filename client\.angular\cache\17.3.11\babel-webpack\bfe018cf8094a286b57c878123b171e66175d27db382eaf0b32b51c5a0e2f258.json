{"ast": null, "code": "import _parseNumber from '../parse.js';\nimport normalizeArguments from '../normalizeArguments.js';\nexport default function parseNumber() {\n  var _normalizeArguments = normalizeArguments(arguments),\n    text = _normalizeArguments.text,\n    options = _normalizeArguments.options,\n    metadata = _normalizeArguments.metadata;\n  return _parseNumber(text, options, metadata);\n}", "map": {"version": 3, "names": ["_parseNumber", "normalizeArguments", "parseNumber", "_normalizeArguments", "arguments", "text", "options", "metadata"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/legacy/parse.js"], "sourcesContent": ["import _parseNumber from '../parse.js';\nimport normalizeArguments from '../normalizeArguments.js';\nexport default function parseNumber() {\n  var _normalizeArguments = normalizeArguments(arguments),\n      text = _normalizeArguments.text,\n      options = _normalizeArguments.options,\n      metadata = _normalizeArguments.metadata;\n\n  return _parseNumber(text, options, metadata);\n}\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,aAAa;AACtC,OAAOC,kBAAkB,MAAM,0BAA0B;AACzD,eAAe,SAASC,WAAWA,CAAA,EAAG;EACpC,IAAIC,mBAAmB,GAAGF,kBAAkB,CAACG,SAAS,CAAC;IACnDC,IAAI,GAAGF,mBAAmB,CAACE,IAAI;IAC/BC,OAAO,GAAGH,mBAAmB,CAACG,OAAO;IACrCC,QAAQ,GAAGJ,mBAAmB,CAACI,QAAQ;EAE3C,OAAOP,YAAY,CAACK,IAAI,EAAEC,OAAO,EAAEC,QAAQ,CAAC;AAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}