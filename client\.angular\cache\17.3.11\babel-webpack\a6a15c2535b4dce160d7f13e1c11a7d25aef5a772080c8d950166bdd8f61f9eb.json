{"ast": null, "code": "import mergeArrays from './mergeArrays.js';\nexport default function checkNumberLength(nationalNumber, metadata) {\n  return checkNumberLengthForType(nationalNumber, undefined, metadata);\n} // Checks whether a number is possible for the country based on its length.\n// Should only be called for the \"new\" metadata which has \"possible lengths\".\n\nexport function checkNumberLengthForType(nationalNumber, type, metadata) {\n  var type_info = metadata.type(type); // There should always be \"<possiblePengths/>\" set for every type element.\n  // This is declared in the XML schema.\n  // For size efficiency, where a sub-description (e.g. fixed-line)\n  // has the same \"<possiblePengths/>\" as the \"general description\", this is missing,\n  // so we fall back to the \"general description\". Where no numbers of the type\n  // exist at all, there is one possible length (-1) which is guaranteed\n  // not to match the length of any real phone number.\n\n  var possible_lengths = type_info && type_info.possibleLengths() || metadata.possibleLengths(); // let local_lengths    = type_info && type.possibleLengthsLocal() || metadata.possibleLengthsLocal()\n  // Metadata before version `1.0.18` didn't contain `possible_lengths`.\n\n  if (!possible_lengths) {\n    return 'IS_POSSIBLE';\n  }\n  if (type === 'FIXED_LINE_OR_MOBILE') {\n    // No such country in metadata.\n\n    /* istanbul ignore next */\n    if (!metadata.type('FIXED_LINE')) {\n      // The rare case has been encountered where no fixedLine data is available\n      // (true for some non-geographic entities), so we just check mobile.\n      return checkNumberLengthForType(nationalNumber, 'MOBILE', metadata);\n    }\n    var mobile_type = metadata.type('MOBILE');\n    if (mobile_type) {\n      // Merge the mobile data in if there was any. \"Concat\" creates a new\n      // array, it doesn't edit possible_lengths in place, so we don't need a copy.\n      // Note that when adding the possible lengths from mobile, we have\n      // to again check they aren't empty since if they are this indicates\n      // they are the same as the general desc and should be obtained from there.\n      possible_lengths = mergeArrays(possible_lengths, mobile_type.possibleLengths()); // The current list is sorted; we need to merge in the new list and\n      // re-sort (duplicates are okay). Sorting isn't so expensive because\n      // the lists are very small.\n      // if (local_lengths) {\n      // \tlocal_lengths = mergeArrays(local_lengths, mobile_type.possibleLengthsLocal())\n      // } else {\n      // \tlocal_lengths = mobile_type.possibleLengthsLocal()\n      // }\n    }\n  } // If the type doesn't exist then return 'INVALID_LENGTH'.\n  else if (type && !type_info) {\n    return 'INVALID_LENGTH';\n  }\n  var actual_length = nationalNumber.length; // In `libphonenumber-js` all \"local-only\" formats are dropped for simplicity.\n  // // This is safe because there is never an overlap beween the possible lengths\n  // // and the local-only lengths; this is checked at build time.\n  // if (local_lengths && local_lengths.indexOf(nationalNumber.length) >= 0)\n  // {\n  // \treturn 'IS_POSSIBLE_LOCAL_ONLY'\n  // }\n\n  var minimum_length = possible_lengths[0];\n  if (minimum_length === actual_length) {\n    return 'IS_POSSIBLE';\n  }\n  if (minimum_length > actual_length) {\n    return 'TOO_SHORT';\n  }\n  if (possible_lengths[possible_lengths.length - 1] < actual_length) {\n    return 'TOO_LONG';\n  } // We skip the first element since we've already checked it.\n\n  return possible_lengths.indexOf(actual_length, 1) >= 0 ? 'IS_POSSIBLE' : 'INVALID_LENGTH';\n}", "map": {"version": 3, "names": ["mergeArrays", "checkNumberLength", "nationalNumber", "metadata", "checkNumberLengthForType", "undefined", "type", "type_info", "possible_lengths", "possibleLengths", "mobile_type", "actual_length", "length", "minimum_length", "indexOf"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/helpers/checkNumberLength.js"], "sourcesContent": ["import mergeArrays from './mergeArrays.js';\nexport default function checkNumberLength(nationalNumber, metadata) {\n  return checkNumberLengthForType(nationalNumber, undefined, metadata);\n} // Checks whether a number is possible for the country based on its length.\n// Should only be called for the \"new\" metadata which has \"possible lengths\".\n\nexport function checkNumberLengthForType(nationalNumber, type, metadata) {\n  var type_info = metadata.type(type); // There should always be \"<possiblePengths/>\" set for every type element.\n  // This is declared in the XML schema.\n  // For size efficiency, where a sub-description (e.g. fixed-line)\n  // has the same \"<possiblePengths/>\" as the \"general description\", this is missing,\n  // so we fall back to the \"general description\". Where no numbers of the type\n  // exist at all, there is one possible length (-1) which is guaranteed\n  // not to match the length of any real phone number.\n\n  var possible_lengths = type_info && type_info.possibleLengths() || metadata.possibleLengths(); // let local_lengths    = type_info && type.possibleLengthsLocal() || metadata.possibleLengthsLocal()\n  // Metadata before version `1.0.18` didn't contain `possible_lengths`.\n\n  if (!possible_lengths) {\n    return 'IS_POSSIBLE';\n  }\n\n  if (type === 'FIXED_LINE_OR_MOBILE') {\n    // No such country in metadata.\n\n    /* istanbul ignore next */\n    if (!metadata.type('FIXED_LINE')) {\n      // The rare case has been encountered where no fixedLine data is available\n      // (true for some non-geographic entities), so we just check mobile.\n      return checkNumberLengthForType(nationalNumber, 'MOBILE', metadata);\n    }\n\n    var mobile_type = metadata.type('MOBILE');\n\n    if (mobile_type) {\n      // Merge the mobile data in if there was any. \"Concat\" creates a new\n      // array, it doesn't edit possible_lengths in place, so we don't need a copy.\n      // Note that when adding the possible lengths from mobile, we have\n      // to again check they aren't empty since if they are this indicates\n      // they are the same as the general desc and should be obtained from there.\n      possible_lengths = mergeArrays(possible_lengths, mobile_type.possibleLengths()); // The current list is sorted; we need to merge in the new list and\n      // re-sort (duplicates are okay). Sorting isn't so expensive because\n      // the lists are very small.\n      // if (local_lengths) {\n      // \tlocal_lengths = mergeArrays(local_lengths, mobile_type.possibleLengthsLocal())\n      // } else {\n      // \tlocal_lengths = mobile_type.possibleLengthsLocal()\n      // }\n    }\n  } // If the type doesn't exist then return 'INVALID_LENGTH'.\n  else if (type && !type_info) {\n    return 'INVALID_LENGTH';\n  }\n\n  var actual_length = nationalNumber.length; // In `libphonenumber-js` all \"local-only\" formats are dropped for simplicity.\n  // // This is safe because there is never an overlap beween the possible lengths\n  // // and the local-only lengths; this is checked at build time.\n  // if (local_lengths && local_lengths.indexOf(nationalNumber.length) >= 0)\n  // {\n  // \treturn 'IS_POSSIBLE_LOCAL_ONLY'\n  // }\n\n  var minimum_length = possible_lengths[0];\n\n  if (minimum_length === actual_length) {\n    return 'IS_POSSIBLE';\n  }\n\n  if (minimum_length > actual_length) {\n    return 'TOO_SHORT';\n  }\n\n  if (possible_lengths[possible_lengths.length - 1] < actual_length) {\n    return 'TOO_LONG';\n  } // We skip the first element since we've already checked it.\n\n\n  return possible_lengths.indexOf(actual_length, 1) >= 0 ? 'IS_POSSIBLE' : 'INVALID_LENGTH';\n}\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,kBAAkB;AAC1C,eAAe,SAASC,iBAAiBA,CAACC,cAAc,EAAEC,QAAQ,EAAE;EAClE,OAAOC,wBAAwB,CAACF,cAAc,EAAEG,SAAS,EAAEF,QAAQ,CAAC;AACtE,CAAC,CAAC;AACF;;AAEA,OAAO,SAASC,wBAAwBA,CAACF,cAAc,EAAEI,IAAI,EAAEH,QAAQ,EAAE;EACvE,IAAII,SAAS,GAAGJ,QAAQ,CAACG,IAAI,CAACA,IAAI,CAAC,CAAC,CAAC;EACrC;EACA;EACA;EACA;EACA;EACA;;EAEA,IAAIE,gBAAgB,GAAGD,SAAS,IAAIA,SAAS,CAACE,eAAe,CAAC,CAAC,IAAIN,QAAQ,CAACM,eAAe,CAAC,CAAC,CAAC,CAAC;EAC/F;;EAEA,IAAI,CAACD,gBAAgB,EAAE;IACrB,OAAO,aAAa;EACtB;EAEA,IAAIF,IAAI,KAAK,sBAAsB,EAAE;IACnC;;IAEA;IACA,IAAI,CAACH,QAAQ,CAACG,IAAI,CAAC,YAAY,CAAC,EAAE;MAChC;MACA;MACA,OAAOF,wBAAwB,CAACF,cAAc,EAAE,QAAQ,EAAEC,QAAQ,CAAC;IACrE;IAEA,IAAIO,WAAW,GAAGP,QAAQ,CAACG,IAAI,CAAC,QAAQ,CAAC;IAEzC,IAAII,WAAW,EAAE;MACf;MACA;MACA;MACA;MACA;MACAF,gBAAgB,GAAGR,WAAW,CAACQ,gBAAgB,EAAEE,WAAW,CAACD,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;MACjF;MACA;MACA;MACA;MACA;MACA;MACA;IACF;EACF,CAAC,CAAC;EAAA,KACG,IAAIH,IAAI,IAAI,CAACC,SAAS,EAAE;IAC3B,OAAO,gBAAgB;EACzB;EAEA,IAAII,aAAa,GAAGT,cAAc,CAACU,MAAM,CAAC,CAAC;EAC3C;EACA;EACA;EACA;EACA;EACA;;EAEA,IAAIC,cAAc,GAAGL,gBAAgB,CAAC,CAAC,CAAC;EAExC,IAAIK,cAAc,KAAKF,aAAa,EAAE;IACpC,OAAO,aAAa;EACtB;EAEA,IAAIE,cAAc,GAAGF,aAAa,EAAE;IAClC,OAAO,WAAW;EACpB;EAEA,IAAIH,gBAAgB,CAACA,gBAAgB,CAACI,MAAM,GAAG,CAAC,CAAC,GAAGD,aAAa,EAAE;IACjE,OAAO,UAAU;EACnB,CAAC,CAAC;;EAGF,OAAOH,gBAAgB,CAACM,OAAO,CAACH,aAAa,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,aAAa,GAAG,gBAAgB;AAC3F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}