{"ast": null, "code": "import isValidNumber from '../isValid.js';\n/**\r\n * Checks if a given phone number is valid within a given region.\r\n * Is just an alias for `phoneNumber.isValid() && phoneNumber.country === country`.\r\n * https://github.com/googlei18n/libphonenumber/blob/master/FAQ.md#when-should-i-use-isvalidnumberforregion\r\n */\n\nexport default function isValidNumberForRegion(input, country, options, metadata) {\n  // If assigning the `{}` default value is moved to the arguments above,\n  // code coverage would decrease for some weird reason.\n  options = options || {};\n  return input.country === country && isValidNumber(input, options, metadata);\n}", "map": {"version": 3, "names": ["isValidNumber", "isValidNumberForRegion", "input", "country", "options", "metadata"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/legacy/isValidNumberForRegion_.js"], "sourcesContent": ["import isValidNumber from '../isValid.js';\n/**\r\n * Checks if a given phone number is valid within a given region.\r\n * Is just an alias for `phoneNumber.isValid() && phoneNumber.country === country`.\r\n * https://github.com/googlei18n/libphonenumber/blob/master/FAQ.md#when-should-i-use-isvalidnumberforregion\r\n */\n\nexport default function isValidNumberForRegion(input, country, options, metadata) {\n  // If assigning the `{}` default value is moved to the arguments above,\n  // code coverage would decrease for some weird reason.\n  options = options || {};\n  return input.country === country && isValidNumber(input, options, metadata);\n}\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,eAAe;AACzC;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,sBAAsBA,CAACC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAEC,QAAQ,EAAE;EAChF;EACA;EACAD,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvB,OAAOF,KAAK,CAACC,OAAO,KAAKA,OAAO,IAAIH,aAAa,CAACE,KAAK,EAAEE,OAAO,EAAEC,QAAQ,CAAC;AAC7E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}