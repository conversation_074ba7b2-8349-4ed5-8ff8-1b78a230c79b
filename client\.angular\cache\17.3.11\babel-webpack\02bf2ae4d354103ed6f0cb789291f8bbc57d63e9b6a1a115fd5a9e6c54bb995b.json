{"ast": null, "code": "import applyInternationalSeparatorStyle from './applyInternationalSeparatorStyle.js'; // This was originally set to $1 but there are some countries for which the\n// first group is not used in the national pattern (e.g. Argentina) so the $1\n// group does not match correctly. Therefore, we use `\\d`, so that the first\n// group actually used in the pattern will be matched.\n\nexport var FIRST_GROUP_PATTERN = /(\\$\\d)/;\nexport default function formatNationalNumberUsingFormat(number, format, _ref) {\n  var useInternationalFormat = _ref.useInternationalFormat,\n    withNationalPrefix = _ref.withNationalPrefix,\n    carrierCode = _ref.carrierCode,\n    metadata = _ref.metadata;\n  var formattedNumber = number.replace(new RegExp(format.pattern()), useInternationalFormat ? format.internationalFormat() :\n  // This library doesn't use `domestic_carrier_code_formatting_rule`,\n  // because that one is only used when formatting phone numbers\n  // for dialing from a mobile phone, and this is not a dialing library.\n  // carrierCode && format.domesticCarrierCodeFormattingRule()\n  // \t// First, replace the $CC in the formatting rule with the desired carrier code.\n  // \t// Then, replace the $FG in the formatting rule with the first group\n  // \t// and the carrier code combined in the appropriate way.\n  // \t? format.format().replace(FIRST_GROUP_PATTERN, format.domesticCarrierCodeFormattingRule().replace('$CC', carrierCode))\n  // \t: (\n  // \t\twithNationalPrefix && format.nationalPrefixFormattingRule()\n  // \t\t\t? format.format().replace(FIRST_GROUP_PATTERN, format.nationalPrefixFormattingRule())\n  // \t\t\t: format.format()\n  // \t)\n  withNationalPrefix && format.nationalPrefixFormattingRule() ? format.format().replace(FIRST_GROUP_PATTERN, format.nationalPrefixFormattingRule()) : format.format());\n  if (useInternationalFormat) {\n    return applyInternationalSeparatorStyle(formattedNumber);\n  }\n  return formattedNumber;\n}", "map": {"version": 3, "names": ["applyInternationalSeparatorStyle", "FIRST_GROUP_PATTERN", "formatNationalNumberUsingFormat", "number", "format", "_ref", "useInternationalFormat", "withNationalPrefix", "carrierCode", "metadata", "formattedNumber", "replace", "RegExp", "pattern", "internationalFormat", "nationalPrefixFormattingRule"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/helpers/formatNationalNumberUsingFormat.js"], "sourcesContent": ["import applyInternationalSeparatorStyle from './applyInternationalSeparatorStyle.js'; // This was originally set to $1 but there are some countries for which the\n// first group is not used in the national pattern (e.g. Argentina) so the $1\n// group does not match correctly. Therefore, we use `\\d`, so that the first\n// group actually used in the pattern will be matched.\n\nexport var FIRST_GROUP_PATTERN = /(\\$\\d)/;\nexport default function formatNationalNumberUsingFormat(number, format, _ref) {\n  var useInternationalFormat = _ref.useInternationalFormat,\n      withNationalPrefix = _ref.withNationalPrefix,\n      carrierCode = _ref.carrierCode,\n      metadata = _ref.metadata;\n  var formattedNumber = number.replace(new RegExp(format.pattern()), useInternationalFormat ? format.internationalFormat() : // This library doesn't use `domestic_carrier_code_formatting_rule`,\n  // because that one is only used when formatting phone numbers\n  // for dialing from a mobile phone, and this is not a dialing library.\n  // carrierCode && format.domesticCarrierCodeFormattingRule()\n  // \t// First, replace the $CC in the formatting rule with the desired carrier code.\n  // \t// Then, replace the $FG in the formatting rule with the first group\n  // \t// and the carrier code combined in the appropriate way.\n  // \t? format.format().replace(FIRST_GROUP_PATTERN, format.domesticCarrierCodeFormattingRule().replace('$CC', carrierCode))\n  // \t: (\n  // \t\twithNationalPrefix && format.nationalPrefixFormattingRule()\n  // \t\t\t? format.format().replace(FIRST_GROUP_PATTERN, format.nationalPrefixFormattingRule())\n  // \t\t\t: format.format()\n  // \t)\n  withNationalPrefix && format.nationalPrefixFormattingRule() ? format.format().replace(FIRST_GROUP_PATTERN, format.nationalPrefixFormattingRule()) : format.format());\n\n  if (useInternationalFormat) {\n    return applyInternationalSeparatorStyle(formattedNumber);\n  }\n\n  return formattedNumber;\n}\n"], "mappings": "AAAA,OAAOA,gCAAgC,MAAM,uCAAuC,CAAC,CAAC;AACtF;AACA;AACA;;AAEA,OAAO,IAAIC,mBAAmB,GAAG,QAAQ;AACzC,eAAe,SAASC,+BAA+BA,CAACC,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAE;EAC5E,IAAIC,sBAAsB,GAAGD,IAAI,CAACC,sBAAsB;IACpDC,kBAAkB,GAAGF,IAAI,CAACE,kBAAkB;IAC5CC,WAAW,GAAGH,IAAI,CAACG,WAAW;IAC9BC,QAAQ,GAAGJ,IAAI,CAACI,QAAQ;EAC5B,IAAIC,eAAe,GAAGP,MAAM,CAACQ,OAAO,CAAC,IAAIC,MAAM,CAACR,MAAM,CAACS,OAAO,CAAC,CAAC,CAAC,EAAEP,sBAAsB,GAAGF,MAAM,CAACU,mBAAmB,CAAC,CAAC;EAAG;EAC3H;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAP,kBAAkB,IAAIH,MAAM,CAACW,4BAA4B,CAAC,CAAC,GAAGX,MAAM,CAACA,MAAM,CAAC,CAAC,CAACO,OAAO,CAACV,mBAAmB,EAAEG,MAAM,CAACW,4BAA4B,CAAC,CAAC,CAAC,GAAGX,MAAM,CAACA,MAAM,CAAC,CAAC,CAAC;EAEpK,IAAIE,sBAAsB,EAAE;IAC1B,OAAON,gCAAgC,CAACU,eAAe,CAAC;EAC1D;EAEA,OAAOA,eAAe;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}