{"ast": null, "code": "function _createForOfIteratorHelperLoose(o, allowArrayLike) {\n  var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"];\n  if (it) return (it = it.call(o)).next.bind(it);\n  if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") {\n    if (it) o = it;\n    var i = 0;\n    return function () {\n      if (i >= o.length) return {\n        done: true\n      };\n      return {\n        done: false,\n        value: o[i++]\n      };\n    };\n  }\n  throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n  return arr2;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\n\n/**\r\n * A port of Google's `PhoneNumberMatcher.java`.\r\n * https://github.com/googlei18n/libphonenumber/blob/master/java/libphonenumber/src/com/google/i18n/phonenumbers/PhoneNumberMatcher.java\r\n * Date: 08.03.2018.\r\n */\nimport PhoneNumber from './PhoneNumber.js';\nimport { MAX_LENGTH_FOR_NSN, MAX_LENGTH_COUNTRY_CODE, VALID_PUNCTUATION } from './constants.js';\nimport createExtensionPattern from './helpers/extension/createExtensionPattern.js';\nimport RegExpCache from './findNumbers/RegExpCache.js';\nimport { limit, trimAfterFirstMatch } from './findNumbers/util.js';\nimport { _pL, _pN, pZ, PZ, pNd } from './findNumbers/utf-8.js';\nimport Leniency from './findNumbers/Leniency.js';\nimport parsePreCandidate from './findNumbers/parsePreCandidate.js';\nimport isValidPreCandidate from './findNumbers/isValidPreCandidate.js';\nimport isValidCandidate, { LEAD_CLASS } from './findNumbers/isValidCandidate.js';\nimport { isSupportedCountry } from './metadata.js';\nimport parsePhoneNumber from './parsePhoneNumber.js';\nvar USE_NON_GEOGRAPHIC_COUNTRY_CODE = false;\nvar EXTN_PATTERNS_FOR_MATCHING = createExtensionPattern('matching');\n/**\r\n * Patterns used to extract phone numbers from a larger phone-number-like pattern. These are\r\n * ordered according to specificity. For example, white-space is last since that is frequently\r\n * used in numbers, not just to separate two numbers. We have separate patterns since we don't\r\n * want to break up the phone-number-like text on more than one different kind of symbol at one\r\n * time, although symbols of the same type (e.g. space) can be safely grouped together.\r\n *\r\n * Note that if there is a match, we will always check any text found up to the first match as\r\n * well.\r\n */\n\nvar INNER_MATCHES = [\n// Breaks on the slash - e.g. \"************/************\"\n'\\\\/+(.*)/',\n// Note that the bracket here is inside the capturing group, since we consider it part of the\n// phone number. Will match a pattern like \"(************* (*************\".\n'(\\\\([^(]*)',\n// Breaks on a hyphen - e.g. \"12345 - ************ is my number.\"\n// We require a space on either side of the hyphen for it to be considered a separator.\n\"(?:\".concat(pZ, \"-|-\").concat(pZ, \")\").concat(pZ, \"*(.+)\"),\n// Various types of wide hyphens. Note we have decided not to enforce a space here, since it's\n// possible that it's supposed to be used to break two numbers without spaces, and we haven't\n// seen many instances of it used within a number.\n\"[\\u2012-\\u2015\\uFF0D]\".concat(pZ, \"*(.+)\"),\n// Breaks on a full stop - e.g. \"12345. ************ is my number.\"\n\"\\\\.+\".concat(pZ, \"*([^.]+)\"),\n// Breaks on space - e.g. \"3324451234 8002341234\"\n\"\".concat(pZ, \"+(\").concat(PZ, \"+)\")]; // Limit on the number of leading (plus) characters.\n\nvar leadLimit = limit(0, 2); // Limit on the number of consecutive punctuation characters.\n\nvar punctuationLimit = limit(0, 4);\n/* The maximum number of digits allowed in a digit-separated block. As we allow all digits in a\r\n * single block, set high enough to accommodate the entire national number and the international\r\n * country code. */\n\nvar digitBlockLimit = MAX_LENGTH_FOR_NSN + MAX_LENGTH_COUNTRY_CODE; // Limit on the number of blocks separated by punctuation.\n// Uses digitBlockLimit since some formats use spaces to separate each digit.\n\nvar blockLimit = limit(0, digitBlockLimit);\n/* A punctuation sequence allowing white space. */\n\nvar punctuation = \"[\".concat(VALID_PUNCTUATION, \"]\") + punctuationLimit; // A digits block without punctuation.\n\nvar digitSequence = pNd + limit(1, digitBlockLimit);\n/**\r\n * Phone number pattern allowing optional punctuation.\r\n * The phone number pattern used by `find()`, similar to\r\n * VALID_PHONE_NUMBER, but with the following differences:\r\n * <ul>\r\n *   <li>All captures are limited in order to place an upper bound to the text matched by the\r\n *       pattern.\r\n * <ul>\r\n *   <li>Leading punctuation / plus signs are limited.\r\n *   <li>Consecutive occurrences of punctuation are limited.\r\n *   <li>Number of digits is limited.\r\n * </ul>\r\n *   <li>No whitespace is allowed at the start or end.\r\n *   <li>No alpha digits (vanity numbers such as 1-800-SIX-FLAGS) are currently supported.\r\n * </ul>\r\n */\n\nvar PATTERN = '(?:' + LEAD_CLASS + punctuation + ')' + leadLimit + digitSequence + '(?:' + punctuation + digitSequence + ')' + blockLimit + '(?:' + EXTN_PATTERNS_FOR_MATCHING + ')?'; // Regular expression of trailing characters that we want to remove.\n// We remove all characters that are not alpha or numerical characters.\n// The hash character is retained here, as it may signify\n// the previous block was an extension.\n//\n// // Don't know what does '&&' mean here.\n// const UNWANTED_END_CHAR_PATTERN = new RegExp(`[[\\\\P{N}&&\\\\P{L}]&&[^#]]+$`)\n//\n\nvar UNWANTED_END_CHAR_PATTERN = new RegExp(\"[^\".concat(_pN).concat(_pL, \"#]+$\"));\nvar NON_DIGITS_PATTERN = /(\\D+)/;\nvar MAX_SAFE_INTEGER = Number.MAX_SAFE_INTEGER || Math.pow(2, 53) - 1;\n/**\r\n * A stateful class that finds and extracts telephone numbers from {@linkplain CharSequence text}.\r\n * Instances can be created using the {@linkplain PhoneNumberUtil#findNumbers factory methods} in\r\n * {@link PhoneNumberUtil}.\r\n *\r\n * <p>Vanity numbers (phone numbers using alphabetic digits such as <tt>1-800-SIX-FLAGS</tt> are\r\n * not found.\r\n *\r\n * <p>This class is not thread-safe.\r\n */\n\nvar PhoneNumberMatcher = /*#__PURE__*/function () {\n  /**\r\n   * @param {string} text — the character sequence that we will search, null for no text.\r\n   * @param {'POSSIBLE'|'VALID'|'STRICT_GROUPING'|'EXACT_GROUPING'} [options.leniency] — The leniency to use when evaluating candidate phone numbers. See `source/findNumbers/Leniency.js` for more details.\r\n   * @param {number} [options.maxTries] — The maximum number of invalid numbers to try before giving up on the text. This is to cover degenerate cases where the text has a lot of false positives in it. Must be >= 0.\r\n   */\n  function PhoneNumberMatcher() {\n    var text = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var metadata = arguments.length > 2 ? arguments[2] : undefined;\n    _classCallCheck(this, PhoneNumberMatcher);\n    options = {\n      v2: options.v2,\n      defaultCallingCode: options.defaultCallingCode,\n      defaultCountry: options.defaultCountry && isSupportedCountry(options.defaultCountry, metadata) ? options.defaultCountry : undefined,\n      leniency: options.leniency || (options.extended ? 'POSSIBLE' : 'VALID'),\n      maxTries: options.maxTries || MAX_SAFE_INTEGER\n    }; // Validate `leniency`.\n\n    if (!options.leniency) {\n      throw new TypeError('`leniency` is required');\n    }\n    if (options.leniency !== 'POSSIBLE' && options.leniency !== 'VALID') {\n      throw new TypeError(\"Invalid `leniency`: \\\"\".concat(options.leniency, \"\\\". Supported values: \\\"POSSIBLE\\\", \\\"VALID\\\".\"));\n    } // Validate `maxTries`.\n\n    if (options.maxTries < 0) {\n      throw new TypeError('`maxTries` must be `>= 0`');\n    }\n    this.text = text;\n    this.options = options;\n    this.metadata = metadata; // The degree of phone number validation.\n\n    this.leniency = Leniency[options.leniency];\n    if (!this.leniency) {\n      throw new TypeError(\"Unknown leniency: \\\"\".concat(options.leniency, \"\\\"\"));\n    }\n    /** The maximum number of retries after matching an invalid number. */\n\n    this.maxTries = options.maxTries;\n    this.PATTERN = new RegExp(PATTERN, 'ig');\n    /** The iteration tristate. */\n\n    this.state = 'NOT_READY';\n    /** The next index to start searching at. Undefined in {@link State#DONE}. */\n\n    this.searchIndex = 0; // A cache for frequently used country-specific regular expressions. Set to 32 to cover ~2-3\n    // countries being used for the same doc with ~10 patterns for each country. Some pages will have\n    // a lot more countries in use, but typically fewer numbers for each so expanding the cache for\n    // that use-case won't have a lot of benefit.\n\n    this.regExpCache = new RegExpCache(32);\n  }\n  /**\r\n   * Attempts to find the next subsequence in the searched sequence on or after {@code searchIndex}\r\n   * that represents a phone number. Returns the next match, null if none was found.\r\n   *\r\n   * @param index  the search index to start searching at\r\n   * @return  the phone number match found, null if none can be found\r\n   */\n\n  _createClass(PhoneNumberMatcher, [{\n    key: \"find\",\n    value: function find() {\n      // // Reset the regular expression.\n      // this.PATTERN.lastIndex = index\n      var matches;\n      while (this.maxTries > 0 && (matches = this.PATTERN.exec(this.text)) !== null) {\n        var candidate = matches[0];\n        var offset = matches.index;\n        candidate = parsePreCandidate(candidate);\n        if (isValidPreCandidate(candidate, offset, this.text)) {\n          var match =\n          // Try to come up with a valid match given the entire candidate.\n          this.parseAndVerify(candidate, offset, this.text) // If that failed, try to find an \"inner match\" -\n          // there might be a phone number within this candidate.\n          || this.extractInnerMatch(candidate, offset, this.text);\n          if (match) {\n            if (this.options.v2) {\n              return {\n                startsAt: match.startsAt,\n                endsAt: match.endsAt,\n                number: match.phoneNumber\n              };\n            } else {\n              var phoneNumber = match.phoneNumber;\n              var result = {\n                startsAt: match.startsAt,\n                endsAt: match.endsAt,\n                phone: phoneNumber.nationalNumber\n              };\n              if (phoneNumber.country) {\n                /* istanbul ignore if */\n                if (USE_NON_GEOGRAPHIC_COUNTRY_CODE && country === '001') {\n                  result.countryCallingCode = phoneNumber.countryCallingCode;\n                } else {\n                  result.country = phoneNumber.country;\n                }\n              } else {\n                result.countryCallingCode = phoneNumber.countryCallingCode;\n              }\n              if (phoneNumber.ext) {\n                result.ext = phoneNumber.ext;\n              }\n              return result;\n            }\n          }\n        }\n        this.maxTries--;\n      }\n    }\n    /**\r\n     * Attempts to extract a match from `substring`\r\n     * if the substring itself does not qualify as a match.\r\n     */\n  }, {\n    key: \"extractInnerMatch\",\n    value: function extractInnerMatch(substring, offset, text) {\n      for (var _iterator = _createForOfIteratorHelperLoose(INNER_MATCHES), _step; !(_step = _iterator()).done;) {\n        var innerMatchPattern = _step.value;\n        var isFirstMatch = true;\n        var candidateMatch = void 0;\n        var innerMatchRegExp = new RegExp(innerMatchPattern, 'g');\n        while (this.maxTries > 0 && (candidateMatch = innerMatchRegExp.exec(substring)) !== null) {\n          if (isFirstMatch) {\n            // We should handle any group before this one too.\n            var _candidate = trimAfterFirstMatch(UNWANTED_END_CHAR_PATTERN, substring.slice(0, candidateMatch.index));\n            var _match = this.parseAndVerify(_candidate, offset, text);\n            if (_match) {\n              return _match;\n            }\n            this.maxTries--;\n            isFirstMatch = false;\n          }\n          var candidate = trimAfterFirstMatch(UNWANTED_END_CHAR_PATTERN, candidateMatch[1]); // Java code does `groupMatcher.start(1)` here,\n          // but there's no way in javascript to get a `candidate` start index,\n          // therefore resort to using this kind of an approximation.\n          // (`groupMatcher` is called `candidateInSubstringMatch` in this javascript port)\n          // https://stackoverflow.com/questions/15934353/get-index-of-each-capture-in-a-javascript-regex\n\n          var candidateIndexGuess = substring.indexOf(candidate, candidateMatch.index);\n          var match = this.parseAndVerify(candidate, offset + candidateIndexGuess, text);\n          if (match) {\n            return match;\n          }\n          this.maxTries--;\n        }\n      }\n    }\n    /**\r\n     * Parses a phone number from the `candidate` using `parse` and\r\n     * verifies it matches the requested `leniency`. If parsing and verification succeed,\r\n     * a corresponding `PhoneNumberMatch` is returned, otherwise this method returns `null`.\r\n     *\r\n     * @param candidate  the candidate match\r\n     * @param offset  the offset of {@code candidate} within {@link #text}\r\n     * @return  the parsed and validated phone number match, or null\r\n     */\n  }, {\n    key: \"parseAndVerify\",\n    value: function parseAndVerify(candidate, offset, text) {\n      if (!isValidCandidate(candidate, offset, text, this.options.leniency)) {\n        return;\n      }\n      var phoneNumber = parsePhoneNumber(candidate, {\n        extended: true,\n        defaultCountry: this.options.defaultCountry,\n        defaultCallingCode: this.options.defaultCallingCode\n      }, this.metadata);\n      if (!phoneNumber) {\n        return;\n      }\n      if (!phoneNumber.isPossible()) {\n        return;\n      }\n      if (this.leniency(phoneNumber, {\n        candidate: candidate,\n        defaultCountry: this.options.defaultCountry,\n        metadata: this.metadata,\n        regExpCache: this.regExpCache\n      })) {\n        return {\n          startsAt: offset,\n          endsAt: offset + candidate.length,\n          phoneNumber: phoneNumber\n        };\n      }\n    }\n  }, {\n    key: \"hasNext\",\n    value: function hasNext() {\n      if (this.state === 'NOT_READY') {\n        this.lastMatch = this.find(); // (this.searchIndex)\n\n        if (this.lastMatch) {\n          // this.searchIndex = this.lastMatch.endsAt\n          this.state = 'READY';\n        } else {\n          this.state = 'DONE';\n        }\n      }\n      return this.state === 'READY';\n    }\n  }, {\n    key: \"next\",\n    value: function next() {\n      // Check the state and find the next match as a side-effect if necessary.\n      if (!this.hasNext()) {\n        throw new Error('No next element');\n      } // Don't retain that memory any longer than necessary.\n\n      var result = this.lastMatch;\n      this.lastMatch = null;\n      this.state = 'NOT_READY';\n      return result;\n    }\n  }]);\n  return PhoneNumberMatcher;\n}();\nexport { PhoneNumberMatcher as default };", "map": {"version": 3, "names": ["_createForOfIteratorHelperLoose", "o", "allowArrayLike", "it", "Symbol", "iterator", "call", "next", "bind", "Array", "isArray", "_unsupportedIterableToArray", "length", "i", "done", "value", "TypeError", "minLen", "_arrayLikeToArray", "n", "Object", "prototype", "toString", "slice", "constructor", "name", "from", "test", "arr", "len", "arr2", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "_defineProperties", "target", "props", "descriptor", "enumerable", "configurable", "writable", "defineProperty", "key", "_createClass", "protoProps", "staticProps", "PhoneNumber", "MAX_LENGTH_FOR_NSN", "MAX_LENGTH_COUNTRY_CODE", "VALID_PUNCTUATION", "createExtensionPattern", "RegExpCache", "limit", "trimAfterFirstMatch", "_pL", "_pN", "pZ", "PZ", "pNd", "<PERSON><PERSON><PERSON>", "parsePreCandidate", "isValidPreCandidate", "isValidCandidate", "LEAD_CLASS", "isSupportedCountry", "parsePhoneNumber", "USE_NON_GEOGRAPHIC_COUNTRY_CODE", "EXTN_PATTERNS_FOR_MATCHING", "INNER_MATCHES", "concat", "leadLimit", "punctuationLimit", "digitBlockLimit", "blockLimit", "punctuation", "digitSequence", "PATTERN", "UNWANTED_END_CHAR_PATTERN", "RegExp", "NON_DIGITS_PATTERN", "MAX_SAFE_INTEGER", "Number", "Math", "pow", "PhoneNumberMatcher", "text", "arguments", "undefined", "options", "metadata", "v2", "defaultCallingCode", "defaultCountry", "leniency", "extended", "max<PERSON>ries", "state", "searchIndex", "regExpCache", "find", "matches", "exec", "candidate", "offset", "index", "match", "parseAndVerify", "extractInnerMatch", "startsAt", "endsAt", "number", "phoneNumber", "result", "phone", "nationalNumber", "country", "countryCallingCode", "ext", "substring", "_iterator", "_step", "innerMatchPattern", "isFirstMatch", "<PERSON><PERSON><PERSON>", "innerMatchRegExp", "_candidate", "_match", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indexOf", "isPossible", "hasNext", "lastMatch", "Error", "default"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/PhoneNumberMatcher.js"], "sourcesContent": ["function _createForOfIteratorHelperLoose(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (it) return (it = it.call(o)).next.bind(it); if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; return function () { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\n\n/**\r\n * A port of Google's `PhoneNumberMatcher.java`.\r\n * https://github.com/googlei18n/libphonenumber/blob/master/java/libphonenumber/src/com/google/i18n/phonenumbers/PhoneNumberMatcher.java\r\n * Date: 08.03.2018.\r\n */\nimport PhoneNumber from './PhoneNumber.js';\nimport { MAX_LENGTH_FOR_NSN, MAX_LENGTH_COUNTRY_CODE, VALID_PUNCTUATION } from './constants.js';\nimport createExtensionPattern from './helpers/extension/createExtensionPattern.js';\nimport RegExpCache from './findNumbers/RegExpCache.js';\nimport { limit, trimAfterFirstMatch } from './findNumbers/util.js';\nimport { _pL, _pN, pZ, PZ, pNd } from './findNumbers/utf-8.js';\nimport Leniency from './findNumbers/Leniency.js';\nimport parsePreCandidate from './findNumbers/parsePreCandidate.js';\nimport isValidPreCandidate from './findNumbers/isValidPreCandidate.js';\nimport isValidCandidate, { LEAD_CLASS } from './findNumbers/isValidCandidate.js';\nimport { isSupportedCountry } from './metadata.js';\nimport parsePhoneNumber from './parsePhoneNumber.js';\nvar USE_NON_GEOGRAPHIC_COUNTRY_CODE = false;\nvar EXTN_PATTERNS_FOR_MATCHING = createExtensionPattern('matching');\n/**\r\n * Patterns used to extract phone numbers from a larger phone-number-like pattern. These are\r\n * ordered according to specificity. For example, white-space is last since that is frequently\r\n * used in numbers, not just to separate two numbers. We have separate patterns since we don't\r\n * want to break up the phone-number-like text on more than one different kind of symbol at one\r\n * time, although symbols of the same type (e.g. space) can be safely grouped together.\r\n *\r\n * Note that if there is a match, we will always check any text found up to the first match as\r\n * well.\r\n */\n\nvar INNER_MATCHES = [// Breaks on the slash - e.g. \"************/************\"\n'\\\\/+(.*)/', // Note that the bracket here is inside the capturing group, since we consider it part of the\n// phone number. Will match a pattern like \"(************* (*************\".\n'(\\\\([^(]*)', // Breaks on a hyphen - e.g. \"12345 - ************ is my number.\"\n// We require a space on either side of the hyphen for it to be considered a separator.\n\"(?:\".concat(pZ, \"-|-\").concat(pZ, \")\").concat(pZ, \"*(.+)\"), // Various types of wide hyphens. Note we have decided not to enforce a space here, since it's\n// possible that it's supposed to be used to break two numbers without spaces, and we haven't\n// seen many instances of it used within a number.\n\"[\\u2012-\\u2015\\uFF0D]\".concat(pZ, \"*(.+)\"), // Breaks on a full stop - e.g. \"12345. ************ is my number.\"\n\"\\\\.+\".concat(pZ, \"*([^.]+)\"), // Breaks on space - e.g. \"3324451234 8002341234\"\n\"\".concat(pZ, \"+(\").concat(PZ, \"+)\")]; // Limit on the number of leading (plus) characters.\n\nvar leadLimit = limit(0, 2); // Limit on the number of consecutive punctuation characters.\n\nvar punctuationLimit = limit(0, 4);\n/* The maximum number of digits allowed in a digit-separated block. As we allow all digits in a\r\n * single block, set high enough to accommodate the entire national number and the international\r\n * country code. */\n\nvar digitBlockLimit = MAX_LENGTH_FOR_NSN + MAX_LENGTH_COUNTRY_CODE; // Limit on the number of blocks separated by punctuation.\n// Uses digitBlockLimit since some formats use spaces to separate each digit.\n\nvar blockLimit = limit(0, digitBlockLimit);\n/* A punctuation sequence allowing white space. */\n\nvar punctuation = \"[\".concat(VALID_PUNCTUATION, \"]\") + punctuationLimit; // A digits block without punctuation.\n\nvar digitSequence = pNd + limit(1, digitBlockLimit);\n/**\r\n * Phone number pattern allowing optional punctuation.\r\n * The phone number pattern used by `find()`, similar to\r\n * VALID_PHONE_NUMBER, but with the following differences:\r\n * <ul>\r\n *   <li>All captures are limited in order to place an upper bound to the text matched by the\r\n *       pattern.\r\n * <ul>\r\n *   <li>Leading punctuation / plus signs are limited.\r\n *   <li>Consecutive occurrences of punctuation are limited.\r\n *   <li>Number of digits is limited.\r\n * </ul>\r\n *   <li>No whitespace is allowed at the start or end.\r\n *   <li>No alpha digits (vanity numbers such as 1-800-SIX-FLAGS) are currently supported.\r\n * </ul>\r\n */\n\nvar PATTERN = '(?:' + LEAD_CLASS + punctuation + ')' + leadLimit + digitSequence + '(?:' + punctuation + digitSequence + ')' + blockLimit + '(?:' + EXTN_PATTERNS_FOR_MATCHING + ')?'; // Regular expression of trailing characters that we want to remove.\n// We remove all characters that are not alpha or numerical characters.\n// The hash character is retained here, as it may signify\n// the previous block was an extension.\n//\n// // Don't know what does '&&' mean here.\n// const UNWANTED_END_CHAR_PATTERN = new RegExp(`[[\\\\P{N}&&\\\\P{L}]&&[^#]]+$`)\n//\n\nvar UNWANTED_END_CHAR_PATTERN = new RegExp(\"[^\".concat(_pN).concat(_pL, \"#]+$\"));\nvar NON_DIGITS_PATTERN = /(\\D+)/;\nvar MAX_SAFE_INTEGER = Number.MAX_SAFE_INTEGER || Math.pow(2, 53) - 1;\n/**\r\n * A stateful class that finds and extracts telephone numbers from {@linkplain CharSequence text}.\r\n * Instances can be created using the {@linkplain PhoneNumberUtil#findNumbers factory methods} in\r\n * {@link PhoneNumberUtil}.\r\n *\r\n * <p>Vanity numbers (phone numbers using alphabetic digits such as <tt>1-800-SIX-FLAGS</tt> are\r\n * not found.\r\n *\r\n * <p>This class is not thread-safe.\r\n */\n\nvar PhoneNumberMatcher = /*#__PURE__*/function () {\n  /**\r\n   * @param {string} text — the character sequence that we will search, null for no text.\r\n   * @param {'POSSIBLE'|'VALID'|'STRICT_GROUPING'|'EXACT_GROUPING'} [options.leniency] — The leniency to use when evaluating candidate phone numbers. See `source/findNumbers/Leniency.js` for more details.\r\n   * @param {number} [options.maxTries] — The maximum number of invalid numbers to try before giving up on the text. This is to cover degenerate cases where the text has a lot of false positives in it. Must be >= 0.\r\n   */\n  function PhoneNumberMatcher() {\n    var text = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var metadata = arguments.length > 2 ? arguments[2] : undefined;\n\n    _classCallCheck(this, PhoneNumberMatcher);\n\n    options = {\n      v2: options.v2,\n      defaultCallingCode: options.defaultCallingCode,\n      defaultCountry: options.defaultCountry && isSupportedCountry(options.defaultCountry, metadata) ? options.defaultCountry : undefined,\n      leniency: options.leniency || (options.extended ? 'POSSIBLE' : 'VALID'),\n      maxTries: options.maxTries || MAX_SAFE_INTEGER\n    }; // Validate `leniency`.\n\n    if (!options.leniency) {\n      throw new TypeError('`leniency` is required');\n    }\n\n    if (options.leniency !== 'POSSIBLE' && options.leniency !== 'VALID') {\n      throw new TypeError(\"Invalid `leniency`: \\\"\".concat(options.leniency, \"\\\". Supported values: \\\"POSSIBLE\\\", \\\"VALID\\\".\"));\n    } // Validate `maxTries`.\n\n\n    if (options.maxTries < 0) {\n      throw new TypeError('`maxTries` must be `>= 0`');\n    }\n\n    this.text = text;\n    this.options = options;\n    this.metadata = metadata; // The degree of phone number validation.\n\n    this.leniency = Leniency[options.leniency];\n\n    if (!this.leniency) {\n      throw new TypeError(\"Unknown leniency: \\\"\".concat(options.leniency, \"\\\"\"));\n    }\n    /** The maximum number of retries after matching an invalid number. */\n\n\n    this.maxTries = options.maxTries;\n    this.PATTERN = new RegExp(PATTERN, 'ig');\n    /** The iteration tristate. */\n\n    this.state = 'NOT_READY';\n    /** The next index to start searching at. Undefined in {@link State#DONE}. */\n\n    this.searchIndex = 0; // A cache for frequently used country-specific regular expressions. Set to 32 to cover ~2-3\n    // countries being used for the same doc with ~10 patterns for each country. Some pages will have\n    // a lot more countries in use, but typically fewer numbers for each so expanding the cache for\n    // that use-case won't have a lot of benefit.\n\n    this.regExpCache = new RegExpCache(32);\n  }\n  /**\r\n   * Attempts to find the next subsequence in the searched sequence on or after {@code searchIndex}\r\n   * that represents a phone number. Returns the next match, null if none was found.\r\n   *\r\n   * @param index  the search index to start searching at\r\n   * @return  the phone number match found, null if none can be found\r\n   */\n\n\n  _createClass(PhoneNumberMatcher, [{\n    key: \"find\",\n    value: function find() {\n      // // Reset the regular expression.\n      // this.PATTERN.lastIndex = index\n      var matches;\n\n      while (this.maxTries > 0 && (matches = this.PATTERN.exec(this.text)) !== null) {\n        var candidate = matches[0];\n        var offset = matches.index;\n        candidate = parsePreCandidate(candidate);\n\n        if (isValidPreCandidate(candidate, offset, this.text)) {\n          var match = // Try to come up with a valid match given the entire candidate.\n          this.parseAndVerify(candidate, offset, this.text) // If that failed, try to find an \"inner match\" -\n          // there might be a phone number within this candidate.\n          || this.extractInnerMatch(candidate, offset, this.text);\n\n          if (match) {\n            if (this.options.v2) {\n              return {\n                startsAt: match.startsAt,\n                endsAt: match.endsAt,\n                number: match.phoneNumber\n              };\n            } else {\n              var phoneNumber = match.phoneNumber;\n              var result = {\n                startsAt: match.startsAt,\n                endsAt: match.endsAt,\n                phone: phoneNumber.nationalNumber\n              };\n\n              if (phoneNumber.country) {\n                /* istanbul ignore if */\n                if (USE_NON_GEOGRAPHIC_COUNTRY_CODE && country === '001') {\n                  result.countryCallingCode = phoneNumber.countryCallingCode;\n                } else {\n                  result.country = phoneNumber.country;\n                }\n              } else {\n                result.countryCallingCode = phoneNumber.countryCallingCode;\n              }\n\n              if (phoneNumber.ext) {\n                result.ext = phoneNumber.ext;\n              }\n\n              return result;\n            }\n          }\n        }\n\n        this.maxTries--;\n      }\n    }\n    /**\r\n     * Attempts to extract a match from `substring`\r\n     * if the substring itself does not qualify as a match.\r\n     */\n\n  }, {\n    key: \"extractInnerMatch\",\n    value: function extractInnerMatch(substring, offset, text) {\n      for (var _iterator = _createForOfIteratorHelperLoose(INNER_MATCHES), _step; !(_step = _iterator()).done;) {\n        var innerMatchPattern = _step.value;\n        var isFirstMatch = true;\n        var candidateMatch = void 0;\n        var innerMatchRegExp = new RegExp(innerMatchPattern, 'g');\n\n        while (this.maxTries > 0 && (candidateMatch = innerMatchRegExp.exec(substring)) !== null) {\n          if (isFirstMatch) {\n            // We should handle any group before this one too.\n            var _candidate = trimAfterFirstMatch(UNWANTED_END_CHAR_PATTERN, substring.slice(0, candidateMatch.index));\n\n            var _match = this.parseAndVerify(_candidate, offset, text);\n\n            if (_match) {\n              return _match;\n            }\n\n            this.maxTries--;\n            isFirstMatch = false;\n          }\n\n          var candidate = trimAfterFirstMatch(UNWANTED_END_CHAR_PATTERN, candidateMatch[1]); // Java code does `groupMatcher.start(1)` here,\n          // but there's no way in javascript to get a `candidate` start index,\n          // therefore resort to using this kind of an approximation.\n          // (`groupMatcher` is called `candidateInSubstringMatch` in this javascript port)\n          // https://stackoverflow.com/questions/15934353/get-index-of-each-capture-in-a-javascript-regex\n\n          var candidateIndexGuess = substring.indexOf(candidate, candidateMatch.index);\n          var match = this.parseAndVerify(candidate, offset + candidateIndexGuess, text);\n\n          if (match) {\n            return match;\n          }\n\n          this.maxTries--;\n        }\n      }\n    }\n    /**\r\n     * Parses a phone number from the `candidate` using `parse` and\r\n     * verifies it matches the requested `leniency`. If parsing and verification succeed,\r\n     * a corresponding `PhoneNumberMatch` is returned, otherwise this method returns `null`.\r\n     *\r\n     * @param candidate  the candidate match\r\n     * @param offset  the offset of {@code candidate} within {@link #text}\r\n     * @return  the parsed and validated phone number match, or null\r\n     */\n\n  }, {\n    key: \"parseAndVerify\",\n    value: function parseAndVerify(candidate, offset, text) {\n      if (!isValidCandidate(candidate, offset, text, this.options.leniency)) {\n        return;\n      }\n\n      var phoneNumber = parsePhoneNumber(candidate, {\n        extended: true,\n        defaultCountry: this.options.defaultCountry,\n        defaultCallingCode: this.options.defaultCallingCode\n      }, this.metadata);\n\n      if (!phoneNumber) {\n        return;\n      }\n\n      if (!phoneNumber.isPossible()) {\n        return;\n      }\n\n      if (this.leniency(phoneNumber, {\n        candidate: candidate,\n        defaultCountry: this.options.defaultCountry,\n        metadata: this.metadata,\n        regExpCache: this.regExpCache\n      })) {\n        return {\n          startsAt: offset,\n          endsAt: offset + candidate.length,\n          phoneNumber: phoneNumber\n        };\n      }\n    }\n  }, {\n    key: \"hasNext\",\n    value: function hasNext() {\n      if (this.state === 'NOT_READY') {\n        this.lastMatch = this.find(); // (this.searchIndex)\n\n        if (this.lastMatch) {\n          // this.searchIndex = this.lastMatch.endsAt\n          this.state = 'READY';\n        } else {\n          this.state = 'DONE';\n        }\n      }\n\n      return this.state === 'READY';\n    }\n  }, {\n    key: \"next\",\n    value: function next() {\n      // Check the state and find the next match as a side-effect if necessary.\n      if (!this.hasNext()) {\n        throw new Error('No next element');\n      } // Don't retain that memory any longer than necessary.\n\n\n      var result = this.lastMatch;\n      this.lastMatch = null;\n      this.state = 'NOT_READY';\n      return result;\n    }\n  }]);\n\n  return PhoneNumberMatcher;\n}();\n\nexport { PhoneNumberMatcher as default };\n"], "mappings": "AAAA,SAASA,+BAA+BA,CAACC,CAAC,EAAEC,cAAc,EAAE;EAAE,IAAIC,EAAE,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAIH,CAAC,CAACG,MAAM,CAACC,QAAQ,CAAC,IAAIJ,CAAC,CAAC,YAAY,CAAC;EAAE,IAAIE,EAAE,EAAE,OAAO,CAACA,EAAE,GAAGA,EAAE,CAACG,IAAI,CAACL,CAAC,CAAC,EAAEM,IAAI,CAACC,IAAI,CAACL,EAAE,CAAC;EAAE,IAAIM,KAAK,CAACC,OAAO,CAACT,CAAC,CAAC,KAAKE,EAAE,GAAGQ,2BAA2B,CAACV,CAAC,CAAC,CAAC,IAAIC,cAAc,IAAID,CAAC,IAAI,OAAOA,CAAC,CAACW,MAAM,KAAK,QAAQ,EAAE;IAAE,IAAIT,EAAE,EAAEF,CAAC,GAAGE,EAAE;IAAE,IAAIU,CAAC,GAAG,CAAC;IAAE,OAAO,YAAY;MAAE,IAAIA,CAAC,IAAIZ,CAAC,CAACW,MAAM,EAAE,OAAO;QAAEE,IAAI,EAAE;MAAK,CAAC;MAAE,OAAO;QAAEA,IAAI,EAAE,KAAK;QAAEC,KAAK,EAAEd,CAAC,CAACY,CAAC,EAAE;MAAE,CAAC;IAAE,CAAC;EAAE;EAAE,MAAM,IAAIG,SAAS,CAAC,uIAAuI,CAAC;AAAE;AAE3lB,SAASL,2BAA2BA,CAACV,CAAC,EAAEgB,MAAM,EAAE;EAAE,IAAI,CAAChB,CAAC,EAAE;EAAQ,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOiB,iBAAiB,CAACjB,CAAC,EAAEgB,MAAM,CAAC;EAAE,IAAIE,CAAC,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAAChB,IAAI,CAACL,CAAC,CAAC,CAACsB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAE,IAAIJ,CAAC,KAAK,QAAQ,IAAIlB,CAAC,CAACuB,WAAW,EAAEL,CAAC,GAAGlB,CAAC,CAACuB,WAAW,CAACC,IAAI;EAAE,IAAIN,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOV,KAAK,CAACiB,IAAI,CAACzB,CAAC,CAAC;EAAE,IAAIkB,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACQ,IAAI,CAACR,CAAC,CAAC,EAAE,OAAOD,iBAAiB,CAACjB,CAAC,EAAEgB,MAAM,CAAC;AAAE;AAE/Z,SAASC,iBAAiBA,CAACU,GAAG,EAAEC,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGD,GAAG,CAAChB,MAAM,EAAEiB,GAAG,GAAGD,GAAG,CAAChB,MAAM;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEiB,IAAI,GAAG,IAAIrB,KAAK,CAACoB,GAAG,CAAC,EAAEhB,CAAC,GAAGgB,GAAG,EAAEhB,CAAC,EAAE,EAAE;IAAEiB,IAAI,CAACjB,CAAC,CAAC,GAAGe,GAAG,CAACf,CAAC,CAAC;EAAE;EAAE,OAAOiB,IAAI;AAAE;AAEtL,SAASC,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIjB,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AAExJ,SAASkB,iBAAiBA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAAE,KAAK,IAAIvB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuB,KAAK,CAACxB,MAAM,EAAEC,CAAC,EAAE,EAAE;IAAE,IAAIwB,UAAU,GAAGD,KAAK,CAACvB,CAAC,CAAC;IAAEwB,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;IAAED,UAAU,CAACE,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;IAAEpB,MAAM,CAACqB,cAAc,CAACN,MAAM,EAAEE,UAAU,CAACK,GAAG,EAAEL,UAAU,CAAC;EAAE;AAAE;AAE5T,SAASM,YAAYA,CAACV,WAAW,EAAEW,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEV,iBAAiB,CAACD,WAAW,CAACZ,SAAS,EAAEuB,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAEX,iBAAiB,CAACD,WAAW,EAAEY,WAAW,CAAC;EAAEzB,MAAM,CAACqB,cAAc,CAACR,WAAW,EAAE,WAAW,EAAE;IAAEO,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAOP,WAAW;AAAE;;AAE5R;AACA;AACA;AACA;AACA;AACA,OAAOa,WAAW,MAAM,kBAAkB;AAC1C,SAASC,kBAAkB,EAAEC,uBAAuB,EAAEC,iBAAiB,QAAQ,gBAAgB;AAC/F,OAAOC,sBAAsB,MAAM,+CAA+C;AAClF,OAAOC,WAAW,MAAM,8BAA8B;AACtD,SAASC,KAAK,EAAEC,mBAAmB,QAAQ,uBAAuB;AAClE,SAASC,GAAG,EAAEC,GAAG,EAAEC,EAAE,EAAEC,EAAE,EAAEC,GAAG,QAAQ,wBAAwB;AAC9D,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,iBAAiB,MAAM,oCAAoC;AAClE,OAAOC,mBAAmB,MAAM,sCAAsC;AACtE,OAAOC,gBAAgB,IAAIC,UAAU,QAAQ,mCAAmC;AAChF,SAASC,kBAAkB,QAAQ,eAAe;AAClD,OAAOC,gBAAgB,MAAM,uBAAuB;AACpD,IAAIC,+BAA+B,GAAG,KAAK;AAC3C,IAAIC,0BAA0B,GAAGjB,sBAAsB,CAAC,UAAU,CAAC;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIkB,aAAa,GAAG;AAAC;AACrB,WAAW;AAAE;AACb;AACA,YAAY;AAAE;AACd;AACA,KAAK,CAACC,MAAM,CAACb,EAAE,EAAE,KAAK,CAAC,CAACa,MAAM,CAACb,EAAE,EAAE,GAAG,CAAC,CAACa,MAAM,CAACb,EAAE,EAAE,OAAO,CAAC;AAAE;AAC7D;AACA;AACA,uBAAuB,CAACa,MAAM,CAACb,EAAE,EAAE,OAAO,CAAC;AAAE;AAC7C,MAAM,CAACa,MAAM,CAACb,EAAE,EAAE,UAAU,CAAC;AAAE;AAC/B,EAAE,CAACa,MAAM,CAACb,EAAE,EAAE,IAAI,CAAC,CAACa,MAAM,CAACZ,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;;AAEvC,IAAIa,SAAS,GAAGlB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;AAE7B,IAAImB,gBAAgB,GAAGnB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;AAClC;AACA;AACA;;AAEA,IAAIoB,eAAe,GAAGzB,kBAAkB,GAAGC,uBAAuB,CAAC,CAAC;AACpE;;AAEA,IAAIyB,UAAU,GAAGrB,KAAK,CAAC,CAAC,EAAEoB,eAAe,CAAC;AAC1C;;AAEA,IAAIE,WAAW,GAAG,GAAG,CAACL,MAAM,CAACpB,iBAAiB,EAAE,GAAG,CAAC,GAAGsB,gBAAgB,CAAC,CAAC;;AAEzE,IAAII,aAAa,GAAGjB,GAAG,GAAGN,KAAK,CAAC,CAAC,EAAEoB,eAAe,CAAC;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAII,OAAO,GAAG,KAAK,GAAGb,UAAU,GAAGW,WAAW,GAAG,GAAG,GAAGJ,SAAS,GAAGK,aAAa,GAAG,KAAK,GAAGD,WAAW,GAAGC,aAAa,GAAG,GAAG,GAAGF,UAAU,GAAG,KAAK,GAAGN,0BAA0B,GAAG,IAAI,CAAC,CAAC;AACvL;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIU,yBAAyB,GAAG,IAAIC,MAAM,CAAC,IAAI,CAACT,MAAM,CAACd,GAAG,CAAC,CAACc,MAAM,CAACf,GAAG,EAAE,MAAM,CAAC,CAAC;AAChF,IAAIyB,kBAAkB,GAAG,OAAO;AAChC,IAAIC,gBAAgB,GAAGC,MAAM,CAACD,gBAAgB,IAAIE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,kBAAkB,GAAG,aAAa,YAAY;EAChD;AACF;AACA;AACA;AACA;EACE,SAASA,kBAAkBA,CAAA,EAAG;IAC5B,IAAIC,IAAI,GAAGC,SAAS,CAAC1E,MAAM,GAAG,CAAC,IAAI0E,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;IACjF,IAAIE,OAAO,GAAGF,SAAS,CAAC1E,MAAM,GAAG,CAAC,IAAI0E,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,IAAIG,QAAQ,GAAGH,SAAS,CAAC1E,MAAM,GAAG,CAAC,GAAG0E,SAAS,CAAC,CAAC,CAAC,GAAGC,SAAS;IAE9DxD,eAAe,CAAC,IAAI,EAAEqD,kBAAkB,CAAC;IAEzCI,OAAO,GAAG;MACRE,EAAE,EAAEF,OAAO,CAACE,EAAE;MACdC,kBAAkB,EAAEH,OAAO,CAACG,kBAAkB;MAC9CC,cAAc,EAAEJ,OAAO,CAACI,cAAc,IAAI5B,kBAAkB,CAACwB,OAAO,CAACI,cAAc,EAAEH,QAAQ,CAAC,GAAGD,OAAO,CAACI,cAAc,GAAGL,SAAS;MACnIM,QAAQ,EAAEL,OAAO,CAACK,QAAQ,KAAKL,OAAO,CAACM,QAAQ,GAAG,UAAU,GAAG,OAAO,CAAC;MACvEC,QAAQ,EAAEP,OAAO,CAACO,QAAQ,IAAIf;IAChC,CAAC,CAAC,CAAC;;IAEH,IAAI,CAACQ,OAAO,CAACK,QAAQ,EAAE;MACrB,MAAM,IAAI7E,SAAS,CAAC,wBAAwB,CAAC;IAC/C;IAEA,IAAIwE,OAAO,CAACK,QAAQ,KAAK,UAAU,IAAIL,OAAO,CAACK,QAAQ,KAAK,OAAO,EAAE;MACnE,MAAM,IAAI7E,SAAS,CAAC,wBAAwB,CAACqD,MAAM,CAACmB,OAAO,CAACK,QAAQ,EAAE,gDAAgD,CAAC,CAAC;IAC1H,CAAC,CAAC;;IAGF,IAAIL,OAAO,CAACO,QAAQ,GAAG,CAAC,EAAE;MACxB,MAAM,IAAI/E,SAAS,CAAC,2BAA2B,CAAC;IAClD;IAEA,IAAI,CAACqE,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACG,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,QAAQ,GAAGA,QAAQ,CAAC,CAAC;;IAE1B,IAAI,CAACI,QAAQ,GAAGlC,QAAQ,CAAC6B,OAAO,CAACK,QAAQ,CAAC;IAE1C,IAAI,CAAC,IAAI,CAACA,QAAQ,EAAE;MAClB,MAAM,IAAI7E,SAAS,CAAC,sBAAsB,CAACqD,MAAM,CAACmB,OAAO,CAACK,QAAQ,EAAE,IAAI,CAAC,CAAC;IAC5E;IACA;;IAGA,IAAI,CAACE,QAAQ,GAAGP,OAAO,CAACO,QAAQ;IAChC,IAAI,CAACnB,OAAO,GAAG,IAAIE,MAAM,CAACF,OAAO,EAAE,IAAI,CAAC;IACxC;;IAEA,IAAI,CAACoB,KAAK,GAAG,WAAW;IACxB;;IAEA,IAAI,CAACC,WAAW,GAAG,CAAC,CAAC,CAAC;IACtB;IACA;IACA;;IAEA,IAAI,CAACC,WAAW,GAAG,IAAI/C,WAAW,CAAC,EAAE,CAAC;EACxC;EACA;AACF;AACA;AACA;AACA;AACA;AACA;;EAGER,YAAY,CAACyC,kBAAkB,EAAE,CAAC;IAChC1C,GAAG,EAAE,MAAM;IACX3B,KAAK,EAAE,SAASoF,IAAIA,CAAA,EAAG;MACrB;MACA;MACA,IAAIC,OAAO;MAEX,OAAO,IAAI,CAACL,QAAQ,GAAG,CAAC,IAAI,CAACK,OAAO,GAAG,IAAI,CAACxB,OAAO,CAACyB,IAAI,CAAC,IAAI,CAAChB,IAAI,CAAC,MAAM,IAAI,EAAE;QAC7E,IAAIiB,SAAS,GAAGF,OAAO,CAAC,CAAC,CAAC;QAC1B,IAAIG,MAAM,GAAGH,OAAO,CAACI,KAAK;QAC1BF,SAAS,GAAG1C,iBAAiB,CAAC0C,SAAS,CAAC;QAExC,IAAIzC,mBAAmB,CAACyC,SAAS,EAAEC,MAAM,EAAE,IAAI,CAAClB,IAAI,CAAC,EAAE;UACrD,IAAIoB,KAAK;UAAG;UACZ,IAAI,CAACC,cAAc,CAACJ,SAAS,EAAEC,MAAM,EAAE,IAAI,CAAClB,IAAI,CAAC,CAAC;UAClD;UAAA,GACG,IAAI,CAACsB,iBAAiB,CAACL,SAAS,EAAEC,MAAM,EAAE,IAAI,CAAClB,IAAI,CAAC;UAEvD,IAAIoB,KAAK,EAAE;YACT,IAAI,IAAI,CAACjB,OAAO,CAACE,EAAE,EAAE;cACnB,OAAO;gBACLkB,QAAQ,EAAEH,KAAK,CAACG,QAAQ;gBACxBC,MAAM,EAAEJ,KAAK,CAACI,MAAM;gBACpBC,MAAM,EAAEL,KAAK,CAACM;cAChB,CAAC;YACH,CAAC,MAAM;cACL,IAAIA,WAAW,GAAGN,KAAK,CAACM,WAAW;cACnC,IAAIC,MAAM,GAAG;gBACXJ,QAAQ,EAAEH,KAAK,CAACG,QAAQ;gBACxBC,MAAM,EAAEJ,KAAK,CAACI,MAAM;gBACpBI,KAAK,EAAEF,WAAW,CAACG;cACrB,CAAC;cAED,IAAIH,WAAW,CAACI,OAAO,EAAE;gBACvB;gBACA,IAAIjD,+BAA+B,IAAIiD,OAAO,KAAK,KAAK,EAAE;kBACxDH,MAAM,CAACI,kBAAkB,GAAGL,WAAW,CAACK,kBAAkB;gBAC5D,CAAC,MAAM;kBACLJ,MAAM,CAACG,OAAO,GAAGJ,WAAW,CAACI,OAAO;gBACtC;cACF,CAAC,MAAM;gBACLH,MAAM,CAACI,kBAAkB,GAAGL,WAAW,CAACK,kBAAkB;cAC5D;cAEA,IAAIL,WAAW,CAACM,GAAG,EAAE;gBACnBL,MAAM,CAACK,GAAG,GAAGN,WAAW,CAACM,GAAG;cAC9B;cAEA,OAAOL,MAAM;YACf;UACF;QACF;QAEA,IAAI,CAACjB,QAAQ,EAAE;MACjB;IACF;IACA;AACJ;AACA;AACA;EAEE,CAAC,EAAE;IACDrD,GAAG,EAAE,mBAAmB;IACxB3B,KAAK,EAAE,SAAS4F,iBAAiBA,CAACW,SAAS,EAAEf,MAAM,EAAElB,IAAI,EAAE;MACzD,KAAK,IAAIkC,SAAS,GAAGvH,+BAA+B,CAACoE,aAAa,CAAC,EAAEoD,KAAK,EAAE,CAAC,CAACA,KAAK,GAAGD,SAAS,CAAC,CAAC,EAAEzG,IAAI,GAAG;QACxG,IAAI2G,iBAAiB,GAAGD,KAAK,CAACzG,KAAK;QACnC,IAAI2G,YAAY,GAAG,IAAI;QACvB,IAAIC,cAAc,GAAG,KAAK,CAAC;QAC3B,IAAIC,gBAAgB,GAAG,IAAI9C,MAAM,CAAC2C,iBAAiB,EAAE,GAAG,CAAC;QAEzD,OAAO,IAAI,CAAC1B,QAAQ,GAAG,CAAC,IAAI,CAAC4B,cAAc,GAAGC,gBAAgB,CAACvB,IAAI,CAACiB,SAAS,CAAC,MAAM,IAAI,EAAE;UACxF,IAAII,YAAY,EAAE;YAChB;YACA,IAAIG,UAAU,GAAGxE,mBAAmB,CAACwB,yBAAyB,EAAEyC,SAAS,CAAC/F,KAAK,CAAC,CAAC,EAAEoG,cAAc,CAACnB,KAAK,CAAC,CAAC;YAEzG,IAAIsB,MAAM,GAAG,IAAI,CAACpB,cAAc,CAACmB,UAAU,EAAEtB,MAAM,EAAElB,IAAI,CAAC;YAE1D,IAAIyC,MAAM,EAAE;cACV,OAAOA,MAAM;YACf;YAEA,IAAI,CAAC/B,QAAQ,EAAE;YACf2B,YAAY,GAAG,KAAK;UACtB;UAEA,IAAIpB,SAAS,GAAGjD,mBAAmB,CAACwB,yBAAyB,EAAE8C,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnF;UACA;UACA;UACA;;UAEA,IAAII,mBAAmB,GAAGT,SAAS,CAACU,OAAO,CAAC1B,SAAS,EAAEqB,cAAc,CAACnB,KAAK,CAAC;UAC5E,IAAIC,KAAK,GAAG,IAAI,CAACC,cAAc,CAACJ,SAAS,EAAEC,MAAM,GAAGwB,mBAAmB,EAAE1C,IAAI,CAAC;UAE9E,IAAIoB,KAAK,EAAE;YACT,OAAOA,KAAK;UACd;UAEA,IAAI,CAACV,QAAQ,EAAE;QACjB;MACF;IACF;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAEE,CAAC,EAAE;IACDrD,GAAG,EAAE,gBAAgB;IACrB3B,KAAK,EAAE,SAAS2F,cAAcA,CAACJ,SAAS,EAAEC,MAAM,EAAElB,IAAI,EAAE;MACtD,IAAI,CAACvB,gBAAgB,CAACwC,SAAS,EAAEC,MAAM,EAAElB,IAAI,EAAE,IAAI,CAACG,OAAO,CAACK,QAAQ,CAAC,EAAE;QACrE;MACF;MAEA,IAAIkB,WAAW,GAAG9C,gBAAgB,CAACqC,SAAS,EAAE;QAC5CR,QAAQ,EAAE,IAAI;QACdF,cAAc,EAAE,IAAI,CAACJ,OAAO,CAACI,cAAc;QAC3CD,kBAAkB,EAAE,IAAI,CAACH,OAAO,CAACG;MACnC,CAAC,EAAE,IAAI,CAACF,QAAQ,CAAC;MAEjB,IAAI,CAACsB,WAAW,EAAE;QAChB;MACF;MAEA,IAAI,CAACA,WAAW,CAACkB,UAAU,CAAC,CAAC,EAAE;QAC7B;MACF;MAEA,IAAI,IAAI,CAACpC,QAAQ,CAACkB,WAAW,EAAE;QAC7BT,SAAS,EAAEA,SAAS;QACpBV,cAAc,EAAE,IAAI,CAACJ,OAAO,CAACI,cAAc;QAC3CH,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBS,WAAW,EAAE,IAAI,CAACA;MACpB,CAAC,CAAC,EAAE;QACF,OAAO;UACLU,QAAQ,EAAEL,MAAM;UAChBM,MAAM,EAAEN,MAAM,GAAGD,SAAS,CAAC1F,MAAM;UACjCmG,WAAW,EAAEA;QACf,CAAC;MACH;IACF;EACF,CAAC,EAAE;IACDrE,GAAG,EAAE,SAAS;IACd3B,KAAK,EAAE,SAASmH,OAAOA,CAAA,EAAG;MACxB,IAAI,IAAI,CAAClC,KAAK,KAAK,WAAW,EAAE;QAC9B,IAAI,CAACmC,SAAS,GAAG,IAAI,CAAChC,IAAI,CAAC,CAAC,CAAC,CAAC;;QAE9B,IAAI,IAAI,CAACgC,SAAS,EAAE;UAClB;UACA,IAAI,CAACnC,KAAK,GAAG,OAAO;QACtB,CAAC,MAAM;UACL,IAAI,CAACA,KAAK,GAAG,MAAM;QACrB;MACF;MAEA,OAAO,IAAI,CAACA,KAAK,KAAK,OAAO;IAC/B;EACF,CAAC,EAAE;IACDtD,GAAG,EAAE,MAAM;IACX3B,KAAK,EAAE,SAASR,IAAIA,CAAA,EAAG;MACrB;MACA,IAAI,CAAC,IAAI,CAAC2H,OAAO,CAAC,CAAC,EAAE;QACnB,MAAM,IAAIE,KAAK,CAAC,iBAAiB,CAAC;MACpC,CAAC,CAAC;;MAGF,IAAIpB,MAAM,GAAG,IAAI,CAACmB,SAAS;MAC3B,IAAI,CAACA,SAAS,GAAG,IAAI;MACrB,IAAI,CAACnC,KAAK,GAAG,WAAW;MACxB,OAAOgB,MAAM;IACf;EACF,CAAC,CAAC,CAAC;EAEH,OAAO5B,kBAAkB;AAC3B,CAAC,CAAC,CAAC;AAEH,SAASA,kBAAkB,IAAIiD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}