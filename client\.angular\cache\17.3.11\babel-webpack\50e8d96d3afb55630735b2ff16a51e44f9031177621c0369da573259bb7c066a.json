{"ast": null, "code": "import extractPhoneContext, { isPhoneContextValid, PLUS_SIGN, RFC3966_PREFIX_, RFC3966_PHONE_CONTEXT_, RFC3966_ISDN_SUBADDRESS_ } from './extractPhoneContext.js';\nimport ParseError from '../ParseError.js';\n/**\r\n * @param  {string} numberToParse\r\n * @param  {string} nationalNumber\r\n * @return {}\r\n */\n\nexport default function extractFormattedPhoneNumberFromPossibleRfc3966NumberUri(numberToParse, _ref) {\n  var extractFormattedPhoneNumber = _ref.extractFormattedPhoneNumber;\n  var phoneContext = extractPhoneContext(numberToParse);\n  if (!isPhoneContextValid(phoneContext)) {\n    throw new ParseError('NOT_A_NUMBER');\n  }\n  var phoneNumberString;\n  if (phoneContext === null) {\n    // Extract a possible number from the string passed in.\n    // (this strips leading characters that could not be the start of a phone number)\n    phoneNumberString = extractFormattedPhoneNumber(numberToParse) || '';\n  } else {\n    phoneNumberString = ''; // If the phone context contains a phone number prefix, we need to capture\n    // it, whereas domains will be ignored.\n\n    if (phoneContext.charAt(0) === PLUS_SIGN) {\n      phoneNumberString += phoneContext;\n    } // Now append everything between the \"tel:\" prefix and the phone-context.\n    // This should include the national number, an optional extension or\n    // isdn-subaddress component. Note we also handle the case when \"tel:\" is\n    // missing, as we have seen in some of the phone number inputs.\n    // In that case, we append everything from the beginning.\n\n    var indexOfRfc3966Prefix = numberToParse.indexOf(RFC3966_PREFIX_);\n    var indexOfNationalNumber; // RFC 3966 \"tel:\" prefix is preset at this stage because\n    // `isPhoneContextValid()` requires it to be present.\n\n    /* istanbul ignore else */\n\n    if (indexOfRfc3966Prefix >= 0) {\n      indexOfNationalNumber = indexOfRfc3966Prefix + RFC3966_PREFIX_.length;\n    } else {\n      indexOfNationalNumber = 0;\n    }\n    var indexOfPhoneContext = numberToParse.indexOf(RFC3966_PHONE_CONTEXT_);\n    phoneNumberString += numberToParse.substring(indexOfNationalNumber, indexOfPhoneContext);\n  } // Delete the isdn-subaddress and everything after it if it is present.\n  // Note extension won't appear at the same time with isdn-subaddress\n  // according to paragraph 5.3 of the RFC3966 spec.\n\n  var indexOfIsdn = phoneNumberString.indexOf(RFC3966_ISDN_SUBADDRESS_);\n  if (indexOfIsdn > 0) {\n    phoneNumberString = phoneNumberString.substring(0, indexOfIsdn);\n  } // If both phone context and isdn-subaddress are absent but other\n  // parameters are present, the parameters are left in nationalNumber.\n  // This is because we are concerned about deleting content from a potential\n  // number string when there is no strong evidence that the number is\n  // actually written in RFC3966.\n\n  if (phoneNumberString !== '') {\n    return phoneNumberString;\n  }\n}", "map": {"version": 3, "names": ["extractPhoneContext", "isPhoneContextValid", "PLUS_SIGN", "RFC3966_PREFIX_", "RFC3966_PHONE_CONTEXT_", "RFC3966_ISDN_SUBADDRESS_", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "extractFormattedPhoneNumberFromPossibleRfc3966NumberUri", "numberToParse", "_ref", "extractFormattedPhoneNumber", "phoneContext", "phoneNumberString", "char<PERSON>t", "indexOfRfc3966Prefix", "indexOf", "indexOfNationalNumber", "length", "indexOfPhoneContext", "substring", "indexOfIsdn"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/helpers/extractFormattedPhoneNumberFromPossibleRfc3966NumberUri.js"], "sourcesContent": ["import extractPhoneContext, { isPhoneContextValid, PLUS_SIGN, RFC3966_PREFIX_, RFC3966_PHONE_CONTEXT_, RFC3966_ISDN_SUBADDRESS_ } from './extractPhoneContext.js';\nimport ParseError from '../ParseError.js';\n/**\r\n * @param  {string} numberToParse\r\n * @param  {string} nationalNumber\r\n * @return {}\r\n */\n\nexport default function extractFormattedPhoneNumberFromPossibleRfc3966NumberUri(numberToParse, _ref) {\n  var extractFormattedPhoneNumber = _ref.extractFormattedPhoneNumber;\n  var phoneContext = extractPhoneContext(numberToParse);\n\n  if (!isPhoneContextValid(phoneContext)) {\n    throw new ParseError('NOT_A_NUMBER');\n  }\n\n  var phoneNumberString;\n\n  if (phoneContext === null) {\n    // Extract a possible number from the string passed in.\n    // (this strips leading characters that could not be the start of a phone number)\n    phoneNumberString = extractFormattedPhoneNumber(numberToParse) || '';\n  } else {\n    phoneNumberString = ''; // If the phone context contains a phone number prefix, we need to capture\n    // it, whereas domains will be ignored.\n\n    if (phoneContext.charAt(0) === PLUS_SIGN) {\n      phoneNumberString += phoneContext;\n    } // Now append everything between the \"tel:\" prefix and the phone-context.\n    // This should include the national number, an optional extension or\n    // isdn-subaddress component. Note we also handle the case when \"tel:\" is\n    // missing, as we have seen in some of the phone number inputs.\n    // In that case, we append everything from the beginning.\n\n\n    var indexOfRfc3966Prefix = numberToParse.indexOf(RFC3966_PREFIX_);\n    var indexOfNationalNumber; // RFC 3966 \"tel:\" prefix is preset at this stage because\n    // `isPhoneContextValid()` requires it to be present.\n\n    /* istanbul ignore else */\n\n    if (indexOfRfc3966Prefix >= 0) {\n      indexOfNationalNumber = indexOfRfc3966Prefix + RFC3966_PREFIX_.length;\n    } else {\n      indexOfNationalNumber = 0;\n    }\n\n    var indexOfPhoneContext = numberToParse.indexOf(RFC3966_PHONE_CONTEXT_);\n    phoneNumberString += numberToParse.substring(indexOfNationalNumber, indexOfPhoneContext);\n  } // Delete the isdn-subaddress and everything after it if it is present.\n  // Note extension won't appear at the same time with isdn-subaddress\n  // according to paragraph 5.3 of the RFC3966 spec.\n\n\n  var indexOfIsdn = phoneNumberString.indexOf(RFC3966_ISDN_SUBADDRESS_);\n\n  if (indexOfIsdn > 0) {\n    phoneNumberString = phoneNumberString.substring(0, indexOfIsdn);\n  } // If both phone context and isdn-subaddress are absent but other\n  // parameters are present, the parameters are left in nationalNumber.\n  // This is because we are concerned about deleting content from a potential\n  // number string when there is no strong evidence that the number is\n  // actually written in RFC3966.\n\n\n  if (phoneNumberString !== '') {\n    return phoneNumberString;\n  }\n}\n"], "mappings": "AAAA,OAAOA,mBAAmB,IAAIC,mBAAmB,EAAEC,SAAS,EAAEC,eAAe,EAAEC,sBAAsB,EAAEC,wBAAwB,QAAQ,0BAA0B;AACjK,OAAOC,UAAU,MAAM,kBAAkB;AACzC;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,uDAAuDA,CAACC,aAAa,EAAEC,IAAI,EAAE;EACnG,IAAIC,2BAA2B,GAAGD,IAAI,CAACC,2BAA2B;EAClE,IAAIC,YAAY,GAAGX,mBAAmB,CAACQ,aAAa,CAAC;EAErD,IAAI,CAACP,mBAAmB,CAACU,YAAY,CAAC,EAAE;IACtC,MAAM,IAAIL,UAAU,CAAC,cAAc,CAAC;EACtC;EAEA,IAAIM,iBAAiB;EAErB,IAAID,YAAY,KAAK,IAAI,EAAE;IACzB;IACA;IACAC,iBAAiB,GAAGF,2BAA2B,CAACF,aAAa,CAAC,IAAI,EAAE;EACtE,CAAC,MAAM;IACLI,iBAAiB,GAAG,EAAE,CAAC,CAAC;IACxB;;IAEA,IAAID,YAAY,CAACE,MAAM,CAAC,CAAC,CAAC,KAAKX,SAAS,EAAE;MACxCU,iBAAiB,IAAID,YAAY;IACnC,CAAC,CAAC;IACF;IACA;IACA;IACA;;IAGA,IAAIG,oBAAoB,GAAGN,aAAa,CAACO,OAAO,CAACZ,eAAe,CAAC;IACjE,IAAIa,qBAAqB,CAAC,CAAC;IAC3B;;IAEA;;IAEA,IAAIF,oBAAoB,IAAI,CAAC,EAAE;MAC7BE,qBAAqB,GAAGF,oBAAoB,GAAGX,eAAe,CAACc,MAAM;IACvE,CAAC,MAAM;MACLD,qBAAqB,GAAG,CAAC;IAC3B;IAEA,IAAIE,mBAAmB,GAAGV,aAAa,CAACO,OAAO,CAACX,sBAAsB,CAAC;IACvEQ,iBAAiB,IAAIJ,aAAa,CAACW,SAAS,CAACH,qBAAqB,EAAEE,mBAAmB,CAAC;EAC1F,CAAC,CAAC;EACF;EACA;;EAGA,IAAIE,WAAW,GAAGR,iBAAiB,CAACG,OAAO,CAACV,wBAAwB,CAAC;EAErE,IAAIe,WAAW,GAAG,CAAC,EAAE;IACnBR,iBAAiB,GAAGA,iBAAiB,CAACO,SAAS,CAAC,CAAC,EAAEC,WAAW,CAAC;EACjE,CAAC,CAAC;EACF;EACA;EACA;EACA;;EAGA,IAAIR,iBAAiB,KAAK,EAAE,EAAE;IAC5B,OAAOA,iBAAiB;EAC1B;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}