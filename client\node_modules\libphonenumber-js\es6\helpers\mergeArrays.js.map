{"version": 3, "file": "mergeArrays.js", "names": ["mergeArrays", "a", "b", "merged", "slice", "element", "indexOf", "push", "sort"], "sources": ["../../source/helpers/mergeArrays.js"], "sourcesContent": ["/**\r\n * Merges two arrays.\r\n * @param  {*} a\r\n * @param  {*} b\r\n * @return {*}\r\n */\r\nexport default function mergeArrays(a, b) {\r\n\tconst merged = a.slice()\r\n\r\n\tfor (const element of b) {\r\n\t\tif (a.indexOf(element) < 0) {\r\n\t\t\tmerged.push(element)\r\n\t\t}\r\n\t}\r\n\r\n\treturn merged.sort((a, b) => a - b)\r\n\r\n\t// ES6 version, requires Set polyfill.\r\n\t// let merged = new Set(a)\r\n\t// for (const element of b) {\r\n\t// \tmerged.add(i)\r\n\t// }\r\n\t// return Array.from(merged).sort((a, b) => a - b)\r\n}"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASA,WAAT,CAAqBC,CAArB,EAAwBC,CAAxB,EAA2B;EACzC,IAAMC,MAAM,GAAGF,CAAC,CAACG,KAAF,EAAf;;EAEA,qDAAsBF,CAAtB,wCAAyB;IAAA,IAAdG,OAAc;;IACxB,IAAIJ,CAAC,CAACK,OAAF,CAAUD,OAAV,IAAqB,CAAzB,EAA4B;MAC3BF,MAAM,CAACI,IAAP,CAAYF,OAAZ;IACA;EACD;;EAED,OAAOF,MAAM,CAACK,IAAP,CAAY,UAACP,CAAD,EAAIC,CAAJ;IAAA,OAAUD,CAAC,GAAGC,CAAd;EAAA,CAAZ,CAAP,CATyC,CAWzC;EACA;EACA;EACA;EACA;EACA;AACA"}