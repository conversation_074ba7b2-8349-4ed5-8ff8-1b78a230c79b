{"ast": null, "code": "function _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nimport compare from './tools/semver-compare.js';\nimport isObject from './helpers/isObject.js'; // Added \"possibleLengths\" and renamed\n// \"country_phone_code_to_countries\" to \"country_calling_codes\".\n\nvar V2 = '1.0.18'; // Added \"idd_prefix\" and \"default_idd_prefix\".\n\nvar V3 = '1.2.0'; // Moved `001` country code to \"nonGeographic\" section of metadata.\n\nvar V4 = '1.7.35';\nvar DEFAULT_EXT_PREFIX = ' ext. ';\nvar CALLING_CODE_REG_EXP = /^\\d+$/;\n/**\r\n * See: https://gitlab.com/catamphetamine/libphonenumber-js/blob/master/METADATA.md\r\n */\n\nvar Metadata = /*#__PURE__*/function () {\n  function Metadata(metadata) {\n    _classCallCheck(this, Metadata);\n    validateMetadata(metadata);\n    this.metadata = metadata;\n    setVersion.call(this, metadata);\n  }\n  _createClass(Metadata, [{\n    key: \"getCountries\",\n    value: function getCountries() {\n      return Object.keys(this.metadata.countries).filter(function (_) {\n        return _ !== '001';\n      });\n    }\n  }, {\n    key: \"getCountryMetadata\",\n    value: function getCountryMetadata(countryCode) {\n      return this.metadata.countries[countryCode];\n    }\n  }, {\n    key: \"nonGeographic\",\n    value: function nonGeographic() {\n      if (this.v1 || this.v2 || this.v3) return; // `nonGeographical` was a typo.\n      // It's present in metadata generated from `1.7.35` to `1.7.37`.\n      // The test case could be found by searching for \"nonGeographical\".\n\n      return this.metadata.nonGeographic || this.metadata.nonGeographical;\n    }\n  }, {\n    key: \"hasCountry\",\n    value: function hasCountry(country) {\n      return this.getCountryMetadata(country) !== undefined;\n    }\n  }, {\n    key: \"hasCallingCode\",\n    value: function hasCallingCode(callingCode) {\n      if (this.getCountryCodesForCallingCode(callingCode)) {\n        return true;\n      }\n      if (this.nonGeographic()) {\n        if (this.nonGeographic()[callingCode]) {\n          return true;\n        }\n      } else {\n        // A hacky workaround for old custom metadata (generated before V4).\n        var countryCodes = this.countryCallingCodes()[callingCode];\n        if (countryCodes && countryCodes.length === 1 && countryCodes[0] === '001') {\n          return true;\n        }\n      }\n    }\n  }, {\n    key: \"isNonGeographicCallingCode\",\n    value: function isNonGeographicCallingCode(callingCode) {\n      if (this.nonGeographic()) {\n        return this.nonGeographic()[callingCode] ? true : false;\n      } else {\n        return this.getCountryCodesForCallingCode(callingCode) ? false : true;\n      }\n    } // Deprecated.\n  }, {\n    key: \"country\",\n    value: function country(countryCode) {\n      return this.selectNumberingPlan(countryCode);\n    }\n  }, {\n    key: \"selectNumberingPlan\",\n    value: function selectNumberingPlan(countryCode, callingCode) {\n      // Supports just passing `callingCode` as the first argument.\n      if (countryCode && CALLING_CODE_REG_EXP.test(countryCode)) {\n        callingCode = countryCode;\n        countryCode = null;\n      }\n      if (countryCode && countryCode !== '001') {\n        if (!this.hasCountry(countryCode)) {\n          throw new Error(\"Unknown country: \".concat(countryCode));\n        }\n        this.numberingPlan = new NumberingPlan(this.getCountryMetadata(countryCode), this);\n      } else if (callingCode) {\n        if (!this.hasCallingCode(callingCode)) {\n          throw new Error(\"Unknown calling code: \".concat(callingCode));\n        }\n        this.numberingPlan = new NumberingPlan(this.getNumberingPlanMetadata(callingCode), this);\n      } else {\n        this.numberingPlan = undefined;\n      }\n      return this;\n    }\n  }, {\n    key: \"getCountryCodesForCallingCode\",\n    value: function getCountryCodesForCallingCode(callingCode) {\n      var countryCodes = this.countryCallingCodes()[callingCode];\n      if (countryCodes) {\n        // Metadata before V4 included \"non-geographic entity\" calling codes\n        // inside `country_calling_codes` (for example, `\"881\":[\"001\"]`).\n        // Now the semantics of `country_calling_codes` has changed:\n        // it's specifically for \"countries\" now.\n        // Older versions of custom metadata will simply skip parsing\n        // \"non-geographic entity\" phone numbers with new versions\n        // of this library: it's not considered a bug,\n        // because such numbers are extremely rare,\n        // and developers extremely rarely use custom metadata.\n        if (countryCodes.length === 1 && countryCodes[0].length === 3) {\n          return;\n        }\n        return countryCodes;\n      }\n    }\n  }, {\n    key: \"getCountryCodeForCallingCode\",\n    value: function getCountryCodeForCallingCode(callingCode) {\n      var countryCodes = this.getCountryCodesForCallingCode(callingCode);\n      if (countryCodes) {\n        return countryCodes[0];\n      }\n    }\n  }, {\n    key: \"getNumberingPlanMetadata\",\n    value: function getNumberingPlanMetadata(callingCode) {\n      var countryCode = this.getCountryCodeForCallingCode(callingCode);\n      if (countryCode) {\n        return this.getCountryMetadata(countryCode);\n      }\n      if (this.nonGeographic()) {\n        var metadata = this.nonGeographic()[callingCode];\n        if (metadata) {\n          return metadata;\n        }\n      } else {\n        // A hacky workaround for old custom metadata (generated before V4).\n        // In that metadata, there was no concept of \"non-geographic\" metadata\n        // so metadata for `001` country code was stored along with other countries.\n        // The test case can be found by searching for:\n        // \"should work around `nonGeographic` metadata not existing\".\n        var countryCodes = this.countryCallingCodes()[callingCode];\n        if (countryCodes && countryCodes.length === 1 && countryCodes[0] === '001') {\n          return this.metadata.countries['001'];\n        }\n      }\n    } // Deprecated.\n  }, {\n    key: \"countryCallingCode\",\n    value: function countryCallingCode() {\n      return this.numberingPlan.callingCode();\n    } // Deprecated.\n  }, {\n    key: \"IDDPrefix\",\n    value: function IDDPrefix() {\n      return this.numberingPlan.IDDPrefix();\n    } // Deprecated.\n  }, {\n    key: \"defaultIDDPrefix\",\n    value: function defaultIDDPrefix() {\n      return this.numberingPlan.defaultIDDPrefix();\n    } // Deprecated.\n  }, {\n    key: \"nationalNumberPattern\",\n    value: function nationalNumberPattern() {\n      return this.numberingPlan.nationalNumberPattern();\n    } // Deprecated.\n  }, {\n    key: \"possibleLengths\",\n    value: function possibleLengths() {\n      return this.numberingPlan.possibleLengths();\n    } // Deprecated.\n  }, {\n    key: \"formats\",\n    value: function formats() {\n      return this.numberingPlan.formats();\n    } // Deprecated.\n  }, {\n    key: \"nationalPrefixForParsing\",\n    value: function nationalPrefixForParsing() {\n      return this.numberingPlan.nationalPrefixForParsing();\n    } // Deprecated.\n  }, {\n    key: \"nationalPrefixTransformRule\",\n    value: function nationalPrefixTransformRule() {\n      return this.numberingPlan.nationalPrefixTransformRule();\n    } // Deprecated.\n  }, {\n    key: \"leadingDigits\",\n    value: function leadingDigits() {\n      return this.numberingPlan.leadingDigits();\n    } // Deprecated.\n  }, {\n    key: \"hasTypes\",\n    value: function hasTypes() {\n      return this.numberingPlan.hasTypes();\n    } // Deprecated.\n  }, {\n    key: \"type\",\n    value: function type(_type) {\n      return this.numberingPlan.type(_type);\n    } // Deprecated.\n  }, {\n    key: \"ext\",\n    value: function ext() {\n      return this.numberingPlan.ext();\n    }\n  }, {\n    key: \"countryCallingCodes\",\n    value: function countryCallingCodes() {\n      if (this.v1) return this.metadata.country_phone_code_to_countries;\n      return this.metadata.country_calling_codes;\n    } // Deprecated.\n  }, {\n    key: \"chooseCountryByCountryCallingCode\",\n    value: function chooseCountryByCountryCallingCode(callingCode) {\n      return this.selectNumberingPlan(callingCode);\n    }\n  }, {\n    key: \"hasSelectedNumberingPlan\",\n    value: function hasSelectedNumberingPlan() {\n      return this.numberingPlan !== undefined;\n    }\n  }]);\n  return Metadata;\n}();\nexport { Metadata as default };\nvar NumberingPlan = /*#__PURE__*/function () {\n  function NumberingPlan(metadata, globalMetadataObject) {\n    _classCallCheck(this, NumberingPlan);\n    this.globalMetadataObject = globalMetadataObject;\n    this.metadata = metadata;\n    setVersion.call(this, globalMetadataObject.metadata);\n  }\n  _createClass(NumberingPlan, [{\n    key: \"callingCode\",\n    value: function callingCode() {\n      return this.metadata[0];\n    } // Formatting information for regions which share\n    // a country calling code is contained by only one region\n    // for performance reasons. For example, for NANPA region\n    // (\"North American Numbering Plan Administration\",\n    //  which includes USA, Canada, Cayman Islands, Bahamas, etc)\n    // it will be contained in the metadata for `US`.\n  }, {\n    key: \"getDefaultCountryMetadataForRegion\",\n    value: function getDefaultCountryMetadataForRegion() {\n      return this.globalMetadataObject.getNumberingPlanMetadata(this.callingCode());\n    } // Is always present.\n  }, {\n    key: \"IDDPrefix\",\n    value: function IDDPrefix() {\n      if (this.v1 || this.v2) return;\n      return this.metadata[1];\n    } // Is only present when a country supports multiple IDD prefixes.\n  }, {\n    key: \"defaultIDDPrefix\",\n    value: function defaultIDDPrefix() {\n      if (this.v1 || this.v2) return;\n      return this.metadata[12];\n    }\n  }, {\n    key: \"nationalNumberPattern\",\n    value: function nationalNumberPattern() {\n      if (this.v1 || this.v2) return this.metadata[1];\n      return this.metadata[2];\n    } // \"possible length\" data is always present in Google's metadata.\n  }, {\n    key: \"possibleLengths\",\n    value: function possibleLengths() {\n      if (this.v1) return;\n      return this.metadata[this.v2 ? 2 : 3];\n    }\n  }, {\n    key: \"_getFormats\",\n    value: function _getFormats(metadata) {\n      return metadata[this.v1 ? 2 : this.v2 ? 3 : 4];\n    } // For countries of the same region (e.g. NANPA)\n    // formats are all stored in the \"main\" country for that region.\n    // E.g. \"RU\" and \"KZ\", \"US\" and \"CA\".\n  }, {\n    key: \"formats\",\n    value: function formats() {\n      var _this = this;\n      var formats = this._getFormats(this.metadata) || this._getFormats(this.getDefaultCountryMetadataForRegion()) || [];\n      return formats.map(function (_) {\n        return new Format(_, _this);\n      });\n    }\n  }, {\n    key: \"nationalPrefix\",\n    value: function nationalPrefix() {\n      return this.metadata[this.v1 ? 3 : this.v2 ? 4 : 5];\n    }\n  }, {\n    key: \"_getNationalPrefixFormattingRule\",\n    value: function _getNationalPrefixFormattingRule(metadata) {\n      return metadata[this.v1 ? 4 : this.v2 ? 5 : 6];\n    } // For countries of the same region (e.g. NANPA)\n    // national prefix formatting rule is stored in the \"main\" country for that region.\n    // E.g. \"RU\" and \"KZ\", \"US\" and \"CA\".\n  }, {\n    key: \"nationalPrefixFormattingRule\",\n    value: function nationalPrefixFormattingRule() {\n      return this._getNationalPrefixFormattingRule(this.metadata) || this._getNationalPrefixFormattingRule(this.getDefaultCountryMetadataForRegion());\n    }\n  }, {\n    key: \"_nationalPrefixForParsing\",\n    value: function _nationalPrefixForParsing() {\n      return this.metadata[this.v1 ? 5 : this.v2 ? 6 : 7];\n    }\n  }, {\n    key: \"nationalPrefixForParsing\",\n    value: function nationalPrefixForParsing() {\n      // If `national_prefix_for_parsing` is not set explicitly,\n      // then infer it from `national_prefix` (if any)\n      return this._nationalPrefixForParsing() || this.nationalPrefix();\n    }\n  }, {\n    key: \"nationalPrefixTransformRule\",\n    value: function nationalPrefixTransformRule() {\n      return this.metadata[this.v1 ? 6 : this.v2 ? 7 : 8];\n    }\n  }, {\n    key: \"_getNationalPrefixIsOptionalWhenFormatting\",\n    value: function _getNationalPrefixIsOptionalWhenFormatting() {\n      return !!this.metadata[this.v1 ? 7 : this.v2 ? 8 : 9];\n    } // For countries of the same region (e.g. NANPA)\n    // \"national prefix is optional when formatting\" flag is\n    // stored in the \"main\" country for that region.\n    // E.g. \"RU\" and \"KZ\", \"US\" and \"CA\".\n  }, {\n    key: \"nationalPrefixIsOptionalWhenFormattingInNationalFormat\",\n    value: function nationalPrefixIsOptionalWhenFormattingInNationalFormat() {\n      return this._getNationalPrefixIsOptionalWhenFormatting(this.metadata) || this._getNationalPrefixIsOptionalWhenFormatting(this.getDefaultCountryMetadataForRegion());\n    }\n  }, {\n    key: \"leadingDigits\",\n    value: function leadingDigits() {\n      return this.metadata[this.v1 ? 8 : this.v2 ? 9 : 10];\n    }\n  }, {\n    key: \"types\",\n    value: function types() {\n      return this.metadata[this.v1 ? 9 : this.v2 ? 10 : 11];\n    }\n  }, {\n    key: \"hasTypes\",\n    value: function hasTypes() {\n      // Versions 1.2.0 - 1.2.4: can be `[]`.\n\n      /* istanbul ignore next */\n      if (this.types() && this.types().length === 0) {\n        return false;\n      } // Versions <= 1.2.4: can be `undefined`.\n      // Version >= 1.2.5: can be `0`.\n\n      return !!this.types();\n    }\n  }, {\n    key: \"type\",\n    value: function type(_type2) {\n      if (this.hasTypes() && getType(this.types(), _type2)) {\n        return new Type(getType(this.types(), _type2), this);\n      }\n    }\n  }, {\n    key: \"ext\",\n    value: function ext() {\n      if (this.v1 || this.v2) return DEFAULT_EXT_PREFIX;\n      return this.metadata[13] || DEFAULT_EXT_PREFIX;\n    }\n  }]);\n  return NumberingPlan;\n}();\nvar Format = /*#__PURE__*/function () {\n  function Format(format, metadata) {\n    _classCallCheck(this, Format);\n    this._format = format;\n    this.metadata = metadata;\n  }\n  _createClass(Format, [{\n    key: \"pattern\",\n    value: function pattern() {\n      return this._format[0];\n    }\n  }, {\n    key: \"format\",\n    value: function format() {\n      return this._format[1];\n    }\n  }, {\n    key: \"leadingDigitsPatterns\",\n    value: function leadingDigitsPatterns() {\n      return this._format[2] || [];\n    }\n  }, {\n    key: \"nationalPrefixFormattingRule\",\n    value: function nationalPrefixFormattingRule() {\n      return this._format[3] || this.metadata.nationalPrefixFormattingRule();\n    }\n  }, {\n    key: \"nationalPrefixIsOptionalWhenFormattingInNationalFormat\",\n    value: function nationalPrefixIsOptionalWhenFormattingInNationalFormat() {\n      return !!this._format[4] || this.metadata.nationalPrefixIsOptionalWhenFormattingInNationalFormat();\n    }\n  }, {\n    key: \"nationalPrefixIsMandatoryWhenFormattingInNationalFormat\",\n    value: function nationalPrefixIsMandatoryWhenFormattingInNationalFormat() {\n      // National prefix is omitted if there's no national prefix formatting rule\n      // set for this country, or when the national prefix formatting rule\n      // contains no national prefix itself, or when this rule is set but\n      // national prefix is optional for this phone number format\n      // (and it is not enforced explicitly)\n      return this.usesNationalPrefix() && !this.nationalPrefixIsOptionalWhenFormattingInNationalFormat();\n    } // Checks whether national prefix formatting rule contains national prefix.\n  }, {\n    key: \"usesNationalPrefix\",\n    value: function usesNationalPrefix() {\n      return this.nationalPrefixFormattingRule() &&\n      // Check that national prefix formatting rule is not a \"dummy\" one.\n      !FIRST_GROUP_ONLY_PREFIX_PATTERN.test(this.nationalPrefixFormattingRule()) // In compressed metadata, `this.nationalPrefixFormattingRule()` is `0`\n      // when `national_prefix_formatting_rule` is not present.\n      // So, `true` or `false` are returned explicitly here, so that\n      // `0` number isn't returned.\n      ? true : false;\n    }\n  }, {\n    key: \"internationalFormat\",\n    value: function internationalFormat() {\n      return this._format[5] || this.format();\n    }\n  }]);\n  return Format;\n}();\n/**\r\n * A pattern that is used to determine if the national prefix formatting rule\r\n * has the first group only, i.e., does not start with the national prefix.\r\n * Note that the pattern explicitly allows for unbalanced parentheses.\r\n */\n\nvar FIRST_GROUP_ONLY_PREFIX_PATTERN = /^\\(?\\$1\\)?$/;\nvar Type = /*#__PURE__*/function () {\n  function Type(type, metadata) {\n    _classCallCheck(this, Type);\n    this.type = type;\n    this.metadata = metadata;\n  }\n  _createClass(Type, [{\n    key: \"pattern\",\n    value: function pattern() {\n      if (this.metadata.v1) return this.type;\n      return this.type[0];\n    }\n  }, {\n    key: \"possibleLengths\",\n    value: function possibleLengths() {\n      if (this.metadata.v1) return;\n      return this.type[1] || this.metadata.possibleLengths();\n    }\n  }]);\n  return Type;\n}();\nfunction getType(types, type) {\n  switch (type) {\n    case 'FIXED_LINE':\n      return types[0];\n    case 'MOBILE':\n      return types[1];\n    case 'TOLL_FREE':\n      return types[2];\n    case 'PREMIUM_RATE':\n      return types[3];\n    case 'PERSONAL_NUMBER':\n      return types[4];\n    case 'VOICEMAIL':\n      return types[5];\n    case 'UAN':\n      return types[6];\n    case 'PAGER':\n      return types[7];\n    case 'VOIP':\n      return types[8];\n    case 'SHARED_COST':\n      return types[9];\n  }\n}\nexport function validateMetadata(metadata) {\n  if (!metadata) {\n    throw new Error('[libphonenumber-js] `metadata` argument not passed. Check your arguments.');\n  } // `country_phone_code_to_countries` was renamed to `country_calling_codes` in `1.0.18`.\n  // For that reason, it's not used in this detection algorithm.\n  // Instead, it detects by `countries: {}` property existence.\n\n  if (!isObject(metadata) || !isObject(metadata.countries)) {\n    throw new Error(\"[libphonenumber-js] `metadata` argument was passed but it's not a valid metadata. Must be an object having `.countries` child object property. Got \".concat(isObject(metadata) ? 'an object of shape: { ' + Object.keys(metadata).join(', ') + ' }' : 'a ' + typeOf(metadata) + ': ' + metadata, \".\"));\n  }\n} // Babel transforms `typeof` into some \"branches\"\n// so istanbul will show this as \"branch not covered\".\n\n/* istanbul ignore next */\n\nvar typeOf = function typeOf(_) {\n  return _typeof(_);\n};\n/**\r\n * Returns extension prefix for a country.\r\n * @param  {string} country\r\n * @param  {object} metadata\r\n * @return {string?}\r\n * @example\r\n * // Returns \" ext. \"\r\n * getExtPrefix(\"US\")\r\n */\n\nexport function getExtPrefix(country, metadata) {\n  metadata = new Metadata(metadata);\n  if (metadata.hasCountry(country)) {\n    return metadata.country(country).ext();\n  }\n  return DEFAULT_EXT_PREFIX;\n}\n/**\r\n * Returns \"country calling code\" for a country.\r\n * Throws an error if the country doesn't exist or isn't supported by this library.\r\n * @param  {string} country\r\n * @param  {object} metadata\r\n * @return {string}\r\n * @example\r\n * // Returns \"44\"\r\n * getCountryCallingCode(\"GB\")\r\n */\n\nexport function getCountryCallingCode(country, metadata) {\n  metadata = new Metadata(metadata);\n  if (metadata.hasCountry(country)) {\n    return metadata.country(country).countryCallingCode();\n  }\n  throw new Error(\"Unknown country: \".concat(country));\n}\nexport function isSupportedCountry(country, metadata) {\n  // metadata = new Metadata(metadata)\n  // return metadata.hasCountry(country)\n  return metadata.countries.hasOwnProperty(country);\n}\nfunction setVersion(metadata) {\n  var version = metadata.version;\n  if (typeof version === 'number') {\n    this.v1 = version === 1;\n    this.v2 = version === 2;\n    this.v3 = version === 3;\n    this.v4 = version === 4;\n  } else {\n    if (!version) {\n      this.v1 = true;\n    } else if (compare(version, V3) === -1) {\n      this.v2 = true;\n    } else if (compare(version, V4) === -1) {\n      this.v3 = true;\n    } else {\n      this.v4 = true;\n    }\n  }\n} // const ISO_COUNTRY_CODE = /^[A-Z]{2}$/\n// function isCountryCode(countryCode) {\n// \treturn ISO_COUNTRY_CODE.test(countryCodeOrCountryCallingCode)\n// }", "map": {"version": 3, "names": ["_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "Object", "defineProperty", "key", "_createClass", "protoProps", "staticProps", "compare", "isObject", "V2", "V3", "V4", "DEFAULT_EXT_PREFIX", "CALLING_CODE_REG_EXP", "<PERSON><PERSON><PERSON>", "metadata", "validateMetadata", "setVersion", "call", "value", "getCountries", "keys", "countries", "filter", "_", "getCountryMetadata", "countryCode", "nonGeographic", "v1", "v2", "v3", "nonGeographical", "hasCountry", "country", "undefined", "hasCallingCode", "callingCode", "getCountryCodesForCallingCode", "countryCodes", "countryCallingCodes", "isNonGeographicCallingCode", "selectNumberingPlan", "test", "Error", "concat", "numberingPlan", "NumberingPlan", "getNumberingPlanMetadata", "getCountryCodeForCallingCode", "countryCallingCode", "IDDPrefix", "defaultIDDPrefix", "nationalNumberPattern", "possibleLengths", "formats", "nationalPrefixForParsing", "nationalPrefixTransformRule", "leadingDigits", "hasTypes", "type", "_type", "ext", "country_phone_code_to_countries", "country_calling_codes", "chooseCountryByCountryCallingCode", "hasSelectedNumberingPlan", "default", "globalMetadataObject", "getDefaultCountryMetadataForRegion", "_getFormats", "_this", "map", "Format", "nationalPrefix", "_getNationalPrefixFormattingRule", "nationalPrefixFormattingRule", "_nationalPrefixForParsing", "_getNationalPrefixIsOptionalWhenFormatting", "nationalPrefixIsOptionalWhenFormattingInNationalFormat", "types", "_type2", "getType", "Type", "format", "_format", "pattern", "leadingDigitsPatterns", "nationalPrefixIsMandatoryWhenFormattingInNationalFormat", "usesNationalPrefix", "FIRST_GROUP_ONLY_PREFIX_PATTERN", "internationalFormat", "join", "typeOf", "getExtPrefix", "getCountryCallingCode", "isSupportedCountry", "hasOwnProperty", "version", "v4"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/metadata.js"], "sourcesContent": ["function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\n\nimport compare from './tools/semver-compare.js';\nimport isObject from './helpers/isObject.js'; // Added \"possibleLengths\" and renamed\n// \"country_phone_code_to_countries\" to \"country_calling_codes\".\n\nvar V2 = '1.0.18'; // Added \"idd_prefix\" and \"default_idd_prefix\".\n\nvar V3 = '1.2.0'; // Moved `001` country code to \"nonGeographic\" section of metadata.\n\nvar V4 = '1.7.35';\nvar DEFAULT_EXT_PREFIX = ' ext. ';\nvar CALLING_CODE_REG_EXP = /^\\d+$/;\n/**\r\n * See: https://gitlab.com/catamphetamine/libphonenumber-js/blob/master/METADATA.md\r\n */\n\nvar Metadata = /*#__PURE__*/function () {\n  function Metadata(metadata) {\n    _classCallCheck(this, Metadata);\n\n    validateMetadata(metadata);\n    this.metadata = metadata;\n    setVersion.call(this, metadata);\n  }\n\n  _createClass(Metadata, [{\n    key: \"getCountries\",\n    value: function getCountries() {\n      return Object.keys(this.metadata.countries).filter(function (_) {\n        return _ !== '001';\n      });\n    }\n  }, {\n    key: \"getCountryMetadata\",\n    value: function getCountryMetadata(countryCode) {\n      return this.metadata.countries[countryCode];\n    }\n  }, {\n    key: \"nonGeographic\",\n    value: function nonGeographic() {\n      if (this.v1 || this.v2 || this.v3) return; // `nonGeographical` was a typo.\n      // It's present in metadata generated from `1.7.35` to `1.7.37`.\n      // The test case could be found by searching for \"nonGeographical\".\n\n      return this.metadata.nonGeographic || this.metadata.nonGeographical;\n    }\n  }, {\n    key: \"hasCountry\",\n    value: function hasCountry(country) {\n      return this.getCountryMetadata(country) !== undefined;\n    }\n  }, {\n    key: \"hasCallingCode\",\n    value: function hasCallingCode(callingCode) {\n      if (this.getCountryCodesForCallingCode(callingCode)) {\n        return true;\n      }\n\n      if (this.nonGeographic()) {\n        if (this.nonGeographic()[callingCode]) {\n          return true;\n        }\n      } else {\n        // A hacky workaround for old custom metadata (generated before V4).\n        var countryCodes = this.countryCallingCodes()[callingCode];\n\n        if (countryCodes && countryCodes.length === 1 && countryCodes[0] === '001') {\n          return true;\n        }\n      }\n    }\n  }, {\n    key: \"isNonGeographicCallingCode\",\n    value: function isNonGeographicCallingCode(callingCode) {\n      if (this.nonGeographic()) {\n        return this.nonGeographic()[callingCode] ? true : false;\n      } else {\n        return this.getCountryCodesForCallingCode(callingCode) ? false : true;\n      }\n    } // Deprecated.\n\n  }, {\n    key: \"country\",\n    value: function country(countryCode) {\n      return this.selectNumberingPlan(countryCode);\n    }\n  }, {\n    key: \"selectNumberingPlan\",\n    value: function selectNumberingPlan(countryCode, callingCode) {\n      // Supports just passing `callingCode` as the first argument.\n      if (countryCode && CALLING_CODE_REG_EXP.test(countryCode)) {\n        callingCode = countryCode;\n        countryCode = null;\n      }\n\n      if (countryCode && countryCode !== '001') {\n        if (!this.hasCountry(countryCode)) {\n          throw new Error(\"Unknown country: \".concat(countryCode));\n        }\n\n        this.numberingPlan = new NumberingPlan(this.getCountryMetadata(countryCode), this);\n      } else if (callingCode) {\n        if (!this.hasCallingCode(callingCode)) {\n          throw new Error(\"Unknown calling code: \".concat(callingCode));\n        }\n\n        this.numberingPlan = new NumberingPlan(this.getNumberingPlanMetadata(callingCode), this);\n      } else {\n        this.numberingPlan = undefined;\n      }\n\n      return this;\n    }\n  }, {\n    key: \"getCountryCodesForCallingCode\",\n    value: function getCountryCodesForCallingCode(callingCode) {\n      var countryCodes = this.countryCallingCodes()[callingCode];\n\n      if (countryCodes) {\n        // Metadata before V4 included \"non-geographic entity\" calling codes\n        // inside `country_calling_codes` (for example, `\"881\":[\"001\"]`).\n        // Now the semantics of `country_calling_codes` has changed:\n        // it's specifically for \"countries\" now.\n        // Older versions of custom metadata will simply skip parsing\n        // \"non-geographic entity\" phone numbers with new versions\n        // of this library: it's not considered a bug,\n        // because such numbers are extremely rare,\n        // and developers extremely rarely use custom metadata.\n        if (countryCodes.length === 1 && countryCodes[0].length === 3) {\n          return;\n        }\n\n        return countryCodes;\n      }\n    }\n  }, {\n    key: \"getCountryCodeForCallingCode\",\n    value: function getCountryCodeForCallingCode(callingCode) {\n      var countryCodes = this.getCountryCodesForCallingCode(callingCode);\n\n      if (countryCodes) {\n        return countryCodes[0];\n      }\n    }\n  }, {\n    key: \"getNumberingPlanMetadata\",\n    value: function getNumberingPlanMetadata(callingCode) {\n      var countryCode = this.getCountryCodeForCallingCode(callingCode);\n\n      if (countryCode) {\n        return this.getCountryMetadata(countryCode);\n      }\n\n      if (this.nonGeographic()) {\n        var metadata = this.nonGeographic()[callingCode];\n\n        if (metadata) {\n          return metadata;\n        }\n      } else {\n        // A hacky workaround for old custom metadata (generated before V4).\n        // In that metadata, there was no concept of \"non-geographic\" metadata\n        // so metadata for `001` country code was stored along with other countries.\n        // The test case can be found by searching for:\n        // \"should work around `nonGeographic` metadata not existing\".\n        var countryCodes = this.countryCallingCodes()[callingCode];\n\n        if (countryCodes && countryCodes.length === 1 && countryCodes[0] === '001') {\n          return this.metadata.countries['001'];\n        }\n      }\n    } // Deprecated.\n\n  }, {\n    key: \"countryCallingCode\",\n    value: function countryCallingCode() {\n      return this.numberingPlan.callingCode();\n    } // Deprecated.\n\n  }, {\n    key: \"IDDPrefix\",\n    value: function IDDPrefix() {\n      return this.numberingPlan.IDDPrefix();\n    } // Deprecated.\n\n  }, {\n    key: \"defaultIDDPrefix\",\n    value: function defaultIDDPrefix() {\n      return this.numberingPlan.defaultIDDPrefix();\n    } // Deprecated.\n\n  }, {\n    key: \"nationalNumberPattern\",\n    value: function nationalNumberPattern() {\n      return this.numberingPlan.nationalNumberPattern();\n    } // Deprecated.\n\n  }, {\n    key: \"possibleLengths\",\n    value: function possibleLengths() {\n      return this.numberingPlan.possibleLengths();\n    } // Deprecated.\n\n  }, {\n    key: \"formats\",\n    value: function formats() {\n      return this.numberingPlan.formats();\n    } // Deprecated.\n\n  }, {\n    key: \"nationalPrefixForParsing\",\n    value: function nationalPrefixForParsing() {\n      return this.numberingPlan.nationalPrefixForParsing();\n    } // Deprecated.\n\n  }, {\n    key: \"nationalPrefixTransformRule\",\n    value: function nationalPrefixTransformRule() {\n      return this.numberingPlan.nationalPrefixTransformRule();\n    } // Deprecated.\n\n  }, {\n    key: \"leadingDigits\",\n    value: function leadingDigits() {\n      return this.numberingPlan.leadingDigits();\n    } // Deprecated.\n\n  }, {\n    key: \"hasTypes\",\n    value: function hasTypes() {\n      return this.numberingPlan.hasTypes();\n    } // Deprecated.\n\n  }, {\n    key: \"type\",\n    value: function type(_type) {\n      return this.numberingPlan.type(_type);\n    } // Deprecated.\n\n  }, {\n    key: \"ext\",\n    value: function ext() {\n      return this.numberingPlan.ext();\n    }\n  }, {\n    key: \"countryCallingCodes\",\n    value: function countryCallingCodes() {\n      if (this.v1) return this.metadata.country_phone_code_to_countries;\n      return this.metadata.country_calling_codes;\n    } // Deprecated.\n\n  }, {\n    key: \"chooseCountryByCountryCallingCode\",\n    value: function chooseCountryByCountryCallingCode(callingCode) {\n      return this.selectNumberingPlan(callingCode);\n    }\n  }, {\n    key: \"hasSelectedNumberingPlan\",\n    value: function hasSelectedNumberingPlan() {\n      return this.numberingPlan !== undefined;\n    }\n  }]);\n\n  return Metadata;\n}();\n\nexport { Metadata as default };\n\nvar NumberingPlan = /*#__PURE__*/function () {\n  function NumberingPlan(metadata, globalMetadataObject) {\n    _classCallCheck(this, NumberingPlan);\n\n    this.globalMetadataObject = globalMetadataObject;\n    this.metadata = metadata;\n    setVersion.call(this, globalMetadataObject.metadata);\n  }\n\n  _createClass(NumberingPlan, [{\n    key: \"callingCode\",\n    value: function callingCode() {\n      return this.metadata[0];\n    } // Formatting information for regions which share\n    // a country calling code is contained by only one region\n    // for performance reasons. For example, for NANPA region\n    // (\"North American Numbering Plan Administration\",\n    //  which includes USA, Canada, Cayman Islands, Bahamas, etc)\n    // it will be contained in the metadata for `US`.\n\n  }, {\n    key: \"getDefaultCountryMetadataForRegion\",\n    value: function getDefaultCountryMetadataForRegion() {\n      return this.globalMetadataObject.getNumberingPlanMetadata(this.callingCode());\n    } // Is always present.\n\n  }, {\n    key: \"IDDPrefix\",\n    value: function IDDPrefix() {\n      if (this.v1 || this.v2) return;\n      return this.metadata[1];\n    } // Is only present when a country supports multiple IDD prefixes.\n\n  }, {\n    key: \"defaultIDDPrefix\",\n    value: function defaultIDDPrefix() {\n      if (this.v1 || this.v2) return;\n      return this.metadata[12];\n    }\n  }, {\n    key: \"nationalNumberPattern\",\n    value: function nationalNumberPattern() {\n      if (this.v1 || this.v2) return this.metadata[1];\n      return this.metadata[2];\n    } // \"possible length\" data is always present in Google's metadata.\n\n  }, {\n    key: \"possibleLengths\",\n    value: function possibleLengths() {\n      if (this.v1) return;\n      return this.metadata[this.v2 ? 2 : 3];\n    }\n  }, {\n    key: \"_getFormats\",\n    value: function _getFormats(metadata) {\n      return metadata[this.v1 ? 2 : this.v2 ? 3 : 4];\n    } // For countries of the same region (e.g. NANPA)\n    // formats are all stored in the \"main\" country for that region.\n    // E.g. \"RU\" and \"KZ\", \"US\" and \"CA\".\n\n  }, {\n    key: \"formats\",\n    value: function formats() {\n      var _this = this;\n\n      var formats = this._getFormats(this.metadata) || this._getFormats(this.getDefaultCountryMetadataForRegion()) || [];\n      return formats.map(function (_) {\n        return new Format(_, _this);\n      });\n    }\n  }, {\n    key: \"nationalPrefix\",\n    value: function nationalPrefix() {\n      return this.metadata[this.v1 ? 3 : this.v2 ? 4 : 5];\n    }\n  }, {\n    key: \"_getNationalPrefixFormattingRule\",\n    value: function _getNationalPrefixFormattingRule(metadata) {\n      return metadata[this.v1 ? 4 : this.v2 ? 5 : 6];\n    } // For countries of the same region (e.g. NANPA)\n    // national prefix formatting rule is stored in the \"main\" country for that region.\n    // E.g. \"RU\" and \"KZ\", \"US\" and \"CA\".\n\n  }, {\n    key: \"nationalPrefixFormattingRule\",\n    value: function nationalPrefixFormattingRule() {\n      return this._getNationalPrefixFormattingRule(this.metadata) || this._getNationalPrefixFormattingRule(this.getDefaultCountryMetadataForRegion());\n    }\n  }, {\n    key: \"_nationalPrefixForParsing\",\n    value: function _nationalPrefixForParsing() {\n      return this.metadata[this.v1 ? 5 : this.v2 ? 6 : 7];\n    }\n  }, {\n    key: \"nationalPrefixForParsing\",\n    value: function nationalPrefixForParsing() {\n      // If `national_prefix_for_parsing` is not set explicitly,\n      // then infer it from `national_prefix` (if any)\n      return this._nationalPrefixForParsing() || this.nationalPrefix();\n    }\n  }, {\n    key: \"nationalPrefixTransformRule\",\n    value: function nationalPrefixTransformRule() {\n      return this.metadata[this.v1 ? 6 : this.v2 ? 7 : 8];\n    }\n  }, {\n    key: \"_getNationalPrefixIsOptionalWhenFormatting\",\n    value: function _getNationalPrefixIsOptionalWhenFormatting() {\n      return !!this.metadata[this.v1 ? 7 : this.v2 ? 8 : 9];\n    } // For countries of the same region (e.g. NANPA)\n    // \"national prefix is optional when formatting\" flag is\n    // stored in the \"main\" country for that region.\n    // E.g. \"RU\" and \"KZ\", \"US\" and \"CA\".\n\n  }, {\n    key: \"nationalPrefixIsOptionalWhenFormattingInNationalFormat\",\n    value: function nationalPrefixIsOptionalWhenFormattingInNationalFormat() {\n      return this._getNationalPrefixIsOptionalWhenFormatting(this.metadata) || this._getNationalPrefixIsOptionalWhenFormatting(this.getDefaultCountryMetadataForRegion());\n    }\n  }, {\n    key: \"leadingDigits\",\n    value: function leadingDigits() {\n      return this.metadata[this.v1 ? 8 : this.v2 ? 9 : 10];\n    }\n  }, {\n    key: \"types\",\n    value: function types() {\n      return this.metadata[this.v1 ? 9 : this.v2 ? 10 : 11];\n    }\n  }, {\n    key: \"hasTypes\",\n    value: function hasTypes() {\n      // Versions 1.2.0 - 1.2.4: can be `[]`.\n\n      /* istanbul ignore next */\n      if (this.types() && this.types().length === 0) {\n        return false;\n      } // Versions <= 1.2.4: can be `undefined`.\n      // Version >= 1.2.5: can be `0`.\n\n\n      return !!this.types();\n    }\n  }, {\n    key: \"type\",\n    value: function type(_type2) {\n      if (this.hasTypes() && getType(this.types(), _type2)) {\n        return new Type(getType(this.types(), _type2), this);\n      }\n    }\n  }, {\n    key: \"ext\",\n    value: function ext() {\n      if (this.v1 || this.v2) return DEFAULT_EXT_PREFIX;\n      return this.metadata[13] || DEFAULT_EXT_PREFIX;\n    }\n  }]);\n\n  return NumberingPlan;\n}();\n\nvar Format = /*#__PURE__*/function () {\n  function Format(format, metadata) {\n    _classCallCheck(this, Format);\n\n    this._format = format;\n    this.metadata = metadata;\n  }\n\n  _createClass(Format, [{\n    key: \"pattern\",\n    value: function pattern() {\n      return this._format[0];\n    }\n  }, {\n    key: \"format\",\n    value: function format() {\n      return this._format[1];\n    }\n  }, {\n    key: \"leadingDigitsPatterns\",\n    value: function leadingDigitsPatterns() {\n      return this._format[2] || [];\n    }\n  }, {\n    key: \"nationalPrefixFormattingRule\",\n    value: function nationalPrefixFormattingRule() {\n      return this._format[3] || this.metadata.nationalPrefixFormattingRule();\n    }\n  }, {\n    key: \"nationalPrefixIsOptionalWhenFormattingInNationalFormat\",\n    value: function nationalPrefixIsOptionalWhenFormattingInNationalFormat() {\n      return !!this._format[4] || this.metadata.nationalPrefixIsOptionalWhenFormattingInNationalFormat();\n    }\n  }, {\n    key: \"nationalPrefixIsMandatoryWhenFormattingInNationalFormat\",\n    value: function nationalPrefixIsMandatoryWhenFormattingInNationalFormat() {\n      // National prefix is omitted if there's no national prefix formatting rule\n      // set for this country, or when the national prefix formatting rule\n      // contains no national prefix itself, or when this rule is set but\n      // national prefix is optional for this phone number format\n      // (and it is not enforced explicitly)\n      return this.usesNationalPrefix() && !this.nationalPrefixIsOptionalWhenFormattingInNationalFormat();\n    } // Checks whether national prefix formatting rule contains national prefix.\n\n  }, {\n    key: \"usesNationalPrefix\",\n    value: function usesNationalPrefix() {\n      return this.nationalPrefixFormattingRule() && // Check that national prefix formatting rule is not a \"dummy\" one.\n      !FIRST_GROUP_ONLY_PREFIX_PATTERN.test(this.nationalPrefixFormattingRule()) // In compressed metadata, `this.nationalPrefixFormattingRule()` is `0`\n      // when `national_prefix_formatting_rule` is not present.\n      // So, `true` or `false` are returned explicitly here, so that\n      // `0` number isn't returned.\n      ? true : false;\n    }\n  }, {\n    key: \"internationalFormat\",\n    value: function internationalFormat() {\n      return this._format[5] || this.format();\n    }\n  }]);\n\n  return Format;\n}();\n/**\r\n * A pattern that is used to determine if the national prefix formatting rule\r\n * has the first group only, i.e., does not start with the national prefix.\r\n * Note that the pattern explicitly allows for unbalanced parentheses.\r\n */\n\n\nvar FIRST_GROUP_ONLY_PREFIX_PATTERN = /^\\(?\\$1\\)?$/;\n\nvar Type = /*#__PURE__*/function () {\n  function Type(type, metadata) {\n    _classCallCheck(this, Type);\n\n    this.type = type;\n    this.metadata = metadata;\n  }\n\n  _createClass(Type, [{\n    key: \"pattern\",\n    value: function pattern() {\n      if (this.metadata.v1) return this.type;\n      return this.type[0];\n    }\n  }, {\n    key: \"possibleLengths\",\n    value: function possibleLengths() {\n      if (this.metadata.v1) return;\n      return this.type[1] || this.metadata.possibleLengths();\n    }\n  }]);\n\n  return Type;\n}();\n\nfunction getType(types, type) {\n  switch (type) {\n    case 'FIXED_LINE':\n      return types[0];\n\n    case 'MOBILE':\n      return types[1];\n\n    case 'TOLL_FREE':\n      return types[2];\n\n    case 'PREMIUM_RATE':\n      return types[3];\n\n    case 'PERSONAL_NUMBER':\n      return types[4];\n\n    case 'VOICEMAIL':\n      return types[5];\n\n    case 'UAN':\n      return types[6];\n\n    case 'PAGER':\n      return types[7];\n\n    case 'VOIP':\n      return types[8];\n\n    case 'SHARED_COST':\n      return types[9];\n  }\n}\n\nexport function validateMetadata(metadata) {\n  if (!metadata) {\n    throw new Error('[libphonenumber-js] `metadata` argument not passed. Check your arguments.');\n  } // `country_phone_code_to_countries` was renamed to `country_calling_codes` in `1.0.18`.\n  // For that reason, it's not used in this detection algorithm.\n  // Instead, it detects by `countries: {}` property existence.\n\n\n  if (!isObject(metadata) || !isObject(metadata.countries)) {\n    throw new Error(\"[libphonenumber-js] `metadata` argument was passed but it's not a valid metadata. Must be an object having `.countries` child object property. Got \".concat(isObject(metadata) ? 'an object of shape: { ' + Object.keys(metadata).join(', ') + ' }' : 'a ' + typeOf(metadata) + ': ' + metadata, \".\"));\n  }\n} // Babel transforms `typeof` into some \"branches\"\n// so istanbul will show this as \"branch not covered\".\n\n/* istanbul ignore next */\n\nvar typeOf = function typeOf(_) {\n  return _typeof(_);\n};\n/**\r\n * Returns extension prefix for a country.\r\n * @param  {string} country\r\n * @param  {object} metadata\r\n * @return {string?}\r\n * @example\r\n * // Returns \" ext. \"\r\n * getExtPrefix(\"US\")\r\n */\n\n\nexport function getExtPrefix(country, metadata) {\n  metadata = new Metadata(metadata);\n\n  if (metadata.hasCountry(country)) {\n    return metadata.country(country).ext();\n  }\n\n  return DEFAULT_EXT_PREFIX;\n}\n/**\r\n * Returns \"country calling code\" for a country.\r\n * Throws an error if the country doesn't exist or isn't supported by this library.\r\n * @param  {string} country\r\n * @param  {object} metadata\r\n * @return {string}\r\n * @example\r\n * // Returns \"44\"\r\n * getCountryCallingCode(\"GB\")\r\n */\n\nexport function getCountryCallingCode(country, metadata) {\n  metadata = new Metadata(metadata);\n\n  if (metadata.hasCountry(country)) {\n    return metadata.country(country).countryCallingCode();\n  }\n\n  throw new Error(\"Unknown country: \".concat(country));\n}\nexport function isSupportedCountry(country, metadata) {\n  // metadata = new Metadata(metadata)\n  // return metadata.hasCountry(country)\n  return metadata.countries.hasOwnProperty(country);\n}\n\nfunction setVersion(metadata) {\n  var version = metadata.version;\n\n  if (typeof version === 'number') {\n    this.v1 = version === 1;\n    this.v2 = version === 2;\n    this.v3 = version === 3;\n    this.v4 = version === 4;\n  } else {\n    if (!version) {\n      this.v1 = true;\n    } else if (compare(version, V3) === -1) {\n      this.v2 = true;\n    } else if (compare(version, V4) === -1) {\n      this.v3 = true;\n    } else {\n      this.v4 = true;\n    }\n  }\n} // const ISO_COUNTRY_CODE = /^[A-Z]{2}$/\n// function isCountryCode(countryCode) {\n// \treturn ISO_COUNTRY_CODE.test(countryCodeOrCountryCallingCode)\n// }\n"], "mappings": "AAAA,SAASA,OAAOA,CAACC,GAAG,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,GAAG,EAAE;IAAE,OAAO,OAAOA,GAAG;EAAE,CAAC,GAAG,UAAUA,GAAG,EAAE;IAAE,OAAOA,GAAG,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,GAAG,CAACG,WAAW,KAAKF,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,GAAG;EAAE,CAAC,EAAED,OAAO,CAACC,GAAG,CAAC;AAAE;AAE/U,SAASK,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AAExJ,SAASC,iBAAiBA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IAAE,IAAIE,UAAU,GAAGH,KAAK,CAACC,CAAC,CAAC;IAAEE,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;IAAED,UAAU,CAACE,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;IAAEC,MAAM,CAACC,cAAc,CAACT,MAAM,EAAEI,UAAU,CAACM,GAAG,EAAEN,UAAU,CAAC;EAAE;AAAE;AAE5T,SAASO,YAAYA,CAACd,WAAW,EAAEe,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEb,iBAAiB,CAACF,WAAW,CAACH,SAAS,EAAEkB,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAEd,iBAAiB,CAACF,WAAW,EAAEgB,WAAW,CAAC;EAAEL,MAAM,CAACC,cAAc,CAACZ,WAAW,EAAE,WAAW,EAAE;IAAEU,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAOV,WAAW;AAAE;AAE5R,OAAOiB,OAAO,MAAM,2BAA2B;AAC/C,OAAOC,QAAQ,MAAM,uBAAuB,CAAC,CAAC;AAC9C;;AAEA,IAAIC,EAAE,GAAG,QAAQ,CAAC,CAAC;;AAEnB,IAAIC,EAAE,GAAG,OAAO,CAAC,CAAC;;AAElB,IAAIC,EAAE,GAAG,QAAQ;AACjB,IAAIC,kBAAkB,GAAG,QAAQ;AACjC,IAAIC,oBAAoB,GAAG,OAAO;AAClC;AACA;AACA;;AAEA,IAAIC,QAAQ,GAAG,aAAa,YAAY;EACtC,SAASA,QAAQA,CAACC,QAAQ,EAAE;IAC1B3B,eAAe,CAAC,IAAI,EAAE0B,QAAQ,CAAC;IAE/BE,gBAAgB,CAACD,QAAQ,CAAC;IAC1B,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxBE,UAAU,CAACC,IAAI,CAAC,IAAI,EAAEH,QAAQ,CAAC;EACjC;EAEAX,YAAY,CAACU,QAAQ,EAAE,CAAC;IACtBX,GAAG,EAAE,cAAc;IACnBgB,KAAK,EAAE,SAASC,YAAYA,CAAA,EAAG;MAC7B,OAAOnB,MAAM,CAACoB,IAAI,CAAC,IAAI,CAACN,QAAQ,CAACO,SAAS,CAAC,CAACC,MAAM,CAAC,UAAUC,CAAC,EAAE;QAC9D,OAAOA,CAAC,KAAK,KAAK;MACpB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDrB,GAAG,EAAE,oBAAoB;IACzBgB,KAAK,EAAE,SAASM,kBAAkBA,CAACC,WAAW,EAAE;MAC9C,OAAO,IAAI,CAACX,QAAQ,CAACO,SAAS,CAACI,WAAW,CAAC;IAC7C;EACF,CAAC,EAAE;IACDvB,GAAG,EAAE,eAAe;IACpBgB,KAAK,EAAE,SAASQ,aAAaA,CAAA,EAAG;MAC9B,IAAI,IAAI,CAACC,EAAE,IAAI,IAAI,CAACC,EAAE,IAAI,IAAI,CAACC,EAAE,EAAE,OAAO,CAAC;MAC3C;MACA;;MAEA,OAAO,IAAI,CAACf,QAAQ,CAACY,aAAa,IAAI,IAAI,CAACZ,QAAQ,CAACgB,eAAe;IACrE;EACF,CAAC,EAAE;IACD5B,GAAG,EAAE,YAAY;IACjBgB,KAAK,EAAE,SAASa,UAAUA,CAACC,OAAO,EAAE;MAClC,OAAO,IAAI,CAACR,kBAAkB,CAACQ,OAAO,CAAC,KAAKC,SAAS;IACvD;EACF,CAAC,EAAE;IACD/B,GAAG,EAAE,gBAAgB;IACrBgB,KAAK,EAAE,SAASgB,cAAcA,CAACC,WAAW,EAAE;MAC1C,IAAI,IAAI,CAACC,6BAA6B,CAACD,WAAW,CAAC,EAAE;QACnD,OAAO,IAAI;MACb;MAEA,IAAI,IAAI,CAACT,aAAa,CAAC,CAAC,EAAE;QACxB,IAAI,IAAI,CAACA,aAAa,CAAC,CAAC,CAACS,WAAW,CAAC,EAAE;UACrC,OAAO,IAAI;QACb;MACF,CAAC,MAAM;QACL;QACA,IAAIE,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAAC,CAAC,CAACH,WAAW,CAAC;QAE1D,IAAIE,YAAY,IAAIA,YAAY,CAAC1C,MAAM,KAAK,CAAC,IAAI0C,YAAY,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;UAC1E,OAAO,IAAI;QACb;MACF;IACF;EACF,CAAC,EAAE;IACDnC,GAAG,EAAE,4BAA4B;IACjCgB,KAAK,EAAE,SAASqB,0BAA0BA,CAACJ,WAAW,EAAE;MACtD,IAAI,IAAI,CAACT,aAAa,CAAC,CAAC,EAAE;QACxB,OAAO,IAAI,CAACA,aAAa,CAAC,CAAC,CAACS,WAAW,CAAC,GAAG,IAAI,GAAG,KAAK;MACzD,CAAC,MAAM;QACL,OAAO,IAAI,CAACC,6BAA6B,CAACD,WAAW,CAAC,GAAG,KAAK,GAAG,IAAI;MACvE;IACF,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDjC,GAAG,EAAE,SAAS;IACdgB,KAAK,EAAE,SAASc,OAAOA,CAACP,WAAW,EAAE;MACnC,OAAO,IAAI,CAACe,mBAAmB,CAACf,WAAW,CAAC;IAC9C;EACF,CAAC,EAAE;IACDvB,GAAG,EAAE,qBAAqB;IAC1BgB,KAAK,EAAE,SAASsB,mBAAmBA,CAACf,WAAW,EAAEU,WAAW,EAAE;MAC5D;MACA,IAAIV,WAAW,IAAIb,oBAAoB,CAAC6B,IAAI,CAAChB,WAAW,CAAC,EAAE;QACzDU,WAAW,GAAGV,WAAW;QACzBA,WAAW,GAAG,IAAI;MACpB;MAEA,IAAIA,WAAW,IAAIA,WAAW,KAAK,KAAK,EAAE;QACxC,IAAI,CAAC,IAAI,CAACM,UAAU,CAACN,WAAW,CAAC,EAAE;UACjC,MAAM,IAAIiB,KAAK,CAAC,mBAAmB,CAACC,MAAM,CAAClB,WAAW,CAAC,CAAC;QAC1D;QAEA,IAAI,CAACmB,aAAa,GAAG,IAAIC,aAAa,CAAC,IAAI,CAACrB,kBAAkB,CAACC,WAAW,CAAC,EAAE,IAAI,CAAC;MACpF,CAAC,MAAM,IAAIU,WAAW,EAAE;QACtB,IAAI,CAAC,IAAI,CAACD,cAAc,CAACC,WAAW,CAAC,EAAE;UACrC,MAAM,IAAIO,KAAK,CAAC,wBAAwB,CAACC,MAAM,CAACR,WAAW,CAAC,CAAC;QAC/D;QAEA,IAAI,CAACS,aAAa,GAAG,IAAIC,aAAa,CAAC,IAAI,CAACC,wBAAwB,CAACX,WAAW,CAAC,EAAE,IAAI,CAAC;MAC1F,CAAC,MAAM;QACL,IAAI,CAACS,aAAa,GAAGX,SAAS;MAChC;MAEA,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACD/B,GAAG,EAAE,+BAA+B;IACpCgB,KAAK,EAAE,SAASkB,6BAA6BA,CAACD,WAAW,EAAE;MACzD,IAAIE,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAAC,CAAC,CAACH,WAAW,CAAC;MAE1D,IAAIE,YAAY,EAAE;QAChB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAIA,YAAY,CAAC1C,MAAM,KAAK,CAAC,IAAI0C,YAAY,CAAC,CAAC,CAAC,CAAC1C,MAAM,KAAK,CAAC,EAAE;UAC7D;QACF;QAEA,OAAO0C,YAAY;MACrB;IACF;EACF,CAAC,EAAE;IACDnC,GAAG,EAAE,8BAA8B;IACnCgB,KAAK,EAAE,SAAS6B,4BAA4BA,CAACZ,WAAW,EAAE;MACxD,IAAIE,YAAY,GAAG,IAAI,CAACD,6BAA6B,CAACD,WAAW,CAAC;MAElE,IAAIE,YAAY,EAAE;QAChB,OAAOA,YAAY,CAAC,CAAC,CAAC;MACxB;IACF;EACF,CAAC,EAAE;IACDnC,GAAG,EAAE,0BAA0B;IAC/BgB,KAAK,EAAE,SAAS4B,wBAAwBA,CAACX,WAAW,EAAE;MACpD,IAAIV,WAAW,GAAG,IAAI,CAACsB,4BAA4B,CAACZ,WAAW,CAAC;MAEhE,IAAIV,WAAW,EAAE;QACf,OAAO,IAAI,CAACD,kBAAkB,CAACC,WAAW,CAAC;MAC7C;MAEA,IAAI,IAAI,CAACC,aAAa,CAAC,CAAC,EAAE;QACxB,IAAIZ,QAAQ,GAAG,IAAI,CAACY,aAAa,CAAC,CAAC,CAACS,WAAW,CAAC;QAEhD,IAAIrB,QAAQ,EAAE;UACZ,OAAOA,QAAQ;QACjB;MACF,CAAC,MAAM;QACL;QACA;QACA;QACA;QACA;QACA,IAAIuB,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAAC,CAAC,CAACH,WAAW,CAAC;QAE1D,IAAIE,YAAY,IAAIA,YAAY,CAAC1C,MAAM,KAAK,CAAC,IAAI0C,YAAY,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;UAC1E,OAAO,IAAI,CAACvB,QAAQ,CAACO,SAAS,CAAC,KAAK,CAAC;QACvC;MACF;IACF,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDnB,GAAG,EAAE,oBAAoB;IACzBgB,KAAK,EAAE,SAAS8B,kBAAkBA,CAAA,EAAG;MACnC,OAAO,IAAI,CAACJ,aAAa,CAACT,WAAW,CAAC,CAAC;IACzC,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDjC,GAAG,EAAE,WAAW;IAChBgB,KAAK,EAAE,SAAS+B,SAASA,CAAA,EAAG;MAC1B,OAAO,IAAI,CAACL,aAAa,CAACK,SAAS,CAAC,CAAC;IACvC,CAAC,CAAC;EAEJ,CAAC,EAAE;IACD/C,GAAG,EAAE,kBAAkB;IACvBgB,KAAK,EAAE,SAASgC,gBAAgBA,CAAA,EAAG;MACjC,OAAO,IAAI,CAACN,aAAa,CAACM,gBAAgB,CAAC,CAAC;IAC9C,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDhD,GAAG,EAAE,uBAAuB;IAC5BgB,KAAK,EAAE,SAASiC,qBAAqBA,CAAA,EAAG;MACtC,OAAO,IAAI,CAACP,aAAa,CAACO,qBAAqB,CAAC,CAAC;IACnD,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDjD,GAAG,EAAE,iBAAiB;IACtBgB,KAAK,EAAE,SAASkC,eAAeA,CAAA,EAAG;MAChC,OAAO,IAAI,CAACR,aAAa,CAACQ,eAAe,CAAC,CAAC;IAC7C,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDlD,GAAG,EAAE,SAAS;IACdgB,KAAK,EAAE,SAASmC,OAAOA,CAAA,EAAG;MACxB,OAAO,IAAI,CAACT,aAAa,CAACS,OAAO,CAAC,CAAC;IACrC,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDnD,GAAG,EAAE,0BAA0B;IAC/BgB,KAAK,EAAE,SAASoC,wBAAwBA,CAAA,EAAG;MACzC,OAAO,IAAI,CAACV,aAAa,CAACU,wBAAwB,CAAC,CAAC;IACtD,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDpD,GAAG,EAAE,6BAA6B;IAClCgB,KAAK,EAAE,SAASqC,2BAA2BA,CAAA,EAAG;MAC5C,OAAO,IAAI,CAACX,aAAa,CAACW,2BAA2B,CAAC,CAAC;IACzD,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDrD,GAAG,EAAE,eAAe;IACpBgB,KAAK,EAAE,SAASsC,aAAaA,CAAA,EAAG;MAC9B,OAAO,IAAI,CAACZ,aAAa,CAACY,aAAa,CAAC,CAAC;IAC3C,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDtD,GAAG,EAAE,UAAU;IACfgB,KAAK,EAAE,SAASuC,QAAQA,CAAA,EAAG;MACzB,OAAO,IAAI,CAACb,aAAa,CAACa,QAAQ,CAAC,CAAC;IACtC,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDvD,GAAG,EAAE,MAAM;IACXgB,KAAK,EAAE,SAASwC,IAAIA,CAACC,KAAK,EAAE;MAC1B,OAAO,IAAI,CAACf,aAAa,CAACc,IAAI,CAACC,KAAK,CAAC;IACvC,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDzD,GAAG,EAAE,KAAK;IACVgB,KAAK,EAAE,SAAS0C,GAAGA,CAAA,EAAG;MACpB,OAAO,IAAI,CAAChB,aAAa,CAACgB,GAAG,CAAC,CAAC;IACjC;EACF,CAAC,EAAE;IACD1D,GAAG,EAAE,qBAAqB;IAC1BgB,KAAK,EAAE,SAASoB,mBAAmBA,CAAA,EAAG;MACpC,IAAI,IAAI,CAACX,EAAE,EAAE,OAAO,IAAI,CAACb,QAAQ,CAAC+C,+BAA+B;MACjE,OAAO,IAAI,CAAC/C,QAAQ,CAACgD,qBAAqB;IAC5C,CAAC,CAAC;EAEJ,CAAC,EAAE;IACD5D,GAAG,EAAE,mCAAmC;IACxCgB,KAAK,EAAE,SAAS6C,iCAAiCA,CAAC5B,WAAW,EAAE;MAC7D,OAAO,IAAI,CAACK,mBAAmB,CAACL,WAAW,CAAC;IAC9C;EACF,CAAC,EAAE;IACDjC,GAAG,EAAE,0BAA0B;IAC/BgB,KAAK,EAAE,SAAS8C,wBAAwBA,CAAA,EAAG;MACzC,OAAO,IAAI,CAACpB,aAAa,KAAKX,SAAS;IACzC;EACF,CAAC,CAAC,CAAC;EAEH,OAAOpB,QAAQ;AACjB,CAAC,CAAC,CAAC;AAEH,SAASA,QAAQ,IAAIoD,OAAO;AAE5B,IAAIpB,aAAa,GAAG,aAAa,YAAY;EAC3C,SAASA,aAAaA,CAAC/B,QAAQ,EAAEoD,oBAAoB,EAAE;IACrD/E,eAAe,CAAC,IAAI,EAAE0D,aAAa,CAAC;IAEpC,IAAI,CAACqB,oBAAoB,GAAGA,oBAAoB;IAChD,IAAI,CAACpD,QAAQ,GAAGA,QAAQ;IACxBE,UAAU,CAACC,IAAI,CAAC,IAAI,EAAEiD,oBAAoB,CAACpD,QAAQ,CAAC;EACtD;EAEAX,YAAY,CAAC0C,aAAa,EAAE,CAAC;IAC3B3C,GAAG,EAAE,aAAa;IAClBgB,KAAK,EAAE,SAASiB,WAAWA,CAAA,EAAG;MAC5B,OAAO,IAAI,CAACrB,QAAQ,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC;IACF;IACA;IACA;IACA;IACA;EAEF,CAAC,EAAE;IACDZ,GAAG,EAAE,oCAAoC;IACzCgB,KAAK,EAAE,SAASiD,kCAAkCA,CAAA,EAAG;MACnD,OAAO,IAAI,CAACD,oBAAoB,CAACpB,wBAAwB,CAAC,IAAI,CAACX,WAAW,CAAC,CAAC,CAAC;IAC/E,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDjC,GAAG,EAAE,WAAW;IAChBgB,KAAK,EAAE,SAAS+B,SAASA,CAAA,EAAG;MAC1B,IAAI,IAAI,CAACtB,EAAE,IAAI,IAAI,CAACC,EAAE,EAAE;MACxB,OAAO,IAAI,CAACd,QAAQ,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDZ,GAAG,EAAE,kBAAkB;IACvBgB,KAAK,EAAE,SAASgC,gBAAgBA,CAAA,EAAG;MACjC,IAAI,IAAI,CAACvB,EAAE,IAAI,IAAI,CAACC,EAAE,EAAE;MACxB,OAAO,IAAI,CAACd,QAAQ,CAAC,EAAE,CAAC;IAC1B;EACF,CAAC,EAAE;IACDZ,GAAG,EAAE,uBAAuB;IAC5BgB,KAAK,EAAE,SAASiC,qBAAqBA,CAAA,EAAG;MACtC,IAAI,IAAI,CAACxB,EAAE,IAAI,IAAI,CAACC,EAAE,EAAE,OAAO,IAAI,CAACd,QAAQ,CAAC,CAAC,CAAC;MAC/C,OAAO,IAAI,CAACA,QAAQ,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDZ,GAAG,EAAE,iBAAiB;IACtBgB,KAAK,EAAE,SAASkC,eAAeA,CAAA,EAAG;MAChC,IAAI,IAAI,CAACzB,EAAE,EAAE;MACb,OAAO,IAAI,CAACb,QAAQ,CAAC,IAAI,CAACc,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;IACvC;EACF,CAAC,EAAE;IACD1B,GAAG,EAAE,aAAa;IAClBgB,KAAK,EAAE,SAASkD,WAAWA,CAACtD,QAAQ,EAAE;MACpC,OAAOA,QAAQ,CAAC,IAAI,CAACa,EAAE,GAAG,CAAC,GAAG,IAAI,CAACC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;IAChD,CAAC,CAAC;IACF;IACA;EAEF,CAAC,EAAE;IACD1B,GAAG,EAAE,SAAS;IACdgB,KAAK,EAAE,SAASmC,OAAOA,CAAA,EAAG;MACxB,IAAIgB,KAAK,GAAG,IAAI;MAEhB,IAAIhB,OAAO,GAAG,IAAI,CAACe,WAAW,CAAC,IAAI,CAACtD,QAAQ,CAAC,IAAI,IAAI,CAACsD,WAAW,CAAC,IAAI,CAACD,kCAAkC,CAAC,CAAC,CAAC,IAAI,EAAE;MAClH,OAAOd,OAAO,CAACiB,GAAG,CAAC,UAAU/C,CAAC,EAAE;QAC9B,OAAO,IAAIgD,MAAM,CAAChD,CAAC,EAAE8C,KAAK,CAAC;MAC7B,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDnE,GAAG,EAAE,gBAAgB;IACrBgB,KAAK,EAAE,SAASsD,cAAcA,CAAA,EAAG;MAC/B,OAAO,IAAI,CAAC1D,QAAQ,CAAC,IAAI,CAACa,EAAE,GAAG,CAAC,GAAG,IAAI,CAACC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;IACrD;EACF,CAAC,EAAE;IACD1B,GAAG,EAAE,kCAAkC;IACvCgB,KAAK,EAAE,SAASuD,gCAAgCA,CAAC3D,QAAQ,EAAE;MACzD,OAAOA,QAAQ,CAAC,IAAI,CAACa,EAAE,GAAG,CAAC,GAAG,IAAI,CAACC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;IAChD,CAAC,CAAC;IACF;IACA;EAEF,CAAC,EAAE;IACD1B,GAAG,EAAE,8BAA8B;IACnCgB,KAAK,EAAE,SAASwD,4BAA4BA,CAAA,EAAG;MAC7C,OAAO,IAAI,CAACD,gCAAgC,CAAC,IAAI,CAAC3D,QAAQ,CAAC,IAAI,IAAI,CAAC2D,gCAAgC,CAAC,IAAI,CAACN,kCAAkC,CAAC,CAAC,CAAC;IACjJ;EACF,CAAC,EAAE;IACDjE,GAAG,EAAE,2BAA2B;IAChCgB,KAAK,EAAE,SAASyD,yBAAyBA,CAAA,EAAG;MAC1C,OAAO,IAAI,CAAC7D,QAAQ,CAAC,IAAI,CAACa,EAAE,GAAG,CAAC,GAAG,IAAI,CAACC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;IACrD;EACF,CAAC,EAAE;IACD1B,GAAG,EAAE,0BAA0B;IAC/BgB,KAAK,EAAE,SAASoC,wBAAwBA,CAAA,EAAG;MACzC;MACA;MACA,OAAO,IAAI,CAACqB,yBAAyB,CAAC,CAAC,IAAI,IAAI,CAACH,cAAc,CAAC,CAAC;IAClE;EACF,CAAC,EAAE;IACDtE,GAAG,EAAE,6BAA6B;IAClCgB,KAAK,EAAE,SAASqC,2BAA2BA,CAAA,EAAG;MAC5C,OAAO,IAAI,CAACzC,QAAQ,CAAC,IAAI,CAACa,EAAE,GAAG,CAAC,GAAG,IAAI,CAACC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;IACrD;EACF,CAAC,EAAE;IACD1B,GAAG,EAAE,4CAA4C;IACjDgB,KAAK,EAAE,SAAS0D,0CAA0CA,CAAA,EAAG;MAC3D,OAAO,CAAC,CAAC,IAAI,CAAC9D,QAAQ,CAAC,IAAI,CAACa,EAAE,GAAG,CAAC,GAAG,IAAI,CAACC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;IACvD,CAAC,CAAC;IACF;IACA;IACA;EAEF,CAAC,EAAE;IACD1B,GAAG,EAAE,wDAAwD;IAC7DgB,KAAK,EAAE,SAAS2D,sDAAsDA,CAAA,EAAG;MACvE,OAAO,IAAI,CAACD,0CAA0C,CAAC,IAAI,CAAC9D,QAAQ,CAAC,IAAI,IAAI,CAAC8D,0CAA0C,CAAC,IAAI,CAACT,kCAAkC,CAAC,CAAC,CAAC;IACrK;EACF,CAAC,EAAE;IACDjE,GAAG,EAAE,eAAe;IACpBgB,KAAK,EAAE,SAASsC,aAAaA,CAAA,EAAG;MAC9B,OAAO,IAAI,CAAC1C,QAAQ,CAAC,IAAI,CAACa,EAAE,GAAG,CAAC,GAAG,IAAI,CAACC,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;IACtD;EACF,CAAC,EAAE;IACD1B,GAAG,EAAE,OAAO;IACZgB,KAAK,EAAE,SAAS4D,KAAKA,CAAA,EAAG;MACtB,OAAO,IAAI,CAAChE,QAAQ,CAAC,IAAI,CAACa,EAAE,GAAG,CAAC,GAAG,IAAI,CAACC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;IACvD;EACF,CAAC,EAAE;IACD1B,GAAG,EAAE,UAAU;IACfgB,KAAK,EAAE,SAASuC,QAAQA,CAAA,EAAG;MACzB;;MAEA;MACA,IAAI,IAAI,CAACqB,KAAK,CAAC,CAAC,IAAI,IAAI,CAACA,KAAK,CAAC,CAAC,CAACnF,MAAM,KAAK,CAAC,EAAE;QAC7C,OAAO,KAAK;MACd,CAAC,CAAC;MACF;;MAGA,OAAO,CAAC,CAAC,IAAI,CAACmF,KAAK,CAAC,CAAC;IACvB;EACF,CAAC,EAAE;IACD5E,GAAG,EAAE,MAAM;IACXgB,KAAK,EAAE,SAASwC,IAAIA,CAACqB,MAAM,EAAE;MAC3B,IAAI,IAAI,CAACtB,QAAQ,CAAC,CAAC,IAAIuB,OAAO,CAAC,IAAI,CAACF,KAAK,CAAC,CAAC,EAAEC,MAAM,CAAC,EAAE;QACpD,OAAO,IAAIE,IAAI,CAACD,OAAO,CAAC,IAAI,CAACF,KAAK,CAAC,CAAC,EAAEC,MAAM,CAAC,EAAE,IAAI,CAAC;MACtD;IACF;EACF,CAAC,EAAE;IACD7E,GAAG,EAAE,KAAK;IACVgB,KAAK,EAAE,SAAS0C,GAAGA,CAAA,EAAG;MACpB,IAAI,IAAI,CAACjC,EAAE,IAAI,IAAI,CAACC,EAAE,EAAE,OAAOjB,kBAAkB;MACjD,OAAO,IAAI,CAACG,QAAQ,CAAC,EAAE,CAAC,IAAIH,kBAAkB;IAChD;EACF,CAAC,CAAC,CAAC;EAEH,OAAOkC,aAAa;AACtB,CAAC,CAAC,CAAC;AAEH,IAAI0B,MAAM,GAAG,aAAa,YAAY;EACpC,SAASA,MAAMA,CAACW,MAAM,EAAEpE,QAAQ,EAAE;IAChC3B,eAAe,CAAC,IAAI,EAAEoF,MAAM,CAAC;IAE7B,IAAI,CAACY,OAAO,GAAGD,MAAM;IACrB,IAAI,CAACpE,QAAQ,GAAGA,QAAQ;EAC1B;EAEAX,YAAY,CAACoE,MAAM,EAAE,CAAC;IACpBrE,GAAG,EAAE,SAAS;IACdgB,KAAK,EAAE,SAASkE,OAAOA,CAAA,EAAG;MACxB,OAAO,IAAI,CAACD,OAAO,CAAC,CAAC,CAAC;IACxB;EACF,CAAC,EAAE;IACDjF,GAAG,EAAE,QAAQ;IACbgB,KAAK,EAAE,SAASgE,MAAMA,CAAA,EAAG;MACvB,OAAO,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC;IACxB;EACF,CAAC,EAAE;IACDjF,GAAG,EAAE,uBAAuB;IAC5BgB,KAAK,EAAE,SAASmE,qBAAqBA,CAAA,EAAG;MACtC,OAAO,IAAI,CAACF,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE;IAC9B;EACF,CAAC,EAAE;IACDjF,GAAG,EAAE,8BAA8B;IACnCgB,KAAK,EAAE,SAASwD,4BAA4BA,CAAA,EAAG;MAC7C,OAAO,IAAI,CAACS,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,CAACrE,QAAQ,CAAC4D,4BAA4B,CAAC,CAAC;IACxE;EACF,CAAC,EAAE;IACDxE,GAAG,EAAE,wDAAwD;IAC7DgB,KAAK,EAAE,SAAS2D,sDAAsDA,CAAA,EAAG;MACvE,OAAO,CAAC,CAAC,IAAI,CAACM,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,CAACrE,QAAQ,CAAC+D,sDAAsD,CAAC,CAAC;IACpG;EACF,CAAC,EAAE;IACD3E,GAAG,EAAE,yDAAyD;IAC9DgB,KAAK,EAAE,SAASoE,uDAAuDA,CAAA,EAAG;MACxE;MACA;MACA;MACA;MACA;MACA,OAAO,IAAI,CAACC,kBAAkB,CAAC,CAAC,IAAI,CAAC,IAAI,CAACV,sDAAsD,CAAC,CAAC;IACpG,CAAC,CAAC;EAEJ,CAAC,EAAE;IACD3E,GAAG,EAAE,oBAAoB;IACzBgB,KAAK,EAAE,SAASqE,kBAAkBA,CAAA,EAAG;MACnC,OAAO,IAAI,CAACb,4BAA4B,CAAC,CAAC;MAAI;MAC9C,CAACc,+BAA+B,CAAC/C,IAAI,CAAC,IAAI,CAACiC,4BAA4B,CAAC,CAAC,CAAC,CAAC;MAC3E;MACA;MACA;MAAA,EACE,IAAI,GAAG,KAAK;IAChB;EACF,CAAC,EAAE;IACDxE,GAAG,EAAE,qBAAqB;IAC1BgB,KAAK,EAAE,SAASuE,mBAAmBA,CAAA,EAAG;MACpC,OAAO,IAAI,CAACN,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,CAACD,MAAM,CAAC,CAAC;IACzC;EACF,CAAC,CAAC,CAAC;EAEH,OAAOX,MAAM;AACf,CAAC,CAAC,CAAC;AACH;AACA;AACA;AACA;AACA;;AAGA,IAAIiB,+BAA+B,GAAG,aAAa;AAEnD,IAAIP,IAAI,GAAG,aAAa,YAAY;EAClC,SAASA,IAAIA,CAACvB,IAAI,EAAE5C,QAAQ,EAAE;IAC5B3B,eAAe,CAAC,IAAI,EAAE8F,IAAI,CAAC;IAE3B,IAAI,CAACvB,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC5C,QAAQ,GAAGA,QAAQ;EAC1B;EAEAX,YAAY,CAAC8E,IAAI,EAAE,CAAC;IAClB/E,GAAG,EAAE,SAAS;IACdgB,KAAK,EAAE,SAASkE,OAAOA,CAAA,EAAG;MACxB,IAAI,IAAI,CAACtE,QAAQ,CAACa,EAAE,EAAE,OAAO,IAAI,CAAC+B,IAAI;MACtC,OAAO,IAAI,CAACA,IAAI,CAAC,CAAC,CAAC;IACrB;EACF,CAAC,EAAE;IACDxD,GAAG,EAAE,iBAAiB;IACtBgB,KAAK,EAAE,SAASkC,eAAeA,CAAA,EAAG;MAChC,IAAI,IAAI,CAACtC,QAAQ,CAACa,EAAE,EAAE;MACtB,OAAO,IAAI,CAAC+B,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC5C,QAAQ,CAACsC,eAAe,CAAC,CAAC;IACxD;EACF,CAAC,CAAC,CAAC;EAEH,OAAO6B,IAAI;AACb,CAAC,CAAC,CAAC;AAEH,SAASD,OAAOA,CAACF,KAAK,EAAEpB,IAAI,EAAE;EAC5B,QAAQA,IAAI;IACV,KAAK,YAAY;MACf,OAAOoB,KAAK,CAAC,CAAC,CAAC;IAEjB,KAAK,QAAQ;MACX,OAAOA,KAAK,CAAC,CAAC,CAAC;IAEjB,KAAK,WAAW;MACd,OAAOA,KAAK,CAAC,CAAC,CAAC;IAEjB,KAAK,cAAc;MACjB,OAAOA,KAAK,CAAC,CAAC,CAAC;IAEjB,KAAK,iBAAiB;MACpB,OAAOA,KAAK,CAAC,CAAC,CAAC;IAEjB,KAAK,WAAW;MACd,OAAOA,KAAK,CAAC,CAAC,CAAC;IAEjB,KAAK,KAAK;MACR,OAAOA,KAAK,CAAC,CAAC,CAAC;IAEjB,KAAK,OAAO;MACV,OAAOA,KAAK,CAAC,CAAC,CAAC;IAEjB,KAAK,MAAM;MACT,OAAOA,KAAK,CAAC,CAAC,CAAC;IAEjB,KAAK,aAAa;MAChB,OAAOA,KAAK,CAAC,CAAC,CAAC;EACnB;AACF;AAEA,OAAO,SAAS/D,gBAAgBA,CAACD,QAAQ,EAAE;EACzC,IAAI,CAACA,QAAQ,EAAE;IACb,MAAM,IAAI4B,KAAK,CAAC,2EAA2E,CAAC;EAC9F,CAAC,CAAC;EACF;EACA;;EAGA,IAAI,CAACnC,QAAQ,CAACO,QAAQ,CAAC,IAAI,CAACP,QAAQ,CAACO,QAAQ,CAACO,SAAS,CAAC,EAAE;IACxD,MAAM,IAAIqB,KAAK,CAAC,qJAAqJ,CAACC,MAAM,CAACpC,QAAQ,CAACO,QAAQ,CAAC,GAAG,wBAAwB,GAAGd,MAAM,CAACoB,IAAI,CAACN,QAAQ,CAAC,CAAC4E,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,IAAI,GAAGC,MAAM,CAAC7E,QAAQ,CAAC,GAAG,IAAI,GAAGA,QAAQ,EAAE,GAAG,CAAC,CAAC;EACzT;AACF,CAAC,CAAC;AACF;;AAEA;;AAEA,IAAI6E,MAAM,GAAG,SAASA,MAAMA,CAACpE,CAAC,EAAE;EAC9B,OAAO1C,OAAO,CAAC0C,CAAC,CAAC;AACnB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,OAAO,SAASqE,YAAYA,CAAC5D,OAAO,EAAElB,QAAQ,EAAE;EAC9CA,QAAQ,GAAG,IAAID,QAAQ,CAACC,QAAQ,CAAC;EAEjC,IAAIA,QAAQ,CAACiB,UAAU,CAACC,OAAO,CAAC,EAAE;IAChC,OAAOlB,QAAQ,CAACkB,OAAO,CAACA,OAAO,CAAC,CAAC4B,GAAG,CAAC,CAAC;EACxC;EAEA,OAAOjD,kBAAkB;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASkF,qBAAqBA,CAAC7D,OAAO,EAAElB,QAAQ,EAAE;EACvDA,QAAQ,GAAG,IAAID,QAAQ,CAACC,QAAQ,CAAC;EAEjC,IAAIA,QAAQ,CAACiB,UAAU,CAACC,OAAO,CAAC,EAAE;IAChC,OAAOlB,QAAQ,CAACkB,OAAO,CAACA,OAAO,CAAC,CAACgB,kBAAkB,CAAC,CAAC;EACvD;EAEA,MAAM,IAAIN,KAAK,CAAC,mBAAmB,CAACC,MAAM,CAACX,OAAO,CAAC,CAAC;AACtD;AACA,OAAO,SAAS8D,kBAAkBA,CAAC9D,OAAO,EAAElB,QAAQ,EAAE;EACpD;EACA;EACA,OAAOA,QAAQ,CAACO,SAAS,CAAC0E,cAAc,CAAC/D,OAAO,CAAC;AACnD;AAEA,SAAShB,UAAUA,CAACF,QAAQ,EAAE;EAC5B,IAAIkF,OAAO,GAAGlF,QAAQ,CAACkF,OAAO;EAE9B,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;IAC/B,IAAI,CAACrE,EAAE,GAAGqE,OAAO,KAAK,CAAC;IACvB,IAAI,CAACpE,EAAE,GAAGoE,OAAO,KAAK,CAAC;IACvB,IAAI,CAACnE,EAAE,GAAGmE,OAAO,KAAK,CAAC;IACvB,IAAI,CAACC,EAAE,GAAGD,OAAO,KAAK,CAAC;EACzB,CAAC,MAAM;IACL,IAAI,CAACA,OAAO,EAAE;MACZ,IAAI,CAACrE,EAAE,GAAG,IAAI;IAChB,CAAC,MAAM,IAAIrB,OAAO,CAAC0F,OAAO,EAAEvF,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;MACtC,IAAI,CAACmB,EAAE,GAAG,IAAI;IAChB,CAAC,MAAM,IAAItB,OAAO,CAAC0F,OAAO,EAAEtF,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;MACtC,IAAI,CAACmB,EAAE,GAAG,IAAI;IAChB,CAAC,MAAM;MACL,IAAI,CAACoE,EAAE,GAAG,IAAI;IAChB;EACF;AACF,CAAC,CAAC;AACF;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}