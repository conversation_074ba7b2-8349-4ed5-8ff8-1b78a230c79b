{"ast": null, "code": "import Metadata from './metadata.js';\nimport matchesEntirely from './helpers/matchesEntirely.js';\nimport getNumberType from './helpers/getNumberType.js';\n/**\r\n * Checks if a given phone number is valid.\r\n *\r\n * isValid(phoneNumberInstance, { ..., v2: true }, metadata)\r\n *\r\n * isPossible({ phone: '8005553535', country: 'RU' }, { ... }, metadata)\r\n * isPossible({ phone: '8005553535', country: 'RU' }, undefined, metadata)\r\n *\r\n * If the `number` is a string, it will be parsed to an object,\r\n * but only if it contains only valid phone number characters (including punctuation).\r\n * If the `number` is an object, it is used as is.\r\n *\r\n * The optional `defaultCountry` argument is the default country.\r\n * I.e. it does not restrict to just that country,\r\n * e.g. in those cases where several countries share\r\n * the same phone numbering rules (NANPA, Britain, etc).\r\n * For example, even though the number `07624 369230`\r\n * belongs to the Isle of Man (\"IM\" country code)\r\n * calling `isValidNumber('07624369230', 'GB', metadata)`\r\n * still returns `true` because the country is not restricted to `GB`,\r\n * it's just that `GB` is the default one for the phone numbering rules.\r\n * For restricting the country see `isValidNumberForRegion()`\r\n * though restricting a country might not be a good idea.\r\n * https://github.com/googlei18n/libphonenumber/blob/master/FAQ.md#when-should-i-use-isvalidnumberforregion\r\n *\r\n * Examples:\r\n *\r\n * ```js\r\n * isValidNumber('+78005553535', metadata)\r\n * isValidNumber('8005553535', 'RU', metadata)\r\n * isValidNumber('88005553535', 'RU', metadata)\r\n * isValidNumber({ phone: '8005553535', country: 'RU' }, metadata)\r\n * ```\r\n */\n\nexport default function isValidNumber(input, options, metadata) {\n  // If assigning the `{}` default value is moved to the arguments above,\n  // code coverage would decrease for some weird reason.\n  options = options || {};\n  metadata = new Metadata(metadata);\n  metadata.selectNumberingPlan(input.country, input.countryCallingCode); // By default, countries only have type regexps when it's required for\n  // distinguishing different countries having the same `countryCallingCode`.\n\n  if (metadata.hasTypes()) {\n    return getNumberType(input, options, metadata.metadata) !== undefined;\n  } // If there are no type regexps for this country in metadata then use\n  // `nationalNumberPattern` as a \"better than nothing\" replacement.\n\n  var nationalNumber = options.v2 ? input.nationalNumber : input.phone;\n  return matchesEntirely(nationalNumber, metadata.nationalNumberPattern());\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "matchesEntirely", "getNumberType", "isValidNumber", "input", "options", "metadata", "selectNumberingPlan", "country", "countryCallingCode", "hasTypes", "undefined", "nationalNumber", "v2", "phone", "nationalNumberPattern"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/isValid.js"], "sourcesContent": ["import Metadata from './metadata.js';\nimport matchesEntirely from './helpers/matchesEntirely.js';\nimport getNumberType from './helpers/getNumberType.js';\n/**\r\n * Checks if a given phone number is valid.\r\n *\r\n * isValid(phoneNumberInstance, { ..., v2: true }, metadata)\r\n *\r\n * isPossible({ phone: '8005553535', country: 'RU' }, { ... }, metadata)\r\n * isPossible({ phone: '8005553535', country: 'RU' }, undefined, metadata)\r\n *\r\n * If the `number` is a string, it will be parsed to an object,\r\n * but only if it contains only valid phone number characters (including punctuation).\r\n * If the `number` is an object, it is used as is.\r\n *\r\n * The optional `defaultCountry` argument is the default country.\r\n * I.e. it does not restrict to just that country,\r\n * e.g. in those cases where several countries share\r\n * the same phone numbering rules (NANPA, Britain, etc).\r\n * For example, even though the number `07624 369230`\r\n * belongs to the Isle of Man (\"IM\" country code)\r\n * calling `isValidNumber('07624369230', 'GB', metadata)`\r\n * still returns `true` because the country is not restricted to `GB`,\r\n * it's just that `GB` is the default one for the phone numbering rules.\r\n * For restricting the country see `isValidNumberForRegion()`\r\n * though restricting a country might not be a good idea.\r\n * https://github.com/googlei18n/libphonenumber/blob/master/FAQ.md#when-should-i-use-isvalidnumberforregion\r\n *\r\n * Examples:\r\n *\r\n * ```js\r\n * isValidNumber('+78005553535', metadata)\r\n * isValidNumber('8005553535', 'RU', metadata)\r\n * isValidNumber('88005553535', 'RU', metadata)\r\n * isValidNumber({ phone: '8005553535', country: 'RU' }, metadata)\r\n * ```\r\n */\n\nexport default function isValidNumber(input, options, metadata) {\n  // If assigning the `{}` default value is moved to the arguments above,\n  // code coverage would decrease for some weird reason.\n  options = options || {};\n  metadata = new Metadata(metadata);\n  metadata.selectNumberingPlan(input.country, input.countryCallingCode); // By default, countries only have type regexps when it's required for\n  // distinguishing different countries having the same `countryCallingCode`.\n\n  if (metadata.hasTypes()) {\n    return getNumberType(input, options, metadata.metadata) !== undefined;\n  } // If there are no type regexps for this country in metadata then use\n  // `nationalNumberPattern` as a \"better than nothing\" replacement.\n\n\n  var nationalNumber = options.v2 ? input.nationalNumber : input.phone;\n  return matchesEntirely(nationalNumber, metadata.nationalNumberPattern());\n}\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;AACpC,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,aAAa,MAAM,4BAA4B;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,aAAaA,CAACC,KAAK,EAAEC,OAAO,EAAEC,QAAQ,EAAE;EAC9D;EACA;EACAD,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvBC,QAAQ,GAAG,IAAIN,QAAQ,CAACM,QAAQ,CAAC;EACjCA,QAAQ,CAACC,mBAAmB,CAACH,KAAK,CAACI,OAAO,EAAEJ,KAAK,CAACK,kBAAkB,CAAC,CAAC,CAAC;EACvE;;EAEA,IAAIH,QAAQ,CAACI,QAAQ,CAAC,CAAC,EAAE;IACvB,OAAOR,aAAa,CAACE,KAAK,EAAEC,OAAO,EAAEC,QAAQ,CAACA,QAAQ,CAAC,KAAKK,SAAS;EACvE,CAAC,CAAC;EACF;;EAGA,IAAIC,cAAc,GAAGP,OAAO,CAACQ,EAAE,GAAGT,KAAK,CAACQ,cAAc,GAAGR,KAAK,CAACU,KAAK;EACpE,OAAOb,eAAe,CAACW,cAAc,EAAEN,QAAQ,CAACS,qBAAqB,CAAC,CAAC,CAAC;AAC1E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}