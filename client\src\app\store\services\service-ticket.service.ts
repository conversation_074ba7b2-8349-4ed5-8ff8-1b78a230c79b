import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { CMS_APIContstant } from 'src/app/constants/api.constants';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class ServiceTicketService {

  constructor(private http: HttpClient) { }

  getAll(query: string) {
    return this.http.get<any>(`${CMS_APIContstant.TICKET}?${query}`);
  }

  getTickets(
    page: number,
    pageSize: number,
    sortField?: string,
    sortOrder?: number,
    filters?: any
  ): Observable<any> {
    let params = new HttpParams()
      .set('pagination[page]', Math.max(page, 1).toString())
      .set('pagination[pageSize]', Math.max(pageSize, 1).toString());

    // Add sorting
    if (sortField && sortOrder !== undefined) {
      const order = sortOrder === 1 ? 'asc' : 'desc';
      params = params.set('sort', `${sortField}:${order}`);
    }

    // Add filters if provided
    if (filters) {
      // Convert filters object to query parameters
      Object.keys(filters).forEach(key => {
        if (filters[key] !== undefined && filters[key] !== null) {
          params = params.set(key, filters[key]);
        }
      });
    }

    return this.http.get<any>(CMS_APIContstant.TICKET, { params });
  }

  getById(id: string) {
    return this.http.get<any>(`${CMS_APIContstant.TICKET}?filters[id]=${id}`);
  }

  getByAccountId(id: string) {
    return this.http.get<any>(`${CMS_APIContstant.TICKET}?filters[account_id]=${id}`);
  }

  createTicket(data: any) {
    return this.http.post<any>(`${CMS_APIContstant.TICKET}`, data);
  }

  updateTicket(id: string, data: any) {
    return this.http.put<any>(`${CMS_APIContstant.TICKET}/${id}`, data);
  }

  getAllTicketStatus() {
    return this.http.get<any>(CMS_APIContstant.CONFIG_DATA + '?filters[type][$eq]=TICKET_STATUS');
  }
}
