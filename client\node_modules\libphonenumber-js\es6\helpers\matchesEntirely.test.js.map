{"version": 3, "file": "matchesEntirely.test.js", "names": ["matchesEntirely", "describe", "it", "undefined", "should", "equal"], "sources": ["../../source/helpers/matchesEntirely.test.js"], "sourcesContent": ["import matchesEntirely from './matchesEntirely.js'\r\n\r\ndescribe('matchesEntirely', () => {\r\n\tit('should work in edge cases', () => {\r\n\t\t// No text.\r\n\t\tmatchesEntirely(undefined, '').should.equal(true)\r\n\r\n\t\t// \"OR\" in regexp.\r\n\t\tmatchesEntirely('911231231', '4\\d{8}|[1-9]\\d{7}').should.equal(false)\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,eAAP,MAA4B,sBAA5B;AAEAC,QAAQ,CAAC,iBAAD,EAAoB,YAAM;EACjCC,EAAE,CAAC,2BAAD,EAA8B,YAAM;IACrC;IACAF,eAAe,CAACG,SAAD,EAAY,EAAZ,CAAf,CAA+BC,MAA/B,CAAsCC,KAAtC,CAA4C,IAA5C,EAFqC,CAIrC;;IACAL,eAAe,CAAC,WAAD,EAAc,mBAAd,CAAf,CAAkDI,MAAlD,CAAyDC,KAAzD,CAA+D,KAA/D;EACA,CANC,CAAF;AAOA,CARO,CAAR"}