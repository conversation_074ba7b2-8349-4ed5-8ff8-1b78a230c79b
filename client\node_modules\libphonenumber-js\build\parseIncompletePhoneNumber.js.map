{"version": 3, "file": "parseIncompletePhoneNumber.js", "names": ["parseIncompletePhoneNumber", "string", "result", "split", "character", "parsePhoneNumberCharacter", "prevParsedCharacters", "emitEvent", "parseDigit"], "sources": ["../source/parseIncompletePhoneNumber.js"], "sourcesContent": ["import { parseDigit } from './helpers/parseDigits.js'\r\n\r\n/**\r\n * Parses phone number characters from a string.\r\n * Drops all punctuation leaving only digits and the leading `+` sign (if any).\r\n * Also converts wide-ascii and arabic-indic numerals to conventional numerals.\r\n * E.g. in Iraq they don't write `+442323234` but rather `+٤٤٢٣٢٣٢٣٤`.\r\n * @param  {string} string\r\n * @return {string}\r\n * @example\r\n * ```js\r\n * // Outputs '8800555'.\r\n * parseIncompletePhoneNumber('8 (800) 555')\r\n * // Outputs '+7800555'.\r\n * parseIncompletePhoneNumber('****** 555')\r\n * ```\r\n */\r\nexport default function parseIncompletePhoneNumber(string) {\r\n\tlet result = ''\r\n\t// Using `.split('')` here instead of normal `for ... of`\r\n\t// because the importing application doesn't neccessarily include an ES6 polyfill.\r\n\t// The `.split('')` approach discards \"exotic\" UTF-8 characters\r\n\t// (the ones consisting of four bytes) but digits\r\n\t// (including non-European ones) don't fall into that range\r\n\t// so such \"exotic\" characters would be discarded anyway.\r\n\tfor (const character of string.split('')) {\r\n\t\tresult += parsePhoneNumberCharacter(character, result) || ''\r\n\t}\r\n\treturn result\r\n}\r\n\r\n/**\r\n * Parses next character while parsing phone number digits (including a `+`)\r\n * from text: discards everything except `+` and digits, and `+` is only allowed\r\n * at the start of a phone number.\r\n * For example, is used in `react-phone-number-input` where it uses\r\n * [`input-format`](https://gitlab.com/catamphetamine/input-format).\r\n * @param  {string} character - Yet another character from raw input string.\r\n * @param  {string?} prevParsedCharacters - Previous parsed characters.\r\n * @param  {function?} emitEvent - An optional \"emit event\" function.\r\n * @return {string?} The parsed character.\r\n */\r\nexport function parsePhoneNumberCharacter(character, prevParsedCharacters, emitEvent) {\r\n\t// Only allow a leading `+`.\r\n\tif (character === '+') {\r\n\t\t// If this `+` is not the first parsed character\r\n\t\t// then discard it.\r\n\t\tif (prevParsedCharacters) {\r\n\t\t\t// `emitEvent` argument was added to this `export`ed function on Dec 26th, 2023.\r\n\t\t\t// Any 3rd-party code that used to `import` and call this function before that\r\n\t\t\t// won't be passing any `emitEvent` argument.\r\n\t\t\t//\r\n\t\t\t// The addition of the `emitEvent` argument was to fix the slightly-weird behavior\r\n\t\t\t// of parsing an input string when the user inputs something like `\"2+7\"\r\n\t\t\t// https://github.com/catamphetamine/react-phone-number-input/issues/437\r\n\t\t\t//\r\n\t\t\t// If the parser encounters an unexpected `+` in a string being parsed\r\n\t\t\t// then it simply discards that out-of-place `+` and any following characters.\r\n\t\t\t//\r\n\t\t\tif (typeof emitEvent === 'function') {\r\n\t\t\t\temitEvent('end')\r\n\t\t\t}\r\n\t\t\treturn\r\n\t\t}\r\n\t\treturn '+'\r\n\t}\r\n\t// Allow digits.\r\n\treturn parseDigit(character)\r\n}"], "mappings": ";;;;;;;;AAAA;;;;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAASA,0BAAT,CAAoCC,MAApC,EAA4C;EAC1D,IAAIC,MAAM,GAAG,EAAb,CAD0D,CAE1D;EACA;EACA;EACA;EACA;EACA;;EACA,qDAAwBD,MAAM,CAACE,KAAP,CAAa,EAAb,CAAxB,wCAA0C;IAAA,IAA/BC,SAA+B;IACzCF,MAAM,IAAIG,yBAAyB,CAACD,SAAD,EAAYF,MAAZ,CAAzB,IAAgD,EAA1D;EACA;;EACD,OAAOA,MAAP;AACA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACO,SAASG,yBAAT,CAAmCD,SAAnC,EAA8CE,oBAA9C,EAAoEC,SAApE,EAA+E;EACrF;EACA,IAAIH,SAAS,KAAK,GAAlB,EAAuB;IACtB;IACA;IACA,IAAIE,oBAAJ,EAA0B;MACzB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,OAAOC,SAAP,KAAqB,UAAzB,EAAqC;QACpCA,SAAS,CAAC,KAAD,CAAT;MACA;;MACD;IACA;;IACD,OAAO,GAAP;EACA,CAvBoF,CAwBrF;;;EACA,OAAO,IAAAC,uBAAA,EAAWJ,SAAX,CAAP;AACA"}