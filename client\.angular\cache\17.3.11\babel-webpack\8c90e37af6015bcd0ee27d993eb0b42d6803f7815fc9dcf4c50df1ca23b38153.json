{"ast": null, "code": "import _isValidNumber from '../isValid.js';\nimport { normalizeArguments } from './getNumberType.js'; // Finds out national phone number type (fixed line, mobile, etc)\n\nexport default function isValidNumber() {\n  var _normalizeArguments = normalizeArguments(arguments),\n    input = _normalizeArguments.input,\n    options = _normalizeArguments.options,\n    metadata = _normalizeArguments.metadata; // `parseNumber()` would return `{}` when no phone number could be parsed from the input.\n\n  if (!input.phone) {\n    return false;\n  }\n  return _isValidNumber(input, options, metadata);\n}", "map": {"version": 3, "names": ["_isValidNumber", "normalizeArguments", "isValidNumber", "_normalizeArguments", "arguments", "input", "options", "metadata", "phone"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/legacy/isValidNumber.js"], "sourcesContent": ["import _isValidNumber from '../isValid.js';\nimport { normalizeArguments } from './getNumberType.js'; // Finds out national phone number type (fixed line, mobile, etc)\n\nexport default function isValidNumber() {\n  var _normalizeArguments = normalizeArguments(arguments),\n      input = _normalizeArguments.input,\n      options = _normalizeArguments.options,\n      metadata = _normalizeArguments.metadata; // `parseNumber()` would return `{}` when no phone number could be parsed from the input.\n\n\n  if (!input.phone) {\n    return false;\n  }\n\n  return _isValidNumber(input, options, metadata);\n}\n"], "mappings": "AAAA,OAAOA,cAAc,MAAM,eAAe;AAC1C,SAASC,kBAAkB,QAAQ,oBAAoB,CAAC,CAAC;;AAEzD,eAAe,SAASC,aAAaA,CAAA,EAAG;EACtC,IAAIC,mBAAmB,GAAGF,kBAAkB,CAACG,SAAS,CAAC;IACnDC,KAAK,GAAGF,mBAAmB,CAACE,KAAK;IACjCC,OAAO,GAAGH,mBAAmB,CAACG,OAAO;IACrCC,QAAQ,GAAGJ,mBAAmB,CAACI,QAAQ,CAAC,CAAC;;EAG7C,IAAI,CAACF,KAAK,CAACG,KAAK,EAAE;IAChB,OAAO,KAAK;EACd;EAEA,OAAOR,cAAc,CAACK,KAAK,EAAEC,OAAO,EAAEC,QAAQ,CAAC;AACjD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}