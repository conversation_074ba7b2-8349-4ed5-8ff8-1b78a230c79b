{"ast": null, "code": "import { MIN_LENGTH_FOR_NSN, VALID_DIGITS, VALID_PUNCTUATION, PLUS_CHARS } from '../constants.js';\nimport createExtensionPattern from './extension/createExtensionPattern.js'; //  Regular expression of viable phone numbers. This is location independent.\n//  Checks we have at least three leading digits, and only valid punctuation,\n//  alpha characters and digits in the phone number. Does not include extension\n//  data. The symbol 'x' is allowed here as valid punctuation since it is often\n//  used as a placeholder for carrier codes, for example in Brazilian phone\n//  numbers. We also allow multiple '+' characters at the start.\n//\n//  Corresponds to the following:\n//  [digits]{minLengthNsn}|\n//  plus_sign*\n//  (([punctuation]|[star])*[digits]){3,}([punctuation]|[star]|[digits]|[alpha])*\n//\n//  The first reg-ex is to allow short numbers (two digits long) to be parsed if\n//  they are entered as \"15\" etc, but only if there is no punctuation in them.\n//  The second expression restricts the number of digits to three or more, but\n//  then allows them to be in international form, and to have alpha-characters\n//  and punctuation. We split up the two reg-exes here and combine them when\n//  creating the reg-ex VALID_PHONE_NUMBER_PATTERN itself so we can prefix it\n//  with ^ and append $ to each branch.\n//\n//  \"Note VALID_PUNCTUATION starts with a -,\n//   so must be the first in the range\" (c) Google devs.\n//  (wtf did they mean by saying that; probably nothing)\n//\n\nvar MIN_LENGTH_PHONE_NUMBER_PATTERN = '[' + VALID_DIGITS + ']{' + MIN_LENGTH_FOR_NSN + '}'; //\n// And this is the second reg-exp:\n// (see MIN_LENGTH_PHONE_NUMBER_PATTERN for a full description of this reg-exp)\n//\n\nexport var VALID_PHONE_NUMBER = '[' + PLUS_CHARS + ']{0,1}' + '(?:' + '[' + VALID_PUNCTUATION + ']*' + '[' + VALID_DIGITS + ']' + '){3,}' + '[' + VALID_PUNCTUATION + VALID_DIGITS + ']*'; // This regular expression isn't present in Google's `libphonenumber`\n// and is only used to determine whether the phone number being input\n// is too short for it to even consider it a \"valid\" number.\n// This is just a way to differentiate between a really invalid phone\n// number like \"abcde\" and a valid phone number that a user has just\n// started inputting, like \"+1\" or \"1\": both these cases would be\n// considered `NOT_A_NUMBER` by Google's `libphonenumber`, but this\n// library can provide a more detailed error message — whether it's\n// really \"not a number\", or is it just a start of a valid phone number.\n\nvar VALID_PHONE_NUMBER_START_REG_EXP = new RegExp('^' + '[' + PLUS_CHARS + ']{0,1}' + '(?:' + '[' + VALID_PUNCTUATION + ']*' + '[' + VALID_DIGITS + ']' + '){1,2}' + '$', 'i');\nexport var VALID_PHONE_NUMBER_WITH_EXTENSION = VALID_PHONE_NUMBER +\n// Phone number extensions\n'(?:' + createExtensionPattern() + ')?'; // The combined regular expression for valid phone numbers:\n//\n\nvar VALID_PHONE_NUMBER_PATTERN = new RegExp(\n// Either a short two-digit-only phone number\n'^' + MIN_LENGTH_PHONE_NUMBER_PATTERN + '$' + '|' +\n// Or a longer fully parsed phone number (min 3 characters)\n'^' + VALID_PHONE_NUMBER_WITH_EXTENSION + '$', 'i'); // Checks to see if the string of characters could possibly be a phone number at\n// all. At the moment, checks to see that the string begins with at least 2\n// digits, ignoring any punctuation commonly found in phone numbers. This method\n// does not require the number to be normalized in advance - but does assume\n// that leading non-number symbols have been removed, such as by the method\n// `extract_possible_number`.\n//\n\nexport default function isViablePhoneNumber(number) {\n  return number.length >= MIN_LENGTH_FOR_NSN && VALID_PHONE_NUMBER_PATTERN.test(number);\n} // This is just a way to differentiate between a really invalid phone\n// number like \"abcde\" and a valid phone number that a user has just\n// started inputting, like \"+1\" or \"1\": both these cases would be\n// considered `NOT_A_NUMBER` by Google's `libphonenumber`, but this\n// library can provide a more detailed error message — whether it's\n// really \"not a number\", or is it just a start of a valid phone number.\n\nexport function isViablePhoneNumberStart(number) {\n  return VALID_PHONE_NUMBER_START_REG_EXP.test(number);\n}", "map": {"version": 3, "names": ["MIN_LENGTH_FOR_NSN", "VALID_DIGITS", "VALID_PUNCTUATION", "PLUS_CHARS", "createExtensionPattern", "MIN_LENGTH_PHONE_NUMBER_PATTERN", "VALID_PHONE_NUMBER", "VALID_PHONE_NUMBER_START_REG_EXP", "RegExp", "VALID_PHONE_NUMBER_WITH_EXTENSION", "VALID_PHONE_NUMBER_PATTERN", "isViablePhoneNumber", "number", "length", "test", "isViablePhoneNumberStart"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/helpers/isViablePhoneNumber.js"], "sourcesContent": ["import { MIN_LENGTH_FOR_NSN, VALID_DIGITS, VALID_PUNCTUATION, PLUS_CHARS } from '../constants.js';\nimport createExtensionPattern from './extension/createExtensionPattern.js'; //  Regular expression of viable phone numbers. This is location independent.\n//  Checks we have at least three leading digits, and only valid punctuation,\n//  alpha characters and digits in the phone number. Does not include extension\n//  data. The symbol 'x' is allowed here as valid punctuation since it is often\n//  used as a placeholder for carrier codes, for example in Brazilian phone\n//  numbers. We also allow multiple '+' characters at the start.\n//\n//  Corresponds to the following:\n//  [digits]{minLengthNsn}|\n//  plus_sign*\n//  (([punctuation]|[star])*[digits]){3,}([punctuation]|[star]|[digits]|[alpha])*\n//\n//  The first reg-ex is to allow short numbers (two digits long) to be parsed if\n//  they are entered as \"15\" etc, but only if there is no punctuation in them.\n//  The second expression restricts the number of digits to three or more, but\n//  then allows them to be in international form, and to have alpha-characters\n//  and punctuation. We split up the two reg-exes here and combine them when\n//  creating the reg-ex VALID_PHONE_NUMBER_PATTERN itself so we can prefix it\n//  with ^ and append $ to each branch.\n//\n//  \"Note VALID_PUNCTUATION starts with a -,\n//   so must be the first in the range\" (c) Google devs.\n//  (wtf did they mean by saying that; probably nothing)\n//\n\nvar MIN_LENGTH_PHONE_NUMBER_PATTERN = '[' + VALID_DIGITS + ']{' + MIN_LENGTH_FOR_NSN + '}'; //\n// And this is the second reg-exp:\n// (see MIN_LENGTH_PHONE_NUMBER_PATTERN for a full description of this reg-exp)\n//\n\nexport var VALID_PHONE_NUMBER = '[' + PLUS_CHARS + ']{0,1}' + '(?:' + '[' + VALID_PUNCTUATION + ']*' + '[' + VALID_DIGITS + ']' + '){3,}' + '[' + VALID_PUNCTUATION + VALID_DIGITS + ']*'; // This regular expression isn't present in Google's `libphonenumber`\n// and is only used to determine whether the phone number being input\n// is too short for it to even consider it a \"valid\" number.\n// This is just a way to differentiate between a really invalid phone\n// number like \"abcde\" and a valid phone number that a user has just\n// started inputting, like \"+1\" or \"1\": both these cases would be\n// considered `NOT_A_NUMBER` by Google's `libphonenumber`, but this\n// library can provide a more detailed error message — whether it's\n// really \"not a number\", or is it just a start of a valid phone number.\n\nvar VALID_PHONE_NUMBER_START_REG_EXP = new RegExp('^' + '[' + PLUS_CHARS + ']{0,1}' + '(?:' + '[' + VALID_PUNCTUATION + ']*' + '[' + VALID_DIGITS + ']' + '){1,2}' + '$', 'i');\nexport var VALID_PHONE_NUMBER_WITH_EXTENSION = VALID_PHONE_NUMBER + // Phone number extensions\n'(?:' + createExtensionPattern() + ')?'; // The combined regular expression for valid phone numbers:\n//\n\nvar VALID_PHONE_NUMBER_PATTERN = new RegExp( // Either a short two-digit-only phone number\n'^' + MIN_LENGTH_PHONE_NUMBER_PATTERN + '$' + '|' + // Or a longer fully parsed phone number (min 3 characters)\n'^' + VALID_PHONE_NUMBER_WITH_EXTENSION + '$', 'i'); // Checks to see if the string of characters could possibly be a phone number at\n// all. At the moment, checks to see that the string begins with at least 2\n// digits, ignoring any punctuation commonly found in phone numbers. This method\n// does not require the number to be normalized in advance - but does assume\n// that leading non-number symbols have been removed, such as by the method\n// `extract_possible_number`.\n//\n\nexport default function isViablePhoneNumber(number) {\n  return number.length >= MIN_LENGTH_FOR_NSN && VALID_PHONE_NUMBER_PATTERN.test(number);\n} // This is just a way to differentiate between a really invalid phone\n// number like \"abcde\" and a valid phone number that a user has just\n// started inputting, like \"+1\" or \"1\": both these cases would be\n// considered `NOT_A_NUMBER` by Google's `libphonenumber`, but this\n// library can provide a more detailed error message — whether it's\n// really \"not a number\", or is it just a start of a valid phone number.\n\nexport function isViablePhoneNumberStart(number) {\n  return VALID_PHONE_NUMBER_START_REG_EXP.test(number);\n}\n"], "mappings": "AAAA,SAASA,kBAAkB,EAAEC,YAAY,EAAEC,iBAAiB,EAAEC,UAAU,QAAQ,iBAAiB;AACjG,OAAOC,sBAAsB,MAAM,uCAAuC,CAAC,CAAC;AAC5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,+BAA+B,GAAG,GAAG,GAAGJ,YAAY,GAAG,IAAI,GAAGD,kBAAkB,GAAG,GAAG,CAAC,CAAC;AAC5F;AACA;AACA;;AAEA,OAAO,IAAIM,kBAAkB,GAAG,GAAG,GAAGH,UAAU,GAAG,QAAQ,GAAG,KAAK,GAAG,GAAG,GAAGD,iBAAiB,GAAG,IAAI,GAAG,GAAG,GAAGD,YAAY,GAAG,GAAG,GAAG,OAAO,GAAG,GAAG,GAAGC,iBAAiB,GAAGD,YAAY,GAAG,IAAI,CAAC,CAAC;AAC3L;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIM,gCAAgC,GAAG,IAAIC,MAAM,CAAC,GAAG,GAAG,GAAG,GAAGL,UAAU,GAAG,QAAQ,GAAG,KAAK,GAAG,GAAG,GAAGD,iBAAiB,GAAG,IAAI,GAAG,GAAG,GAAGD,YAAY,GAAG,GAAG,GAAG,QAAQ,GAAG,GAAG,EAAE,GAAG,CAAC;AAC9K,OAAO,IAAIQ,iCAAiC,GAAGH,kBAAkB;AAAG;AACpE,KAAK,GAAGF,sBAAsB,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;AACzC;;AAEA,IAAIM,0BAA0B,GAAG,IAAIF,MAAM;AAAE;AAC7C,GAAG,GAAGH,+BAA+B,GAAG,GAAG,GAAG,GAAG;AAAG;AACpD,GAAG,GAAGI,iCAAiC,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;AACrD;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASE,mBAAmBA,CAACC,MAAM,EAAE;EAClD,OAAOA,MAAM,CAACC,MAAM,IAAIb,kBAAkB,IAAIU,0BAA0B,CAACI,IAAI,CAACF,MAAM,CAAC;AACvF,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASG,wBAAwBA,CAACH,MAAM,EAAE;EAC/C,OAAOL,gCAAgC,CAACO,IAAI,CAACF,MAAM,CAAC;AACtD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}