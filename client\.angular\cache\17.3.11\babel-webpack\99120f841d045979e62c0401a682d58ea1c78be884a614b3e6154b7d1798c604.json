{"ast": null, "code": "function _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n  return arr2;\n}\nfunction _iterableToArrayLimit(arr, i) {\n  var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n  if (_i == null) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n  var _s, _e;\n  try {\n    for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n  return _arr;\n}\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\nimport isViablePhoneNumber from '../helpers/isViablePhoneNumber.js';\nimport _getNumberType from '../helpers/getNumberType.js';\nimport isObject from '../helpers/isObject.js';\nimport parse from '../parse.js'; // Finds out national phone number type (fixed line, mobile, etc)\n\nexport default function getNumberType() {\n  var _normalizeArguments = normalizeArguments(arguments),\n    input = _normalizeArguments.input,\n    options = _normalizeArguments.options,\n    metadata = _normalizeArguments.metadata; // `parseNumber()` would return `{}` when no phone number could be parsed from the input.\n\n  if (!input.phone) {\n    return;\n  }\n  return _getNumberType(input, options, metadata);\n} // Sort out arguments\n\nexport function normalizeArguments(args) {\n  var _Array$prototype$slic = Array.prototype.slice.call(args),\n    _Array$prototype$slic2 = _slicedToArray(_Array$prototype$slic, 4),\n    arg_1 = _Array$prototype$slic2[0],\n    arg_2 = _Array$prototype$slic2[1],\n    arg_3 = _Array$prototype$slic2[2],\n    arg_4 = _Array$prototype$slic2[3];\n  var input;\n  var options = {};\n  var metadata; // If the phone number is passed as a string.\n  // `getNumberType('88005553535', ...)`.\n\n  if (typeof arg_1 === 'string') {\n    // If \"default country\" argument is being passed\n    // then convert it to an `options` object.\n    // `getNumberType('88005553535', 'RU', metadata)`.\n    if (!isObject(arg_2)) {\n      if (arg_4) {\n        options = arg_3;\n        metadata = arg_4;\n      } else {\n        metadata = arg_3;\n      } // `parse` extracts phone numbers from raw text,\n      // therefore it will cut off all \"garbage\" characters,\n      // while this `validate` function needs to verify\n      // that the phone number contains no \"garbage\"\n      // therefore the explicit `isViablePhoneNumber` check.\n\n      if (isViablePhoneNumber(arg_1)) {\n        input = parse(arg_1, {\n          defaultCountry: arg_2\n        }, metadata);\n      } else {\n        input = {};\n      }\n    } // No \"resrict country\" argument is being passed.\n    // International phone number is passed.\n    // `getNumberType('+78005553535', metadata)`.\n    else {\n      if (arg_3) {\n        options = arg_2;\n        metadata = arg_3;\n      } else {\n        metadata = arg_2;\n      } // `parse` extracts phone numbers from raw text,\n      // therefore it will cut off all \"garbage\" characters,\n      // while this `validate` function needs to verify\n      // that the phone number contains no \"garbage\"\n      // therefore the explicit `isViablePhoneNumber` check.\n\n      if (isViablePhoneNumber(arg_1)) {\n        input = parse(arg_1, undefined, metadata);\n      } else {\n        input = {};\n      }\n    }\n  } // If the phone number is passed as a parsed phone number.\n  // `getNumberType({ phone: '88005553535', country: 'RU' }, ...)`.\n  else if (isObject(arg_1)) {\n    input = arg_1;\n    if (arg_3) {\n      options = arg_2;\n      metadata = arg_3;\n    } else {\n      metadata = arg_2;\n    }\n  } else throw new TypeError('A phone number must either be a string or an object of shape { phone, [country] }.');\n  return {\n    input: input,\n    options: options,\n    metadata: metadata\n  };\n}", "map": {"version": 3, "names": ["_slicedToArray", "arr", "i", "_arrayWithHoles", "_iterableToArrayLimit", "_unsupportedIterableToArray", "_nonIterableRest", "TypeError", "o", "minLen", "_arrayLikeToArray", "n", "Object", "prototype", "toString", "call", "slice", "constructor", "name", "Array", "from", "test", "len", "length", "arr2", "_i", "Symbol", "iterator", "_arr", "_n", "_d", "_s", "_e", "next", "done", "push", "value", "err", "isArray", "isViablePhoneNumber", "_getNumberType", "isObject", "parse", "getNumberType", "_normalizeArguments", "normalizeArguments", "arguments", "input", "options", "metadata", "phone", "args", "_Array$prototype$slic", "_Array$prototype$slic2", "arg_1", "arg_2", "arg_3", "arg_4", "defaultCountry", "undefined"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/legacy/getNumberType.js"], "sourcesContent": ["function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\nimport isViablePhoneNumber from '../helpers/isViablePhoneNumber.js';\nimport _getNumberType from '../helpers/getNumberType.js';\nimport isObject from '../helpers/isObject.js';\nimport parse from '../parse.js'; // Finds out national phone number type (fixed line, mobile, etc)\n\nexport default function getNumberType() {\n  var _normalizeArguments = normalizeArguments(arguments),\n      input = _normalizeArguments.input,\n      options = _normalizeArguments.options,\n      metadata = _normalizeArguments.metadata; // `parseNumber()` would return `{}` when no phone number could be parsed from the input.\n\n\n  if (!input.phone) {\n    return;\n  }\n\n  return _getNumberType(input, options, metadata);\n} // Sort out arguments\n\nexport function normalizeArguments(args) {\n  var _Array$prototype$slic = Array.prototype.slice.call(args),\n      _Array$prototype$slic2 = _slicedToArray(_Array$prototype$slic, 4),\n      arg_1 = _Array$prototype$slic2[0],\n      arg_2 = _Array$prototype$slic2[1],\n      arg_3 = _Array$prototype$slic2[2],\n      arg_4 = _Array$prototype$slic2[3];\n\n  var input;\n  var options = {};\n  var metadata; // If the phone number is passed as a string.\n  // `getNumberType('88005553535', ...)`.\n\n  if (typeof arg_1 === 'string') {\n    // If \"default country\" argument is being passed\n    // then convert it to an `options` object.\n    // `getNumberType('88005553535', 'RU', metadata)`.\n    if (!isObject(arg_2)) {\n      if (arg_4) {\n        options = arg_3;\n        metadata = arg_4;\n      } else {\n        metadata = arg_3;\n      } // `parse` extracts phone numbers from raw text,\n      // therefore it will cut off all \"garbage\" characters,\n      // while this `validate` function needs to verify\n      // that the phone number contains no \"garbage\"\n      // therefore the explicit `isViablePhoneNumber` check.\n\n\n      if (isViablePhoneNumber(arg_1)) {\n        input = parse(arg_1, {\n          defaultCountry: arg_2\n        }, metadata);\n      } else {\n        input = {};\n      }\n    } // No \"resrict country\" argument is being passed.\n    // International phone number is passed.\n    // `getNumberType('+78005553535', metadata)`.\n    else {\n      if (arg_3) {\n        options = arg_2;\n        metadata = arg_3;\n      } else {\n        metadata = arg_2;\n      } // `parse` extracts phone numbers from raw text,\n      // therefore it will cut off all \"garbage\" characters,\n      // while this `validate` function needs to verify\n      // that the phone number contains no \"garbage\"\n      // therefore the explicit `isViablePhoneNumber` check.\n\n\n      if (isViablePhoneNumber(arg_1)) {\n        input = parse(arg_1, undefined, metadata);\n      } else {\n        input = {};\n      }\n    }\n  } // If the phone number is passed as a parsed phone number.\n  // `getNumberType({ phone: '88005553535', country: 'RU' }, ...)`.\n  else if (isObject(arg_1)) {\n    input = arg_1;\n\n    if (arg_3) {\n      options = arg_2;\n      metadata = arg_3;\n    } else {\n      metadata = arg_2;\n    }\n  } else throw new TypeError('A phone number must either be a string or an object of shape { phone, [country] }.');\n\n  return {\n    input: input,\n    options: options,\n    metadata: metadata\n  };\n}\n"], "mappings": "AAAA,SAASA,cAAcA,CAACC,GAAG,EAAEC,CAAC,EAAE;EAAE,OAAOC,eAAe,CAACF,GAAG,CAAC,IAAIG,qBAAqB,CAACH,GAAG,EAAEC,CAAC,CAAC,IAAIG,2BAA2B,CAACJ,GAAG,EAAEC,CAAC,CAAC,IAAII,gBAAgB,CAAC,CAAC;AAAE;AAE7J,SAASA,gBAAgBA,CAAA,EAAG;EAAE,MAAM,IAAIC,SAAS,CAAC,2IAA2I,CAAC;AAAE;AAEhM,SAASF,2BAA2BA,CAACG,CAAC,EAAEC,MAAM,EAAE;EAAE,IAAI,CAACD,CAAC,EAAE;EAAQ,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOE,iBAAiB,CAACF,CAAC,EAAEC,MAAM,CAAC;EAAE,IAAIE,CAAC,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACP,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAE,IAAIL,CAAC,KAAK,QAAQ,IAAIH,CAAC,CAACS,WAAW,EAAEN,CAAC,GAAGH,CAAC,CAACS,WAAW,CAACC,IAAI;EAAE,IAAIP,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOQ,KAAK,CAACC,IAAI,CAACZ,CAAC,CAAC;EAAE,IAAIG,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACU,IAAI,CAACV,CAAC,CAAC,EAAE,OAAOD,iBAAiB,CAACF,CAAC,EAAEC,MAAM,CAAC;AAAE;AAE/Z,SAASC,iBAAiBA,CAACT,GAAG,EAAEqB,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGrB,GAAG,CAACsB,MAAM,EAAED,GAAG,GAAGrB,GAAG,CAACsB,MAAM;EAAE,KAAK,IAAIrB,CAAC,GAAG,CAAC,EAAEsB,IAAI,GAAG,IAAIL,KAAK,CAACG,GAAG,CAAC,EAAEpB,CAAC,GAAGoB,GAAG,EAAEpB,CAAC,EAAE,EAAE;IAAEsB,IAAI,CAACtB,CAAC,CAAC,GAAGD,GAAG,CAACC,CAAC,CAAC;EAAE;EAAE,OAAOsB,IAAI;AAAE;AAEtL,SAASpB,qBAAqBA,CAACH,GAAG,EAAEC,CAAC,EAAE;EAAE,IAAIuB,EAAE,GAAGxB,GAAG,IAAI,IAAI,GAAG,IAAI,GAAG,OAAOyB,MAAM,KAAK,WAAW,IAAIzB,GAAG,CAACyB,MAAM,CAACC,QAAQ,CAAC,IAAI1B,GAAG,CAAC,YAAY,CAAC;EAAE,IAAIwB,EAAE,IAAI,IAAI,EAAE;EAAQ,IAAIG,IAAI,GAAG,EAAE;EAAE,IAAIC,EAAE,GAAG,IAAI;EAAE,IAAIC,EAAE,GAAG,KAAK;EAAE,IAAIC,EAAE,EAAEC,EAAE;EAAE,IAAI;IAAE,KAAKP,EAAE,GAAGA,EAAE,CAACV,IAAI,CAACd,GAAG,CAAC,EAAE,EAAE4B,EAAE,GAAG,CAACE,EAAE,GAAGN,EAAE,CAACQ,IAAI,CAAC,CAAC,EAAEC,IAAI,CAAC,EAAEL,EAAE,GAAG,IAAI,EAAE;MAAED,IAAI,CAACO,IAAI,CAACJ,EAAE,CAACK,KAAK,CAAC;MAAE,IAAIlC,CAAC,IAAI0B,IAAI,CAACL,MAAM,KAAKrB,CAAC,EAAE;IAAO;EAAE,CAAC,CAAC,OAAOmC,GAAG,EAAE;IAAEP,EAAE,GAAG,IAAI;IAAEE,EAAE,GAAGK,GAAG;EAAE,CAAC,SAAS;IAAE,IAAI;MAAE,IAAI,CAACR,EAAE,IAAIJ,EAAE,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAEA,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAAE,CAAC,SAAS;MAAE,IAAIK,EAAE,EAAE,MAAME,EAAE;IAAE;EAAE;EAAE,OAAOJ,IAAI;AAAE;AAEhgB,SAASzB,eAAeA,CAACF,GAAG,EAAE;EAAE,IAAIkB,KAAK,CAACmB,OAAO,CAACrC,GAAG,CAAC,EAAE,OAAOA,GAAG;AAAE;AAEpE,OAAOsC,mBAAmB,MAAM,mCAAmC;AACnE,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAOC,KAAK,MAAM,aAAa,CAAC,CAAC;;AAEjC,eAAe,SAASC,aAAaA,CAAA,EAAG;EACtC,IAAIC,mBAAmB,GAAGC,kBAAkB,CAACC,SAAS,CAAC;IACnDC,KAAK,GAAGH,mBAAmB,CAACG,KAAK;IACjCC,OAAO,GAAGJ,mBAAmB,CAACI,OAAO;IACrCC,QAAQ,GAAGL,mBAAmB,CAACK,QAAQ,CAAC,CAAC;;EAG7C,IAAI,CAACF,KAAK,CAACG,KAAK,EAAE;IAChB;EACF;EAEA,OAAOV,cAAc,CAACO,KAAK,EAAEC,OAAO,EAAEC,QAAQ,CAAC;AACjD,CAAC,CAAC;;AAEF,OAAO,SAASJ,kBAAkBA,CAACM,IAAI,EAAE;EACvC,IAAIC,qBAAqB,GAAGjC,KAAK,CAACN,SAAS,CAACG,KAAK,CAACD,IAAI,CAACoC,IAAI,CAAC;IACxDE,sBAAsB,GAAGrD,cAAc,CAACoD,qBAAqB,EAAE,CAAC,CAAC;IACjEE,KAAK,GAAGD,sBAAsB,CAAC,CAAC,CAAC;IACjCE,KAAK,GAAGF,sBAAsB,CAAC,CAAC,CAAC;IACjCG,KAAK,GAAGH,sBAAsB,CAAC,CAAC,CAAC;IACjCI,KAAK,GAAGJ,sBAAsB,CAAC,CAAC,CAAC;EAErC,IAAIN,KAAK;EACT,IAAIC,OAAO,GAAG,CAAC,CAAC;EAChB,IAAIC,QAAQ,CAAC,CAAC;EACd;;EAEA,IAAI,OAAOK,KAAK,KAAK,QAAQ,EAAE;IAC7B;IACA;IACA;IACA,IAAI,CAACb,QAAQ,CAACc,KAAK,CAAC,EAAE;MACpB,IAAIE,KAAK,EAAE;QACTT,OAAO,GAAGQ,KAAK;QACfP,QAAQ,GAAGQ,KAAK;MAClB,CAAC,MAAM;QACLR,QAAQ,GAAGO,KAAK;MAClB,CAAC,CAAC;MACF;MACA;MACA;MACA;;MAGA,IAAIjB,mBAAmB,CAACe,KAAK,CAAC,EAAE;QAC9BP,KAAK,GAAGL,KAAK,CAACY,KAAK,EAAE;UACnBI,cAAc,EAAEH;QAClB,CAAC,EAAEN,QAAQ,CAAC;MACd,CAAC,MAAM;QACLF,KAAK,GAAG,CAAC,CAAC;MACZ;IACF,CAAC,CAAC;IACF;IACA;IAAA,KACK;MACH,IAAIS,KAAK,EAAE;QACTR,OAAO,GAAGO,KAAK;QACfN,QAAQ,GAAGO,KAAK;MAClB,CAAC,MAAM;QACLP,QAAQ,GAAGM,KAAK;MAClB,CAAC,CAAC;MACF;MACA;MACA;MACA;;MAGA,IAAIhB,mBAAmB,CAACe,KAAK,CAAC,EAAE;QAC9BP,KAAK,GAAGL,KAAK,CAACY,KAAK,EAAEK,SAAS,EAAEV,QAAQ,CAAC;MAC3C,CAAC,MAAM;QACLF,KAAK,GAAG,CAAC,CAAC;MACZ;IACF;EACF,CAAC,CAAC;EACF;EAAA,KACK,IAAIN,QAAQ,CAACa,KAAK,CAAC,EAAE;IACxBP,KAAK,GAAGO,KAAK;IAEb,IAAIE,KAAK,EAAE;MACTR,OAAO,GAAGO,KAAK;MACfN,QAAQ,GAAGO,KAAK;IAClB,CAAC,MAAM;MACLP,QAAQ,GAAGM,KAAK;IAClB;EACF,CAAC,MAAM,MAAM,IAAIhD,SAAS,CAAC,oFAAoF,CAAC;EAEhH,OAAO;IACLwC,KAAK,EAAEA,KAAK;IACZC,OAAO,EAAEA,OAAO;IAChBC,QAAQ,EAAEA;EACZ,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}