{"ast": null, "code": "function _createForOfIteratorHelperLoose(o, allowArrayLike) {\n  var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"];\n  if (it) return (it = it.call(o)).next.bind(it);\n  if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") {\n    if (it) o = it;\n    var i = 0;\n    return function () {\n      if (i >= o.length) return {\n        done: true\n      };\n      return {\n        done: false,\n        value: o[i++]\n      };\n    };\n  }\n  throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n  return arr2;\n}\nimport Metadata from '../metadata.js';\nimport getNumberType from './getNumberType.js';\nexport default function getCountryByNationalNumber(nationalPhoneNumber, _ref) {\n  var countries = _ref.countries,\n    defaultCountry = _ref.defaultCountry,\n    metadata = _ref.metadata;\n  // Re-create `metadata` because it will be selecting a `country`.\n  metadata = new Metadata(metadata); // const matchingCountries = []\n\n  for (var _iterator = _createForOfIteratorHelperLoose(countries), _step; !(_step = _iterator()).done;) {\n    var country = _step.value;\n    metadata.country(country); // \"Leading digits\" patterns are only defined for about 20% of all countries.\n    // By definition, matching \"leading digits\" is a sufficient but not a necessary\n    // condition for a phone number to belong to a country.\n    // The point of \"leading digits\" check is that it's the fastest one to get a match.\n    // https://gitlab.com/catamphetamine/libphonenumber-js/blob/master/METADATA.md#leading_digits\n    // I'd suppose that \"leading digits\" patterns are mutually exclusive for different countries\n    // because of the intended use of that feature.\n\n    if (metadata.leadingDigits()) {\n      if (nationalPhoneNumber && nationalPhoneNumber.search(metadata.leadingDigits()) === 0) {\n        return country;\n      }\n    } // Else perform full validation with all of those\n    // fixed-line/mobile/etc regular expressions.\n    else if (getNumberType({\n      phone: nationalPhoneNumber,\n      country: country\n    }, undefined, metadata.metadata)) {\n      // If both the `defaultCountry` and the \"main\" one match the phone number,\n      // don't prefer the `defaultCountry` over the \"main\" one.\n      // https://gitlab.com/catamphetamine/libphonenumber-js/-/issues/154\n      return country; // // If the `defaultCountry` is among the `matchingCountries` then return it.\n      // if (defaultCountry) {\n      // \tif (country === defaultCountry) {\n      // \t\treturn country\n      // \t}\n      // \tmatchingCountries.push(country)\n      // } else {\n      // \treturn country\n      // }\n    }\n  } // // Return the first (\"main\") one of the `matchingCountries`.\n  // if (matchingCountries.length > 0) {\n  // \treturn matchingCountries[0]\n  // }\n}", "map": {"version": 3, "names": ["_createForOfIteratorHelperLoose", "o", "allowArrayLike", "it", "Symbol", "iterator", "call", "next", "bind", "Array", "isArray", "_unsupportedIterableToArray", "length", "i", "done", "value", "TypeError", "minLen", "_arrayLikeToArray", "n", "Object", "prototype", "toString", "slice", "constructor", "name", "from", "test", "arr", "len", "arr2", "<PERSON><PERSON><PERSON>", "getNumberType", "getCountryByNationalNumber", "nationalPhoneNumber", "_ref", "countries", "defaultCountry", "metadata", "_iterator", "_step", "country", "leadingDigits", "search", "phone", "undefined"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/helpers/getCountryByNationalNumber.js"], "sourcesContent": ["function _createForOfIteratorHelperLoose(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (it) return (it = it.call(o)).next.bind(it); if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; return function () { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nimport Metadata from '../metadata.js';\nimport getNumberType from './getNumberType.js';\nexport default function getCountryByNationalNumber(nationalPhoneNumber, _ref) {\n  var countries = _ref.countries,\n      defaultCountry = _ref.defaultCountry,\n      metadata = _ref.metadata;\n  // Re-create `metadata` because it will be selecting a `country`.\n  metadata = new Metadata(metadata); // const matchingCountries = []\n\n  for (var _iterator = _createForOfIteratorHelperLoose(countries), _step; !(_step = _iterator()).done;) {\n    var country = _step.value;\n    metadata.country(country); // \"Leading digits\" patterns are only defined for about 20% of all countries.\n    // By definition, matching \"leading digits\" is a sufficient but not a necessary\n    // condition for a phone number to belong to a country.\n    // The point of \"leading digits\" check is that it's the fastest one to get a match.\n    // https://gitlab.com/catamphetamine/libphonenumber-js/blob/master/METADATA.md#leading_digits\n    // I'd suppose that \"leading digits\" patterns are mutually exclusive for different countries\n    // because of the intended use of that feature.\n\n    if (metadata.leadingDigits()) {\n      if (nationalPhoneNumber && nationalPhoneNumber.search(metadata.leadingDigits()) === 0) {\n        return country;\n      }\n    } // Else perform full validation with all of those\n    // fixed-line/mobile/etc regular expressions.\n    else if (getNumberType({\n      phone: nationalPhoneNumber,\n      country: country\n    }, undefined, metadata.metadata)) {\n      // If both the `defaultCountry` and the \"main\" one match the phone number,\n      // don't prefer the `defaultCountry` over the \"main\" one.\n      // https://gitlab.com/catamphetamine/libphonenumber-js/-/issues/154\n      return country; // // If the `defaultCountry` is among the `matchingCountries` then return it.\n      // if (defaultCountry) {\n      // \tif (country === defaultCountry) {\n      // \t\treturn country\n      // \t}\n      // \tmatchingCountries.push(country)\n      // } else {\n      // \treturn country\n      // }\n    }\n  } // // Return the first (\"main\") one of the `matchingCountries`.\n  // if (matchingCountries.length > 0) {\n  // \treturn matchingCountries[0]\n  // }\n\n}\n"], "mappings": "AAAA,SAASA,+BAA+BA,CAACC,CAAC,EAAEC,cAAc,EAAE;EAAE,IAAIC,EAAE,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAIH,CAAC,CAACG,MAAM,CAACC,QAAQ,CAAC,IAAIJ,CAAC,CAAC,YAAY,CAAC;EAAE,IAAIE,EAAE,EAAE,OAAO,CAACA,EAAE,GAAGA,EAAE,CAACG,IAAI,CAACL,CAAC,CAAC,EAAEM,IAAI,CAACC,IAAI,CAACL,EAAE,CAAC;EAAE,IAAIM,KAAK,CAACC,OAAO,CAACT,CAAC,CAAC,KAAKE,EAAE,GAAGQ,2BAA2B,CAACV,CAAC,CAAC,CAAC,IAAIC,cAAc,IAAID,CAAC,IAAI,OAAOA,CAAC,CAACW,MAAM,KAAK,QAAQ,EAAE;IAAE,IAAIT,EAAE,EAAEF,CAAC,GAAGE,EAAE;IAAE,IAAIU,CAAC,GAAG,CAAC;IAAE,OAAO,YAAY;MAAE,IAAIA,CAAC,IAAIZ,CAAC,CAACW,MAAM,EAAE,OAAO;QAAEE,IAAI,EAAE;MAAK,CAAC;MAAE,OAAO;QAAEA,IAAI,EAAE,KAAK;QAAEC,KAAK,EAAEd,CAAC,CAACY,CAAC,EAAE;MAAE,CAAC;IAAE,CAAC;EAAE;EAAE,MAAM,IAAIG,SAAS,CAAC,uIAAuI,CAAC;AAAE;AAE3lB,SAASL,2BAA2BA,CAACV,CAAC,EAAEgB,MAAM,EAAE;EAAE,IAAI,CAAChB,CAAC,EAAE;EAAQ,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOiB,iBAAiB,CAACjB,CAAC,EAAEgB,MAAM,CAAC;EAAE,IAAIE,CAAC,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAAChB,IAAI,CAACL,CAAC,CAAC,CAACsB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAE,IAAIJ,CAAC,KAAK,QAAQ,IAAIlB,CAAC,CAACuB,WAAW,EAAEL,CAAC,GAAGlB,CAAC,CAACuB,WAAW,CAACC,IAAI;EAAE,IAAIN,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOV,KAAK,CAACiB,IAAI,CAACzB,CAAC,CAAC;EAAE,IAAIkB,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACQ,IAAI,CAACR,CAAC,CAAC,EAAE,OAAOD,iBAAiB,CAACjB,CAAC,EAAEgB,MAAM,CAAC;AAAE;AAE/Z,SAASC,iBAAiBA,CAACU,GAAG,EAAEC,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGD,GAAG,CAAChB,MAAM,EAAEiB,GAAG,GAAGD,GAAG,CAAChB,MAAM;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEiB,IAAI,GAAG,IAAIrB,KAAK,CAACoB,GAAG,CAAC,EAAEhB,CAAC,GAAGgB,GAAG,EAAEhB,CAAC,EAAE,EAAE;IAAEiB,IAAI,CAACjB,CAAC,CAAC,GAAGe,GAAG,CAACf,CAAC,CAAC;EAAE;EAAE,OAAOiB,IAAI;AAAE;AAEtL,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,eAAe,SAASC,0BAA0BA,CAACC,mBAAmB,EAAEC,IAAI,EAAE;EAC5E,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC1BC,cAAc,GAAGF,IAAI,CAACE,cAAc;IACpCC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;EAC5B;EACAA,QAAQ,GAAG,IAAIP,QAAQ,CAACO,QAAQ,CAAC,CAAC,CAAC;;EAEnC,KAAK,IAAIC,SAAS,GAAGvC,+BAA+B,CAACoC,SAAS,CAAC,EAAEI,KAAK,EAAE,CAAC,CAACA,KAAK,GAAGD,SAAS,CAAC,CAAC,EAAEzB,IAAI,GAAG;IACpG,IAAI2B,OAAO,GAAGD,KAAK,CAACzB,KAAK;IACzBuB,QAAQ,CAACG,OAAO,CAACA,OAAO,CAAC,CAAC,CAAC;IAC3B;IACA;IACA;IACA;IACA;IACA;;IAEA,IAAIH,QAAQ,CAACI,aAAa,CAAC,CAAC,EAAE;MAC5B,IAAIR,mBAAmB,IAAIA,mBAAmB,CAACS,MAAM,CAACL,QAAQ,CAACI,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;QACrF,OAAOD,OAAO;MAChB;IACF,CAAC,CAAC;IACF;IAAA,KACK,IAAIT,aAAa,CAAC;MACrBY,KAAK,EAAEV,mBAAmB;MAC1BO,OAAO,EAAEA;IACX,CAAC,EAAEI,SAAS,EAAEP,QAAQ,CAACA,QAAQ,CAAC,EAAE;MAChC;MACA;MACA;MACA,OAAOG,OAAO,CAAC,CAAC;MAChB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACF;EACF,CAAC,CAAC;EACF;EACA;EACA;AAEF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}