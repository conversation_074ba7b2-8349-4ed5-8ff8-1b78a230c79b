{"ast": null, "code": "// Importing from a \".js\" file is a workaround for Node.js \"ES Modules\"\n// importing system which is even uncapable of importing \"*.json\" files.\nimport metadata from '../../metadata.min.json.js';\nexport default function withMetadataArgument(func, _arguments) {\n  var args = Array.prototype.slice.call(_arguments);\n  args.push(metadata);\n  return func.apply(this, args);\n}", "map": {"version": 3, "names": ["metadata", "withMetadataArgument", "func", "_arguments", "args", "Array", "prototype", "slice", "call", "push", "apply"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/min/exports/withMetadataArgument.js"], "sourcesContent": ["// Importing from a \".js\" file is a workaround for Node.js \"ES Modules\"\r\n// importing system which is even uncapable of importing \"*.json\" files.\r\nimport metadata from '../../metadata.min.json.js'\r\n\r\nexport default function withMetadataArgument(func, _arguments) {\r\n\tvar args = Array.prototype.slice.call(_arguments)\r\n\targs.push(metadata)\r\n\treturn func.apply(this, args)\r\n}"], "mappings": "AAAA;AACA;AACA,OAAOA,QAAQ,MAAM,4BAA4B;AAEjD,eAAe,SAASC,oBAAoBA,CAACC,IAAI,EAAEC,UAAU,EAAE;EAC9D,IAAIC,IAAI,GAAGC,KAAK,CAACC,SAAS,CAACC,KAAK,CAACC,IAAI,CAACL,UAAU,CAAC;EACjDC,IAAI,CAACK,IAAI,CAACT,QAAQ,CAAC;EACnB,OAAOE,IAAI,CAACQ,KAAK,CAAC,IAAI,EAAEN,IAAI,CAAC;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}