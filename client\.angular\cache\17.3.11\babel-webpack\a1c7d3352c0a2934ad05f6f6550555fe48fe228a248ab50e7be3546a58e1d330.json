{"ast": null, "code": "function _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _iterableToArrayLimit(arr, i) {\n  var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n  if (_i == null) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n  var _s, _e;\n  try {\n    for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n  return _arr;\n}\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\nfunction _createForOfIteratorHelperLoose(o, allowArrayLike) {\n  var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"];\n  if (it) return (it = it.call(o)).next.bind(it);\n  if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") {\n    if (it) o = it;\n    var i = 0;\n    return function () {\n      if (i >= o.length) return {\n        done: true\n      };\n      return {\n        done: false,\n        value: o[i++]\n      };\n    };\n  }\n  throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n  return arr2;\n}\nimport isViablePhoneNumber from './isViablePhoneNumber.js'; // https://www.ietf.org/rfc/rfc3966.txt\n\n/**\r\n * @param  {string} text - Phone URI (RFC 3966).\r\n * @return {object} `{ ?number, ?ext }`.\r\n */\n\nexport function parseRFC3966(text) {\n  var number;\n  var ext; // Replace \"tel:\" with \"tel=\" for parsing convenience.\n\n  text = text.replace(/^tel:/, 'tel=');\n  for (var _iterator = _createForOfIteratorHelperLoose(text.split(';')), _step; !(_step = _iterator()).done;) {\n    var part = _step.value;\n    var _part$split = part.split('='),\n      _part$split2 = _slicedToArray(_part$split, 2),\n      name = _part$split2[0],\n      value = _part$split2[1];\n    switch (name) {\n      case 'tel':\n        number = value;\n        break;\n      case 'ext':\n        ext = value;\n        break;\n      case 'phone-context':\n        // Only \"country contexts\" are supported.\n        // \"Domain contexts\" are ignored.\n        if (value[0] === '+') {\n          number = value + number;\n        }\n        break;\n    }\n  } // If the phone number is not viable, then abort.\n\n  if (!isViablePhoneNumber(number)) {\n    return {};\n  }\n  var result = {\n    number: number\n  };\n  if (ext) {\n    result.ext = ext;\n  }\n  return result;\n}\n/**\r\n * @param  {object} - `{ ?number, ?extension }`.\r\n * @return {string} Phone URI (RFC 3966).\r\n */\n\nexport function formatRFC3966(_ref) {\n  var number = _ref.number,\n    ext = _ref.ext;\n  if (!number) {\n    return '';\n  }\n  if (number[0] !== '+') {\n    throw new Error(\"\\\"formatRFC3966()\\\" expects \\\"number\\\" to be in E.164 format.\");\n  }\n  return \"tel:\".concat(number).concat(ext ? ';ext=' + ext : '');\n}", "map": {"version": 3, "names": ["_slicedToArray", "arr", "i", "_arrayWithHoles", "_iterableToArrayLimit", "_unsupportedIterableToArray", "_nonIterableRest", "TypeError", "_i", "Symbol", "iterator", "_arr", "_n", "_d", "_s", "_e", "call", "next", "done", "push", "value", "length", "err", "Array", "isArray", "_createForOfIteratorHelperLoose", "o", "allowArrayLike", "it", "bind", "minLen", "_arrayLikeToArray", "n", "Object", "prototype", "toString", "slice", "constructor", "name", "from", "test", "len", "arr2", "isViablePhoneNumber", "parseRFC3966", "text", "number", "ext", "replace", "_iterator", "split", "_step", "part", "_part$split", "_part$split2", "result", "formatRFC3966", "_ref", "Error", "concat"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/helpers/RFC3966.js"], "sourcesContent": ["function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\nfunction _createForOfIteratorHelperLoose(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (it) return (it = it.call(o)).next.bind(it); if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; return function () { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nimport isViablePhoneNumber from './isViablePhoneNumber.js'; // https://www.ietf.org/rfc/rfc3966.txt\n\n/**\r\n * @param  {string} text - Phone URI (RFC 3966).\r\n * @return {object} `{ ?number, ?ext }`.\r\n */\n\nexport function parseRFC3966(text) {\n  var number;\n  var ext; // Replace \"tel:\" with \"tel=\" for parsing convenience.\n\n  text = text.replace(/^tel:/, 'tel=');\n\n  for (var _iterator = _createForOfIteratorHelperLoose(text.split(';')), _step; !(_step = _iterator()).done;) {\n    var part = _step.value;\n\n    var _part$split = part.split('='),\n        _part$split2 = _slicedToArray(_part$split, 2),\n        name = _part$split2[0],\n        value = _part$split2[1];\n\n    switch (name) {\n      case 'tel':\n        number = value;\n        break;\n\n      case 'ext':\n        ext = value;\n        break;\n\n      case 'phone-context':\n        // Only \"country contexts\" are supported.\n        // \"Domain contexts\" are ignored.\n        if (value[0] === '+') {\n          number = value + number;\n        }\n\n        break;\n    }\n  } // If the phone number is not viable, then abort.\n\n\n  if (!isViablePhoneNumber(number)) {\n    return {};\n  }\n\n  var result = {\n    number: number\n  };\n\n  if (ext) {\n    result.ext = ext;\n  }\n\n  return result;\n}\n/**\r\n * @param  {object} - `{ ?number, ?extension }`.\r\n * @return {string} Phone URI (RFC 3966).\r\n */\n\nexport function formatRFC3966(_ref) {\n  var number = _ref.number,\n      ext = _ref.ext;\n\n  if (!number) {\n    return '';\n  }\n\n  if (number[0] !== '+') {\n    throw new Error(\"\\\"formatRFC3966()\\\" expects \\\"number\\\" to be in E.164 format.\");\n  }\n\n  return \"tel:\".concat(number).concat(ext ? ';ext=' + ext : '');\n}\n"], "mappings": "AAAA,SAASA,cAAcA,CAACC,GAAG,EAAEC,CAAC,EAAE;EAAE,OAAOC,eAAe,CAACF,GAAG,CAAC,IAAIG,qBAAqB,CAACH,GAAG,EAAEC,CAAC,CAAC,IAAIG,2BAA2B,CAACJ,GAAG,EAAEC,CAAC,CAAC,IAAII,gBAAgB,CAAC,CAAC;AAAE;AAE7J,SAASA,gBAAgBA,CAAA,EAAG;EAAE,MAAM,IAAIC,SAAS,CAAC,2IAA2I,CAAC;AAAE;AAEhM,SAASH,qBAAqBA,CAACH,GAAG,EAAEC,CAAC,EAAE;EAAE,IAAIM,EAAE,GAAGP,GAAG,IAAI,IAAI,GAAG,IAAI,GAAG,OAAOQ,MAAM,KAAK,WAAW,IAAIR,GAAG,CAACQ,MAAM,CAACC,QAAQ,CAAC,IAAIT,GAAG,CAAC,YAAY,CAAC;EAAE,IAAIO,EAAE,IAAI,IAAI,EAAE;EAAQ,IAAIG,IAAI,GAAG,EAAE;EAAE,IAAIC,EAAE,GAAG,IAAI;EAAE,IAAIC,EAAE,GAAG,KAAK;EAAE,IAAIC,EAAE,EAAEC,EAAE;EAAE,IAAI;IAAE,KAAKP,EAAE,GAAGA,EAAE,CAACQ,IAAI,CAACf,GAAG,CAAC,EAAE,EAAEW,EAAE,GAAG,CAACE,EAAE,GAAGN,EAAE,CAACS,IAAI,CAAC,CAAC,EAAEC,IAAI,CAAC,EAAEN,EAAE,GAAG,IAAI,EAAE;MAAED,IAAI,CAACQ,IAAI,CAACL,EAAE,CAACM,KAAK,CAAC;MAAE,IAAIlB,CAAC,IAAIS,IAAI,CAACU,MAAM,KAAKnB,CAAC,EAAE;IAAO;EAAE,CAAC,CAAC,OAAOoB,GAAG,EAAE;IAAET,EAAE,GAAG,IAAI;IAAEE,EAAE,GAAGO,GAAG;EAAE,CAAC,SAAS;IAAE,IAAI;MAAE,IAAI,CAACV,EAAE,IAAIJ,EAAE,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAEA,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAAE,CAAC,SAAS;MAAE,IAAIK,EAAE,EAAE,MAAME,EAAE;IAAE;EAAE;EAAE,OAAOJ,IAAI;AAAE;AAEhgB,SAASR,eAAeA,CAACF,GAAG,EAAE;EAAE,IAAIsB,KAAK,CAACC,OAAO,CAACvB,GAAG,CAAC,EAAE,OAAOA,GAAG;AAAE;AAEpE,SAASwB,+BAA+BA,CAACC,CAAC,EAAEC,cAAc,EAAE;EAAE,IAAIC,EAAE,GAAG,OAAOnB,MAAM,KAAK,WAAW,IAAIiB,CAAC,CAACjB,MAAM,CAACC,QAAQ,CAAC,IAAIgB,CAAC,CAAC,YAAY,CAAC;EAAE,IAAIE,EAAE,EAAE,OAAO,CAACA,EAAE,GAAGA,EAAE,CAACZ,IAAI,CAACU,CAAC,CAAC,EAAET,IAAI,CAACY,IAAI,CAACD,EAAE,CAAC;EAAE,IAAIL,KAAK,CAACC,OAAO,CAACE,CAAC,CAAC,KAAKE,EAAE,GAAGvB,2BAA2B,CAACqB,CAAC,CAAC,CAAC,IAAIC,cAAc,IAAID,CAAC,IAAI,OAAOA,CAAC,CAACL,MAAM,KAAK,QAAQ,EAAE;IAAE,IAAIO,EAAE,EAAEF,CAAC,GAAGE,EAAE;IAAE,IAAI1B,CAAC,GAAG,CAAC;IAAE,OAAO,YAAY;MAAE,IAAIA,CAAC,IAAIwB,CAAC,CAACL,MAAM,EAAE,OAAO;QAAEH,IAAI,EAAE;MAAK,CAAC;MAAE,OAAO;QAAEA,IAAI,EAAE,KAAK;QAAEE,KAAK,EAAEM,CAAC,CAACxB,CAAC,EAAE;MAAE,CAAC;IAAE,CAAC;EAAE;EAAE,MAAM,IAAIK,SAAS,CAAC,uIAAuI,CAAC;AAAE;AAE3lB,SAASF,2BAA2BA,CAACqB,CAAC,EAAEI,MAAM,EAAE;EAAE,IAAI,CAACJ,CAAC,EAAE;EAAQ,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOK,iBAAiB,CAACL,CAAC,EAAEI,MAAM,CAAC;EAAE,IAAIE,CAAC,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACnB,IAAI,CAACU,CAAC,CAAC,CAACU,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAE,IAAIJ,CAAC,KAAK,QAAQ,IAAIN,CAAC,CAACW,WAAW,EAAEL,CAAC,GAAGN,CAAC,CAACW,WAAW,CAACC,IAAI;EAAE,IAAIN,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOT,KAAK,CAACgB,IAAI,CAACb,CAAC,CAAC;EAAE,IAAIM,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACQ,IAAI,CAACR,CAAC,CAAC,EAAE,OAAOD,iBAAiB,CAACL,CAAC,EAAEI,MAAM,CAAC;AAAE;AAE/Z,SAASC,iBAAiBA,CAAC9B,GAAG,EAAEwC,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGxC,GAAG,CAACoB,MAAM,EAAEoB,GAAG,GAAGxC,GAAG,CAACoB,MAAM;EAAE,KAAK,IAAInB,CAAC,GAAG,CAAC,EAAEwC,IAAI,GAAG,IAAInB,KAAK,CAACkB,GAAG,CAAC,EAAEvC,CAAC,GAAGuC,GAAG,EAAEvC,CAAC,EAAE,EAAE;IAAEwC,IAAI,CAACxC,CAAC,CAAC,GAAGD,GAAG,CAACC,CAAC,CAAC;EAAE;EAAE,OAAOwC,IAAI;AAAE;AAEtL,OAAOC,mBAAmB,MAAM,0BAA0B,CAAC,CAAC;;AAE5D;AACA;AACA;AACA;;AAEA,OAAO,SAASC,YAAYA,CAACC,IAAI,EAAE;EACjC,IAAIC,MAAM;EACV,IAAIC,GAAG,CAAC,CAAC;;EAETF,IAAI,GAAGA,IAAI,CAACG,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC;EAEpC,KAAK,IAAIC,SAAS,GAAGxB,+BAA+B,CAACoB,IAAI,CAACK,KAAK,CAAC,GAAG,CAAC,CAAC,EAAEC,KAAK,EAAE,CAAC,CAACA,KAAK,GAAGF,SAAS,CAAC,CAAC,EAAE/B,IAAI,GAAG;IAC1G,IAAIkC,IAAI,GAAGD,KAAK,CAAC/B,KAAK;IAEtB,IAAIiC,WAAW,GAAGD,IAAI,CAACF,KAAK,CAAC,GAAG,CAAC;MAC7BI,YAAY,GAAGtD,cAAc,CAACqD,WAAW,EAAE,CAAC,CAAC;MAC7Cf,IAAI,GAAGgB,YAAY,CAAC,CAAC,CAAC;MACtBlC,KAAK,GAAGkC,YAAY,CAAC,CAAC,CAAC;IAE3B,QAAQhB,IAAI;MACV,KAAK,KAAK;QACRQ,MAAM,GAAG1B,KAAK;QACd;MAEF,KAAK,KAAK;QACR2B,GAAG,GAAG3B,KAAK;QACX;MAEF,KAAK,eAAe;QAClB;QACA;QACA,IAAIA,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;UACpB0B,MAAM,GAAG1B,KAAK,GAAG0B,MAAM;QACzB;QAEA;IACJ;EACF,CAAC,CAAC;;EAGF,IAAI,CAACH,mBAAmB,CAACG,MAAM,CAAC,EAAE;IAChC,OAAO,CAAC,CAAC;EACX;EAEA,IAAIS,MAAM,GAAG;IACXT,MAAM,EAAEA;EACV,CAAC;EAED,IAAIC,GAAG,EAAE;IACPQ,MAAM,CAACR,GAAG,GAAGA,GAAG;EAClB;EAEA,OAAOQ,MAAM;AACf;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,aAAaA,CAACC,IAAI,EAAE;EAClC,IAAIX,MAAM,GAAGW,IAAI,CAACX,MAAM;IACpBC,GAAG,GAAGU,IAAI,CAACV,GAAG;EAElB,IAAI,CAACD,MAAM,EAAE;IACX,OAAO,EAAE;EACX;EAEA,IAAIA,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACrB,MAAM,IAAIY,KAAK,CAAC,+DAA+D,CAAC;EAClF;EAEA,OAAO,MAAM,CAACC,MAAM,CAACb,MAAM,CAAC,CAACa,MAAM,CAACZ,GAAG,GAAG,OAAO,GAAGA,GAAG,GAAG,EAAE,CAAC;AAC/D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}