{"version": 3, "file": "mergeArrays.test.js", "names": ["mergeArrays", "describe", "it", "should", "deep", "equal"], "sources": ["../../source/helpers/mergeArrays.test.js"], "sourcesContent": ["import mergeArrays from './mergeArrays.js'\r\n\r\ndescribe('mergeArrays', () => {\r\n\tit('should merge arrays', () => {\r\n\t\tmergeArrays([1, 2], [2, 3]).should.deep.equal([1, 2, 3])\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,WAAP,MAAwB,kBAAxB;AAEAC,QAAQ,CAAC,aAAD,EAAgB,YAAM;EAC7BC,EAAE,CAAC,qBAAD,EAAwB,YAAM;IAC/BF,WAAW,CAAC,CAAC,CAAD,EAAI,CAAJ,CAAD,EAAS,CAAC,CAAD,EAAI,CAAJ,CAAT,CAAX,CAA4BG,MAA5B,CAAmCC,IAAnC,CAAwCC,KAAxC,CAA8C,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CAA9C;EACA,CAFC,CAAF;AAGA,CAJO,CAAR"}