{"ast": null, "code": "function _createForOfIteratorHelperLoose(o, allowArrayLike) {\n  var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"];\n  if (it) return (it = it.call(o)).next.bind(it);\n  if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") {\n    if (it) o = it;\n    var i = 0;\n    return function () {\n      if (i >= o.length) return {\n        done: true\n      };\n      return {\n        done: false,\n        value: o[i++]\n      };\n    };\n  }\n  throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n  return arr2;\n}\nimport isValidNumber from '../isValid.js';\nimport parseDigits from '../helpers/parseDigits.js';\nimport matchPhoneNumberStringAgainstPhoneNumber from './matchPhoneNumberStringAgainstPhoneNumber.js';\nimport Metadata from '../metadata.js';\nimport getCountryByCallingCode from '../helpers/getCountryByCallingCode.js';\nimport { chooseFormatForNumber } from '../format.js';\nimport { startsWith, endsWith } from './util.js';\n/**\r\n * Leniency when finding potential phone numbers in text segments\r\n * The levels here are ordered in increasing strictness.\r\n */\n\nexport default {\n  /**\r\n   * Phone numbers accepted are \"possible\", but not necessarily \"valid\".\r\n   */\n  POSSIBLE: function POSSIBLE(phoneNumber, _ref) {\n    var candidate = _ref.candidate,\n      metadata = _ref.metadata;\n    return true;\n  },\n  /**\r\n   * Phone numbers accepted are \"possible\" and \"valid\".\r\n   * Numbers written in national format must have their national-prefix\r\n   * present if it is usually written for a number of this type.\r\n   */\n  VALID: function VALID(phoneNumber, _ref2) {\n    var candidate = _ref2.candidate,\n      defaultCountry = _ref2.defaultCountry,\n      metadata = _ref2.metadata;\n    if (!phoneNumber.isValid() || !containsOnlyValidXChars(phoneNumber, candidate, metadata)) {\n      return false;\n    } // Skipped for simplicity.\n    // return isNationalPrefixPresentIfRequired(phoneNumber, { defaultCountry, metadata })\n\n    return true;\n  },\n  /**\r\n   * Phone numbers accepted are \"valid\" and\r\n   * are grouped in a possible way for this locale. For example, a US number written as\r\n   * \"65 02 53 00 00\" and \"650253 0000\" are not accepted at this leniency level, whereas\r\n   * \"************\", \"650 2530000\" or \"6502530000\" are.\r\n   * Numbers with more than one '/' symbol in the national significant number\r\n   * are also dropped at this level.\r\n   *\r\n   * Warning: This level might result in lower coverage especially for regions outside of\r\n   * country code \"+1\". If you are not sure about which level to use,\r\n   * email the <NAME_EMAIL>.\r\n   */\n  STRICT_GROUPING: function STRICT_GROUPING(phoneNumber, _ref3) {\n    var candidate = _ref3.candidate,\n      defaultCountry = _ref3.defaultCountry,\n      metadata = _ref3.metadata,\n      regExpCache = _ref3.regExpCache;\n    if (!phoneNumber.isValid() || !containsOnlyValidXChars(phoneNumber, candidate, metadata) || containsMoreThanOneSlashInNationalNumber(phoneNumber, candidate) || !isNationalPrefixPresentIfRequired(phoneNumber, {\n      defaultCountry: defaultCountry,\n      metadata: metadata\n    })) {\n      return false;\n    }\n    return checkNumberGroupingIsValid(phoneNumber, candidate, metadata, allNumberGroupsRemainGrouped, regExpCache);\n  },\n  /**\r\n   * Phone numbers accepted are \"valid\" and are grouped in the same way\r\n   * that we would have formatted it, or as a single block.\r\n   * For example, a US number written as \"650 2530000\" is not accepted\r\n   * at this leniency level, whereas \"************\" or \"6502530000\" are.\r\n   * Numbers with more than one '/' symbol are also dropped at this level.\r\n   *\r\n   * Warning: This level might result in lower coverage especially for regions outside of\r\n   * country code \"+1\". If you are not sure about which level to use, email the discussion group\r\n   * <EMAIL>.\r\n   */\n  EXACT_GROUPING: function EXACT_GROUPING(phoneNumber, _ref4) {\n    var candidate = _ref4.candidate,\n      defaultCountry = _ref4.defaultCountry,\n      metadata = _ref4.metadata,\n      regExpCache = _ref4.regExpCache;\n    if (!phoneNumber.isValid() || !containsOnlyValidXChars(phoneNumber, candidate, metadata) || containsMoreThanOneSlashInNationalNumber(phoneNumber, candidate) || !isNationalPrefixPresentIfRequired(phoneNumber, {\n      defaultCountry: defaultCountry,\n      metadata: metadata\n    })) {\n      return false;\n    }\n    return checkNumberGroupingIsValid(phoneNumber, candidate, metadata, allNumberGroupsAreExactlyPresent, regExpCache);\n  }\n};\nfunction containsOnlyValidXChars(phoneNumber, candidate, metadata) {\n  // The characters 'x' and 'X' can be (1) a carrier code, in which case they always precede the\n  // national significant number or (2) an extension sign, in which case they always precede the\n  // extension number. We assume a carrier code is more than 1 digit, so the first case has to\n  // have more than 1 consecutive 'x' or 'X', whereas the second case can only have exactly 1 'x'\n  // or 'X'. We ignore the character if it appears as the last character of the string.\n  for (var index = 0; index < candidate.length - 1; index++) {\n    var charAtIndex = candidate.charAt(index);\n    if (charAtIndex === 'x' || charAtIndex === 'X') {\n      var charAtNextIndex = candidate.charAt(index + 1);\n      if (charAtNextIndex === 'x' || charAtNextIndex === 'X') {\n        // This is the carrier code case, in which the 'X's always precede the national\n        // significant number.\n        index++;\n        if (matchPhoneNumberStringAgainstPhoneNumber(candidate.substring(index), phoneNumber, metadata) !== 'NSN_MATCH') {\n          return false;\n        } // This is the extension sign case, in which the 'x' or 'X' should always precede the\n        // extension number.\n      } else {\n        var ext = parseDigits(candidate.substring(index));\n        if (ext) {\n          if (phoneNumber.ext !== ext) {\n            return false;\n          }\n        } else {\n          if (phoneNumber.ext) {\n            return false;\n          }\n        }\n      }\n    }\n  }\n  return true;\n}\nfunction isNationalPrefixPresentIfRequired(phoneNumber, _ref5) {\n  var defaultCountry = _ref5.defaultCountry,\n    _metadata = _ref5.metadata;\n\n  // First, check how we deduced the country code. If it was written in international format, then\n  // the national prefix is not required.\n  if (phoneNumber.__countryCallingCodeSource !== 'FROM_DEFAULT_COUNTRY') {\n    return true;\n  }\n  var metadata = new Metadata(_metadata);\n  metadata.selectNumberingPlan(phoneNumber.countryCallingCode);\n  var phoneNumberRegion = phoneNumber.country || getCountryByCallingCode(phoneNumber.countryCallingCode, {\n    nationalNumber: phoneNumber.nationalNumber,\n    defaultCountry: defaultCountry,\n    metadata: metadata\n  }); // Check if a national prefix should be present when formatting this number.\n\n  var nationalNumber = phoneNumber.nationalNumber;\n  var format = chooseFormatForNumber(metadata.numberingPlan.formats(), nationalNumber); // To do this, we check that a national prefix formatting rule was present\n  // and that it wasn't just the first-group symbol ($1) with punctuation.\n\n  if (format.nationalPrefixFormattingRule()) {\n    if (metadata.numberingPlan.nationalPrefixIsOptionalWhenFormattingInNationalFormat()) {\n      // The national-prefix is optional in these cases, so we don't need to check if it was present.\n      return true;\n    }\n    if (!format.usesNationalPrefix()) {\n      // National Prefix not needed for this number.\n      return true;\n    }\n    return Boolean(phoneNumber.nationalPrefix);\n  }\n  return true;\n}\nexport function containsMoreThanOneSlashInNationalNumber(phoneNumber, candidate) {\n  var firstSlashInBodyIndex = candidate.indexOf('/');\n  if (firstSlashInBodyIndex < 0) {\n    // No slashes, this is okay.\n    return false;\n  } // Now look for a second one.\n\n  var secondSlashInBodyIndex = candidate.indexOf('/', firstSlashInBodyIndex + 1);\n  if (secondSlashInBodyIndex < 0) {\n    // Only one slash, this is okay.\n    return false;\n  } // If the first slash is after the country calling code, this is permitted.\n\n  var candidateHasCountryCode = phoneNumber.__countryCallingCodeSource === 'FROM_NUMBER_WITH_PLUS_SIGN' || phoneNumber.__countryCallingCodeSource === 'FROM_NUMBER_WITHOUT_PLUS_SIGN';\n  if (candidateHasCountryCode && parseDigits(candidate.substring(0, firstSlashInBodyIndex)) === phoneNumber.countryCallingCode) {\n    // Any more slashes and this is illegal.\n    return candidate.slice(secondSlashInBodyIndex + 1).indexOf('/') >= 0;\n  }\n  return true;\n}\nfunction checkNumberGroupingIsValid(number, candidate, metadata, checkGroups, regExpCache) {\n  throw new Error('This part of code hasn\\'t been ported');\n  var normalizedCandidate = normalizeDigits(candidate, true\n  /* keep non-digits */);\n  var formattedNumberGroups = getNationalNumberGroups(metadata, number, null);\n  if (checkGroups(metadata, number, normalizedCandidate, formattedNumberGroups)) {\n    return true;\n  } // If this didn't pass, see if there are any alternate formats that match, and try them instead.\n\n  var alternateFormats = MetadataManager.getAlternateFormatsForCountry(number.getCountryCode());\n  var nationalSignificantNumber = util.getNationalSignificantNumber(number);\n  if (alternateFormats) {\n    for (var _iterator = _createForOfIteratorHelperLoose(alternateFormats.numberFormats()), _step; !(_step = _iterator()).done;) {\n      var alternateFormat = _step.value;\n      if (alternateFormat.leadingDigitsPatterns().length > 0) {\n        // There is only one leading digits pattern for alternate formats.\n        var leadingDigitsRegExp = regExpCache.getPatternForRegExp('^' + alternateFormat.leadingDigitsPatterns()[0]);\n        if (!leadingDigitsRegExp.test(nationalSignificantNumber)) {\n          // Leading digits don't match; try another one.\n          continue;\n        }\n      }\n      formattedNumberGroups = getNationalNumberGroups(metadata, number, alternateFormat);\n      if (checkGroups(metadata, number, normalizedCandidate, formattedNumberGroups)) {\n        return true;\n      }\n    }\n  }\n  return false;\n}\n/**\r\n * Helper method to get the national-number part of a number, formatted without any national\r\n * prefix, and return it as a set of digit blocks that would be formatted together following\r\n * standard formatting rules.\r\n */\n\nfunction getNationalNumberGroups(metadata, number, formattingPattern) {\n  throw new Error('This part of code hasn\\'t been ported');\n  if (formattingPattern) {\n    // We format the NSN only, and split that according to the separator.\n    var nationalSignificantNumber = util.getNationalSignificantNumber(number);\n    return util.formatNsnUsingPattern(nationalSignificantNumber, formattingPattern, 'RFC3966', metadata).split('-');\n  } // This will be in the format +CC-DG1-DG2-DGX;ext=EXT where DG1..DGX represents groups of digits.\n\n  var rfc3966Format = formatNumber(number, 'RFC3966', metadata); // We remove the extension part from the formatted string before splitting it into different\n  // groups.\n\n  var endIndex = rfc3966Format.indexOf(';');\n  if (endIndex < 0) {\n    endIndex = rfc3966Format.length;\n  } // The country-code will have a '-' following it.\n\n  var startIndex = rfc3966Format.indexOf('-') + 1;\n  return rfc3966Format.slice(startIndex, endIndex).split('-');\n}\nfunction allNumberGroupsAreExactlyPresent(metadata, number, normalizedCandidate, formattedNumberGroups) {\n  throw new Error('This part of code hasn\\'t been ported');\n  var candidateGroups = normalizedCandidate.split(NON_DIGITS_PATTERN); // Set this to the last group, skipping it if the number has an extension.\n\n  var candidateNumberGroupIndex = number.hasExtension() ? candidateGroups.length - 2 : candidateGroups.length - 1; // First we check if the national significant number is formatted as a block.\n  // We use contains and not equals, since the national significant number may be present with\n  // a prefix such as a national number prefix, or the country code itself.\n\n  if (candidateGroups.length == 1 || candidateGroups[candidateNumberGroupIndex].contains(util.getNationalSignificantNumber(number))) {\n    return true;\n  } // Starting from the end, go through in reverse, excluding the first group, and check the\n  // candidate and number groups are the same.\n\n  var formattedNumberGroupIndex = formattedNumberGroups.length - 1;\n  while (formattedNumberGroupIndex > 0 && candidateNumberGroupIndex >= 0) {\n    if (candidateGroups[candidateNumberGroupIndex] !== formattedNumberGroups[formattedNumberGroupIndex]) {\n      return false;\n    }\n    formattedNumberGroupIndex--;\n    candidateNumberGroupIndex--;\n  } // Now check the first group. There may be a national prefix at the start, so we only check\n  // that the candidate group ends with the formatted number group.\n\n  return candidateNumberGroupIndex >= 0 && endsWith(candidateGroups[candidateNumberGroupIndex], formattedNumberGroups[0]);\n}\nfunction allNumberGroupsRemainGrouped(metadata, number, normalizedCandidate, formattedNumberGroups) {\n  throw new Error('This part of code hasn\\'t been ported');\n  var fromIndex = 0;\n  if (number.getCountryCodeSource() !== CountryCodeSource.FROM_DEFAULT_COUNTRY) {\n    // First skip the country code if the normalized candidate contained it.\n    var countryCode = String(number.getCountryCode());\n    fromIndex = normalizedCandidate.indexOf(countryCode) + countryCode.length();\n  } // Check each group of consecutive digits are not broken into separate groupings in the\n  // {@code normalizedCandidate} string.\n\n  for (var i = 0; i < formattedNumberGroups.length; i++) {\n    // Fails if the substring of {@code normalizedCandidate} starting from {@code fromIndex}\n    // doesn't contain the consecutive digits in formattedNumberGroups[i].\n    fromIndex = normalizedCandidate.indexOf(formattedNumberGroups[i], fromIndex);\n    if (fromIndex < 0) {\n      return false;\n    } // Moves {@code fromIndex} forward.\n\n    fromIndex += formattedNumberGroups[i].length();\n    if (i == 0 && fromIndex < normalizedCandidate.length()) {\n      // We are at the position right after the NDC. We get the region used for formatting\n      // information based on the country code in the phone number, rather than the number itself,\n      // as we do not need to distinguish between different countries with the same country\n      // calling code and this is faster.\n      var region = util.getRegionCodeForCountryCode(number.getCountryCode());\n      if (util.getNddPrefixForRegion(region, true) != null && Character.isDigit(normalizedCandidate.charAt(fromIndex))) {\n        // This means there is no formatting symbol after the NDC. In this case, we only\n        // accept the number if there is no formatting symbol at all in the number, except\n        // for extensions. This is only important for countries with national prefixes.\n        var nationalSignificantNumber = util.getNationalSignificantNumber(number);\n        return startsWith(normalizedCandidate.slice(fromIndex - formattedNumberGroups[i].length), nationalSignificantNumber);\n      }\n    }\n  } // The check here makes sure that we haven't mistakenly already used the extension to\n  // match the last group of the subscriber number. Note the extension cannot have\n  // formatting in-between digits.\n\n  return normalizedCandidate.slice(fromIndex).contains(number.getExtension());\n}", "map": {"version": 3, "names": ["_createForOfIteratorHelperLoose", "o", "allowArrayLike", "it", "Symbol", "iterator", "call", "next", "bind", "Array", "isArray", "_unsupportedIterableToArray", "length", "i", "done", "value", "TypeError", "minLen", "_arrayLikeToArray", "n", "Object", "prototype", "toString", "slice", "constructor", "name", "from", "test", "arr", "len", "arr2", "isValidNumber", "parseDigits", "matchPhoneNumberStringAgainstPhoneNumber", "<PERSON><PERSON><PERSON>", "getCountryByCallingCode", "chooseFormatForNumber", "startsWith", "endsWith", "POSSIBLE", "phoneNumber", "_ref", "candidate", "metadata", "VALID", "_ref2", "defaultCountry", "<PERSON><PERSON><PERSON><PERSON>", "containsOnlyValidXChars", "STRICT_GROUPING", "_ref3", "regExpCache", "containsMoreThanOneSlashInNationalNumber", "isNationalPrefixPresentIfRequired", "checkNumberGroupingIsValid", "allNumberGroupsRemainGrouped", "EXACT_GROUPING", "_ref4", "allNumberGroupsAreExactlyPresent", "index", "charAtIndex", "char<PERSON>t", "charAtNextIndex", "substring", "ext", "_ref5", "_metadata", "__countryCallingCodeSource", "selectNumberingPlan", "countryCallingCode", "phoneNumberRegion", "country", "nationalNumber", "format", "numberingPlan", "formats", "nationalPrefixFormattingRule", "nationalPrefixIsOptionalWhenFormattingInNationalFormat", "usesNationalPrefix", "Boolean", "nationalPrefix", "firstSlashInBodyIndex", "indexOf", "secondSlashInBodyIndex", "candidateHasCountryCode", "number", "checkGroups", "Error", "normalizedCandidate", "normalizeDigits", "formattedNumberGroups", "getNationalNumberGroups", "alternateFormats", "MetadataManager", "getAlternateFormatsForCountry", "getCountryCode", "nationalSignificantNumber", "util", "getNationalSignificantNumber", "_iterator", "numberFormats", "_step", "alternateFormat", "leadingDigitsPatterns", "leadingDigitsRegExp", "getPatternForRegExp", "formattingPattern", "formatNsnUsingPattern", "split", "rfc3966Format", "formatNumber", "endIndex", "startIndex", "candidateGroups", "NON_DIGITS_PATTERN", "candidateNumberGroupIndex", "hasExtension", "contains", "formattedNumberGroupIndex", "fromIndex", "getCountryCodeSource", "CountryCodeSource", "FROM_DEFAULT_COUNTRY", "countryCode", "String", "region", "getRegionCodeForCountryCode", "getNddPrefixForRegion", "Character", "isDigit", "getExtension"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/findNumbers/Leniency.js"], "sourcesContent": ["function _createForOfIteratorHelperLoose(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (it) return (it = it.call(o)).next.bind(it); if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; return function () { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nimport isValidNumber from '../isValid.js';\nimport parseDigits from '../helpers/parseDigits.js';\nimport matchPhoneNumberStringAgainstPhoneNumber from './matchPhoneNumberStringAgainstPhoneNumber.js';\nimport Metadata from '../metadata.js';\nimport getCountryByCallingCode from '../helpers/getCountryByCallingCode.js';\nimport { chooseFormatForNumber } from '../format.js';\nimport { startsWith, endsWith } from './util.js';\n/**\r\n * Leniency when finding potential phone numbers in text segments\r\n * The levels here are ordered in increasing strictness.\r\n */\n\nexport default {\n  /**\r\n   * Phone numbers accepted are \"possible\", but not necessarily \"valid\".\r\n   */\n  POSSIBLE: function POSSIBLE(phoneNumber, _ref) {\n    var candidate = _ref.candidate,\n        metadata = _ref.metadata;\n    return true;\n  },\n\n  /**\r\n   * Phone numbers accepted are \"possible\" and \"valid\".\r\n   * Numbers written in national format must have their national-prefix\r\n   * present if it is usually written for a number of this type.\r\n   */\n  VALID: function VALID(phoneNumber, _ref2) {\n    var candidate = _ref2.candidate,\n        defaultCountry = _ref2.defaultCountry,\n        metadata = _ref2.metadata;\n\n    if (!phoneNumber.isValid() || !containsOnlyValidXChars(phoneNumber, candidate, metadata)) {\n      return false;\n    } // Skipped for simplicity.\n    // return isNationalPrefixPresentIfRequired(phoneNumber, { defaultCountry, metadata })\n\n\n    return true;\n  },\n\n  /**\r\n   * Phone numbers accepted are \"valid\" and\r\n   * are grouped in a possible way for this locale. For example, a US number written as\r\n   * \"65 02 53 00 00\" and \"650253 0000\" are not accepted at this leniency level, whereas\r\n   * \"************\", \"650 2530000\" or \"6502530000\" are.\r\n   * Numbers with more than one '/' symbol in the national significant number\r\n   * are also dropped at this level.\r\n   *\r\n   * Warning: This level might result in lower coverage especially for regions outside of\r\n   * country code \"+1\". If you are not sure about which level to use,\r\n   * email the <NAME_EMAIL>.\r\n   */\n  STRICT_GROUPING: function STRICT_GROUPING(phoneNumber, _ref3) {\n    var candidate = _ref3.candidate,\n        defaultCountry = _ref3.defaultCountry,\n        metadata = _ref3.metadata,\n        regExpCache = _ref3.regExpCache;\n\n    if (!phoneNumber.isValid() || !containsOnlyValidXChars(phoneNumber, candidate, metadata) || containsMoreThanOneSlashInNationalNumber(phoneNumber, candidate) || !isNationalPrefixPresentIfRequired(phoneNumber, {\n      defaultCountry: defaultCountry,\n      metadata: metadata\n    })) {\n      return false;\n    }\n\n    return checkNumberGroupingIsValid(phoneNumber, candidate, metadata, allNumberGroupsRemainGrouped, regExpCache);\n  },\n\n  /**\r\n   * Phone numbers accepted are \"valid\" and are grouped in the same way\r\n   * that we would have formatted it, or as a single block.\r\n   * For example, a US number written as \"650 2530000\" is not accepted\r\n   * at this leniency level, whereas \"************\" or \"6502530000\" are.\r\n   * Numbers with more than one '/' symbol are also dropped at this level.\r\n   *\r\n   * Warning: This level might result in lower coverage especially for regions outside of\r\n   * country code \"+1\". If you are not sure about which level to use, email the discussion group\r\n   * <EMAIL>.\r\n   */\n  EXACT_GROUPING: function EXACT_GROUPING(phoneNumber, _ref4) {\n    var candidate = _ref4.candidate,\n        defaultCountry = _ref4.defaultCountry,\n        metadata = _ref4.metadata,\n        regExpCache = _ref4.regExpCache;\n\n    if (!phoneNumber.isValid() || !containsOnlyValidXChars(phoneNumber, candidate, metadata) || containsMoreThanOneSlashInNationalNumber(phoneNumber, candidate) || !isNationalPrefixPresentIfRequired(phoneNumber, {\n      defaultCountry: defaultCountry,\n      metadata: metadata\n    })) {\n      return false;\n    }\n\n    return checkNumberGroupingIsValid(phoneNumber, candidate, metadata, allNumberGroupsAreExactlyPresent, regExpCache);\n  }\n};\n\nfunction containsOnlyValidXChars(phoneNumber, candidate, metadata) {\n  // The characters 'x' and 'X' can be (1) a carrier code, in which case they always precede the\n  // national significant number or (2) an extension sign, in which case they always precede the\n  // extension number. We assume a carrier code is more than 1 digit, so the first case has to\n  // have more than 1 consecutive 'x' or 'X', whereas the second case can only have exactly 1 'x'\n  // or 'X'. We ignore the character if it appears as the last character of the string.\n  for (var index = 0; index < candidate.length - 1; index++) {\n    var charAtIndex = candidate.charAt(index);\n\n    if (charAtIndex === 'x' || charAtIndex === 'X') {\n      var charAtNextIndex = candidate.charAt(index + 1);\n\n      if (charAtNextIndex === 'x' || charAtNextIndex === 'X') {\n        // This is the carrier code case, in which the 'X's always precede the national\n        // significant number.\n        index++;\n\n        if (matchPhoneNumberStringAgainstPhoneNumber(candidate.substring(index), phoneNumber, metadata) !== 'NSN_MATCH') {\n          return false;\n        } // This is the extension sign case, in which the 'x' or 'X' should always precede the\n        // extension number.\n\n      } else {\n        var ext = parseDigits(candidate.substring(index));\n\n        if (ext) {\n          if (phoneNumber.ext !== ext) {\n            return false;\n          }\n        } else {\n          if (phoneNumber.ext) {\n            return false;\n          }\n        }\n      }\n    }\n  }\n\n  return true;\n}\n\nfunction isNationalPrefixPresentIfRequired(phoneNumber, _ref5) {\n  var defaultCountry = _ref5.defaultCountry,\n      _metadata = _ref5.metadata;\n\n  // First, check how we deduced the country code. If it was written in international format, then\n  // the national prefix is not required.\n  if (phoneNumber.__countryCallingCodeSource !== 'FROM_DEFAULT_COUNTRY') {\n    return true;\n  }\n\n  var metadata = new Metadata(_metadata);\n  metadata.selectNumberingPlan(phoneNumber.countryCallingCode);\n  var phoneNumberRegion = phoneNumber.country || getCountryByCallingCode(phoneNumber.countryCallingCode, {\n    nationalNumber: phoneNumber.nationalNumber,\n    defaultCountry: defaultCountry,\n    metadata: metadata\n  }); // Check if a national prefix should be present when formatting this number.\n\n  var nationalNumber = phoneNumber.nationalNumber;\n  var format = chooseFormatForNumber(metadata.numberingPlan.formats(), nationalNumber); // To do this, we check that a national prefix formatting rule was present\n  // and that it wasn't just the first-group symbol ($1) with punctuation.\n\n  if (format.nationalPrefixFormattingRule()) {\n    if (metadata.numberingPlan.nationalPrefixIsOptionalWhenFormattingInNationalFormat()) {\n      // The national-prefix is optional in these cases, so we don't need to check if it was present.\n      return true;\n    }\n\n    if (!format.usesNationalPrefix()) {\n      // National Prefix not needed for this number.\n      return true;\n    }\n\n    return Boolean(phoneNumber.nationalPrefix);\n  }\n\n  return true;\n}\n\nexport function containsMoreThanOneSlashInNationalNumber(phoneNumber, candidate) {\n  var firstSlashInBodyIndex = candidate.indexOf('/');\n\n  if (firstSlashInBodyIndex < 0) {\n    // No slashes, this is okay.\n    return false;\n  } // Now look for a second one.\n\n\n  var secondSlashInBodyIndex = candidate.indexOf('/', firstSlashInBodyIndex + 1);\n\n  if (secondSlashInBodyIndex < 0) {\n    // Only one slash, this is okay.\n    return false;\n  } // If the first slash is after the country calling code, this is permitted.\n\n\n  var candidateHasCountryCode = phoneNumber.__countryCallingCodeSource === 'FROM_NUMBER_WITH_PLUS_SIGN' || phoneNumber.__countryCallingCodeSource === 'FROM_NUMBER_WITHOUT_PLUS_SIGN';\n\n  if (candidateHasCountryCode && parseDigits(candidate.substring(0, firstSlashInBodyIndex)) === phoneNumber.countryCallingCode) {\n    // Any more slashes and this is illegal.\n    return candidate.slice(secondSlashInBodyIndex + 1).indexOf('/') >= 0;\n  }\n\n  return true;\n}\n\nfunction checkNumberGroupingIsValid(number, candidate, metadata, checkGroups, regExpCache) {\n  throw new Error('This part of code hasn\\'t been ported');\n  var normalizedCandidate = normalizeDigits(candidate, true\n  /* keep non-digits */\n  );\n  var formattedNumberGroups = getNationalNumberGroups(metadata, number, null);\n\n  if (checkGroups(metadata, number, normalizedCandidate, formattedNumberGroups)) {\n    return true;\n  } // If this didn't pass, see if there are any alternate formats that match, and try them instead.\n\n\n  var alternateFormats = MetadataManager.getAlternateFormatsForCountry(number.getCountryCode());\n  var nationalSignificantNumber = util.getNationalSignificantNumber(number);\n\n  if (alternateFormats) {\n    for (var _iterator = _createForOfIteratorHelperLoose(alternateFormats.numberFormats()), _step; !(_step = _iterator()).done;) {\n      var alternateFormat = _step.value;\n\n      if (alternateFormat.leadingDigitsPatterns().length > 0) {\n        // There is only one leading digits pattern for alternate formats.\n        var leadingDigitsRegExp = regExpCache.getPatternForRegExp('^' + alternateFormat.leadingDigitsPatterns()[0]);\n\n        if (!leadingDigitsRegExp.test(nationalSignificantNumber)) {\n          // Leading digits don't match; try another one.\n          continue;\n        }\n      }\n\n      formattedNumberGroups = getNationalNumberGroups(metadata, number, alternateFormat);\n\n      if (checkGroups(metadata, number, normalizedCandidate, formattedNumberGroups)) {\n        return true;\n      }\n    }\n  }\n\n  return false;\n}\n/**\r\n * Helper method to get the national-number part of a number, formatted without any national\r\n * prefix, and return it as a set of digit blocks that would be formatted together following\r\n * standard formatting rules.\r\n */\n\n\nfunction getNationalNumberGroups(metadata, number, formattingPattern) {\n  throw new Error('This part of code hasn\\'t been ported');\n\n  if (formattingPattern) {\n    // We format the NSN only, and split that according to the separator.\n    var nationalSignificantNumber = util.getNationalSignificantNumber(number);\n    return util.formatNsnUsingPattern(nationalSignificantNumber, formattingPattern, 'RFC3966', metadata).split('-');\n  } // This will be in the format +CC-DG1-DG2-DGX;ext=EXT where DG1..DGX represents groups of digits.\n\n\n  var rfc3966Format = formatNumber(number, 'RFC3966', metadata); // We remove the extension part from the formatted string before splitting it into different\n  // groups.\n\n  var endIndex = rfc3966Format.indexOf(';');\n\n  if (endIndex < 0) {\n    endIndex = rfc3966Format.length;\n  } // The country-code will have a '-' following it.\n\n\n  var startIndex = rfc3966Format.indexOf('-') + 1;\n  return rfc3966Format.slice(startIndex, endIndex).split('-');\n}\n\nfunction allNumberGroupsAreExactlyPresent(metadata, number, normalizedCandidate, formattedNumberGroups) {\n  throw new Error('This part of code hasn\\'t been ported');\n  var candidateGroups = normalizedCandidate.split(NON_DIGITS_PATTERN); // Set this to the last group, skipping it if the number has an extension.\n\n  var candidateNumberGroupIndex = number.hasExtension() ? candidateGroups.length - 2 : candidateGroups.length - 1; // First we check if the national significant number is formatted as a block.\n  // We use contains and not equals, since the national significant number may be present with\n  // a prefix such as a national number prefix, or the country code itself.\n\n  if (candidateGroups.length == 1 || candidateGroups[candidateNumberGroupIndex].contains(util.getNationalSignificantNumber(number))) {\n    return true;\n  } // Starting from the end, go through in reverse, excluding the first group, and check the\n  // candidate and number groups are the same.\n\n\n  var formattedNumberGroupIndex = formattedNumberGroups.length - 1;\n\n  while (formattedNumberGroupIndex > 0 && candidateNumberGroupIndex >= 0) {\n    if (candidateGroups[candidateNumberGroupIndex] !== formattedNumberGroups[formattedNumberGroupIndex]) {\n      return false;\n    }\n\n    formattedNumberGroupIndex--;\n    candidateNumberGroupIndex--;\n  } // Now check the first group. There may be a national prefix at the start, so we only check\n  // that the candidate group ends with the formatted number group.\n\n\n  return candidateNumberGroupIndex >= 0 && endsWith(candidateGroups[candidateNumberGroupIndex], formattedNumberGroups[0]);\n}\n\nfunction allNumberGroupsRemainGrouped(metadata, number, normalizedCandidate, formattedNumberGroups) {\n  throw new Error('This part of code hasn\\'t been ported');\n  var fromIndex = 0;\n\n  if (number.getCountryCodeSource() !== CountryCodeSource.FROM_DEFAULT_COUNTRY) {\n    // First skip the country code if the normalized candidate contained it.\n    var countryCode = String(number.getCountryCode());\n    fromIndex = normalizedCandidate.indexOf(countryCode) + countryCode.length();\n  } // Check each group of consecutive digits are not broken into separate groupings in the\n  // {@code normalizedCandidate} string.\n\n\n  for (var i = 0; i < formattedNumberGroups.length; i++) {\n    // Fails if the substring of {@code normalizedCandidate} starting from {@code fromIndex}\n    // doesn't contain the consecutive digits in formattedNumberGroups[i].\n    fromIndex = normalizedCandidate.indexOf(formattedNumberGroups[i], fromIndex);\n\n    if (fromIndex < 0) {\n      return false;\n    } // Moves {@code fromIndex} forward.\n\n\n    fromIndex += formattedNumberGroups[i].length();\n\n    if (i == 0 && fromIndex < normalizedCandidate.length()) {\n      // We are at the position right after the NDC. We get the region used for formatting\n      // information based on the country code in the phone number, rather than the number itself,\n      // as we do not need to distinguish between different countries with the same country\n      // calling code and this is faster.\n      var region = util.getRegionCodeForCountryCode(number.getCountryCode());\n\n      if (util.getNddPrefixForRegion(region, true) != null && Character.isDigit(normalizedCandidate.charAt(fromIndex))) {\n        // This means there is no formatting symbol after the NDC. In this case, we only\n        // accept the number if there is no formatting symbol at all in the number, except\n        // for extensions. This is only important for countries with national prefixes.\n        var nationalSignificantNumber = util.getNationalSignificantNumber(number);\n        return startsWith(normalizedCandidate.slice(fromIndex - formattedNumberGroups[i].length), nationalSignificantNumber);\n      }\n    }\n  } // The check here makes sure that we haven't mistakenly already used the extension to\n  // match the last group of the subscriber number. Note the extension cannot have\n  // formatting in-between digits.\n\n\n  return normalizedCandidate.slice(fromIndex).contains(number.getExtension());\n}\n"], "mappings": "AAAA,SAASA,+BAA+BA,CAACC,CAAC,EAAEC,cAAc,EAAE;EAAE,IAAIC,EAAE,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAIH,CAAC,CAACG,MAAM,CAACC,QAAQ,CAAC,IAAIJ,CAAC,CAAC,YAAY,CAAC;EAAE,IAAIE,EAAE,EAAE,OAAO,CAACA,EAAE,GAAGA,EAAE,CAACG,IAAI,CAACL,CAAC,CAAC,EAAEM,IAAI,CAACC,IAAI,CAACL,EAAE,CAAC;EAAE,IAAIM,KAAK,CAACC,OAAO,CAACT,CAAC,CAAC,KAAKE,EAAE,GAAGQ,2BAA2B,CAACV,CAAC,CAAC,CAAC,IAAIC,cAAc,IAAID,CAAC,IAAI,OAAOA,CAAC,CAACW,MAAM,KAAK,QAAQ,EAAE;IAAE,IAAIT,EAAE,EAAEF,CAAC,GAAGE,EAAE;IAAE,IAAIU,CAAC,GAAG,CAAC;IAAE,OAAO,YAAY;MAAE,IAAIA,CAAC,IAAIZ,CAAC,CAACW,MAAM,EAAE,OAAO;QAAEE,IAAI,EAAE;MAAK,CAAC;MAAE,OAAO;QAAEA,IAAI,EAAE,KAAK;QAAEC,KAAK,EAAEd,CAAC,CAACY,CAAC,EAAE;MAAE,CAAC;IAAE,CAAC;EAAE;EAAE,MAAM,IAAIG,SAAS,CAAC,uIAAuI,CAAC;AAAE;AAE3lB,SAASL,2BAA2BA,CAACV,CAAC,EAAEgB,MAAM,EAAE;EAAE,IAAI,CAAChB,CAAC,EAAE;EAAQ,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOiB,iBAAiB,CAACjB,CAAC,EAAEgB,MAAM,CAAC;EAAE,IAAIE,CAAC,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAAChB,IAAI,CAACL,CAAC,CAAC,CAACsB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAE,IAAIJ,CAAC,KAAK,QAAQ,IAAIlB,CAAC,CAACuB,WAAW,EAAEL,CAAC,GAAGlB,CAAC,CAACuB,WAAW,CAACC,IAAI;EAAE,IAAIN,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOV,KAAK,CAACiB,IAAI,CAACzB,CAAC,CAAC;EAAE,IAAIkB,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACQ,IAAI,CAACR,CAAC,CAAC,EAAE,OAAOD,iBAAiB,CAACjB,CAAC,EAAEgB,MAAM,CAAC;AAAE;AAE/Z,SAASC,iBAAiBA,CAACU,GAAG,EAAEC,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGD,GAAG,CAAChB,MAAM,EAAEiB,GAAG,GAAGD,GAAG,CAAChB,MAAM;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEiB,IAAI,GAAG,IAAIrB,KAAK,CAACoB,GAAG,CAAC,EAAEhB,CAAC,GAAGgB,GAAG,EAAEhB,CAAC,EAAE,EAAE;IAAEiB,IAAI,CAACjB,CAAC,CAAC,GAAGe,GAAG,CAACf,CAAC,CAAC;EAAE;EAAE,OAAOiB,IAAI;AAAE;AAEtL,OAAOC,aAAa,MAAM,eAAe;AACzC,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,wCAAwC,MAAM,+CAA+C;AACpG,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,uBAAuB,MAAM,uCAAuC;AAC3E,SAASC,qBAAqB,QAAQ,cAAc;AACpD,SAASC,UAAU,EAAEC,QAAQ,QAAQ,WAAW;AAChD;AACA;AACA;AACA;;AAEA,eAAe;EACb;AACF;AACA;EACEC,QAAQ,EAAE,SAASA,QAAQA,CAACC,WAAW,EAAEC,IAAI,EAAE;IAC7C,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;MAC1BC,QAAQ,GAAGF,IAAI,CAACE,QAAQ;IAC5B,OAAO,IAAI;EACb,CAAC;EAED;AACF;AACA;AACA;AACA;EACEC,KAAK,EAAE,SAASA,KAAKA,CAACJ,WAAW,EAAEK,KAAK,EAAE;IACxC,IAAIH,SAAS,GAAGG,KAAK,CAACH,SAAS;MAC3BI,cAAc,GAAGD,KAAK,CAACC,cAAc;MACrCH,QAAQ,GAAGE,KAAK,CAACF,QAAQ;IAE7B,IAAI,CAACH,WAAW,CAACO,OAAO,CAAC,CAAC,IAAI,CAACC,uBAAuB,CAACR,WAAW,EAAEE,SAAS,EAAEC,QAAQ,CAAC,EAAE;MACxF,OAAO,KAAK;IACd,CAAC,CAAC;IACF;;IAGA,OAAO,IAAI;EACb,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEM,eAAe,EAAE,SAASA,eAAeA,CAACT,WAAW,EAAEU,KAAK,EAAE;IAC5D,IAAIR,SAAS,GAAGQ,KAAK,CAACR,SAAS;MAC3BI,cAAc,GAAGI,KAAK,CAACJ,cAAc;MACrCH,QAAQ,GAAGO,KAAK,CAACP,QAAQ;MACzBQ,WAAW,GAAGD,KAAK,CAACC,WAAW;IAEnC,IAAI,CAACX,WAAW,CAACO,OAAO,CAAC,CAAC,IAAI,CAACC,uBAAuB,CAACR,WAAW,EAAEE,SAAS,EAAEC,QAAQ,CAAC,IAAIS,wCAAwC,CAACZ,WAAW,EAAEE,SAAS,CAAC,IAAI,CAACW,iCAAiC,CAACb,WAAW,EAAE;MAC9MM,cAAc,EAAEA,cAAc;MAC9BH,QAAQ,EAAEA;IACZ,CAAC,CAAC,EAAE;MACF,OAAO,KAAK;IACd;IAEA,OAAOW,0BAA0B,CAACd,WAAW,EAAEE,SAAS,EAAEC,QAAQ,EAAEY,4BAA4B,EAAEJ,WAAW,CAAC;EAChH,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEK,cAAc,EAAE,SAASA,cAAcA,CAAChB,WAAW,EAAEiB,KAAK,EAAE;IAC1D,IAAIf,SAAS,GAAGe,KAAK,CAACf,SAAS;MAC3BI,cAAc,GAAGW,KAAK,CAACX,cAAc;MACrCH,QAAQ,GAAGc,KAAK,CAACd,QAAQ;MACzBQ,WAAW,GAAGM,KAAK,CAACN,WAAW;IAEnC,IAAI,CAACX,WAAW,CAACO,OAAO,CAAC,CAAC,IAAI,CAACC,uBAAuB,CAACR,WAAW,EAAEE,SAAS,EAAEC,QAAQ,CAAC,IAAIS,wCAAwC,CAACZ,WAAW,EAAEE,SAAS,CAAC,IAAI,CAACW,iCAAiC,CAACb,WAAW,EAAE;MAC9MM,cAAc,EAAEA,cAAc;MAC9BH,QAAQ,EAAEA;IACZ,CAAC,CAAC,EAAE;MACF,OAAO,KAAK;IACd;IAEA,OAAOW,0BAA0B,CAACd,WAAW,EAAEE,SAAS,EAAEC,QAAQ,EAAEe,gCAAgC,EAAEP,WAAW,CAAC;EACpH;AACF,CAAC;AAED,SAASH,uBAAuBA,CAACR,WAAW,EAAEE,SAAS,EAAEC,QAAQ,EAAE;EACjE;EACA;EACA;EACA;EACA;EACA,KAAK,IAAIgB,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGjB,SAAS,CAAC9B,MAAM,GAAG,CAAC,EAAE+C,KAAK,EAAE,EAAE;IACzD,IAAIC,WAAW,GAAGlB,SAAS,CAACmB,MAAM,CAACF,KAAK,CAAC;IAEzC,IAAIC,WAAW,KAAK,GAAG,IAAIA,WAAW,KAAK,GAAG,EAAE;MAC9C,IAAIE,eAAe,GAAGpB,SAAS,CAACmB,MAAM,CAACF,KAAK,GAAG,CAAC,CAAC;MAEjD,IAAIG,eAAe,KAAK,GAAG,IAAIA,eAAe,KAAK,GAAG,EAAE;QACtD;QACA;QACAH,KAAK,EAAE;QAEP,IAAI1B,wCAAwC,CAACS,SAAS,CAACqB,SAAS,CAACJ,KAAK,CAAC,EAAEnB,WAAW,EAAEG,QAAQ,CAAC,KAAK,WAAW,EAAE;UAC/G,OAAO,KAAK;QACd,CAAC,CAAC;QACF;MAEF,CAAC,MAAM;QACL,IAAIqB,GAAG,GAAGhC,WAAW,CAACU,SAAS,CAACqB,SAAS,CAACJ,KAAK,CAAC,CAAC;QAEjD,IAAIK,GAAG,EAAE;UACP,IAAIxB,WAAW,CAACwB,GAAG,KAAKA,GAAG,EAAE;YAC3B,OAAO,KAAK;UACd;QACF,CAAC,MAAM;UACL,IAAIxB,WAAW,CAACwB,GAAG,EAAE;YACnB,OAAO,KAAK;UACd;QACF;MACF;IACF;EACF;EAEA,OAAO,IAAI;AACb;AAEA,SAASX,iCAAiCA,CAACb,WAAW,EAAEyB,KAAK,EAAE;EAC7D,IAAInB,cAAc,GAAGmB,KAAK,CAACnB,cAAc;IACrCoB,SAAS,GAAGD,KAAK,CAACtB,QAAQ;;EAE9B;EACA;EACA,IAAIH,WAAW,CAAC2B,0BAA0B,KAAK,sBAAsB,EAAE;IACrE,OAAO,IAAI;EACb;EAEA,IAAIxB,QAAQ,GAAG,IAAIT,QAAQ,CAACgC,SAAS,CAAC;EACtCvB,QAAQ,CAACyB,mBAAmB,CAAC5B,WAAW,CAAC6B,kBAAkB,CAAC;EAC5D,IAAIC,iBAAiB,GAAG9B,WAAW,CAAC+B,OAAO,IAAIpC,uBAAuB,CAACK,WAAW,CAAC6B,kBAAkB,EAAE;IACrGG,cAAc,EAAEhC,WAAW,CAACgC,cAAc;IAC1C1B,cAAc,EAAEA,cAAc;IAC9BH,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC,CAAC;;EAEJ,IAAI6B,cAAc,GAAGhC,WAAW,CAACgC,cAAc;EAC/C,IAAIC,MAAM,GAAGrC,qBAAqB,CAACO,QAAQ,CAAC+B,aAAa,CAACC,OAAO,CAAC,CAAC,EAAEH,cAAc,CAAC,CAAC,CAAC;EACtF;;EAEA,IAAIC,MAAM,CAACG,4BAA4B,CAAC,CAAC,EAAE;IACzC,IAAIjC,QAAQ,CAAC+B,aAAa,CAACG,sDAAsD,CAAC,CAAC,EAAE;MACnF;MACA,OAAO,IAAI;IACb;IAEA,IAAI,CAACJ,MAAM,CAACK,kBAAkB,CAAC,CAAC,EAAE;MAChC;MACA,OAAO,IAAI;IACb;IAEA,OAAOC,OAAO,CAACvC,WAAW,CAACwC,cAAc,CAAC;EAC5C;EAEA,OAAO,IAAI;AACb;AAEA,OAAO,SAAS5B,wCAAwCA,CAACZ,WAAW,EAAEE,SAAS,EAAE;EAC/E,IAAIuC,qBAAqB,GAAGvC,SAAS,CAACwC,OAAO,CAAC,GAAG,CAAC;EAElD,IAAID,qBAAqB,GAAG,CAAC,EAAE;IAC7B;IACA,OAAO,KAAK;EACd,CAAC,CAAC;;EAGF,IAAIE,sBAAsB,GAAGzC,SAAS,CAACwC,OAAO,CAAC,GAAG,EAAED,qBAAqB,GAAG,CAAC,CAAC;EAE9E,IAAIE,sBAAsB,GAAG,CAAC,EAAE;IAC9B;IACA,OAAO,KAAK;EACd,CAAC,CAAC;;EAGF,IAAIC,uBAAuB,GAAG5C,WAAW,CAAC2B,0BAA0B,KAAK,4BAA4B,IAAI3B,WAAW,CAAC2B,0BAA0B,KAAK,+BAA+B;EAEnL,IAAIiB,uBAAuB,IAAIpD,WAAW,CAACU,SAAS,CAACqB,SAAS,CAAC,CAAC,EAAEkB,qBAAqB,CAAC,CAAC,KAAKzC,WAAW,CAAC6B,kBAAkB,EAAE;IAC5H;IACA,OAAO3B,SAAS,CAACnB,KAAK,CAAC4D,sBAAsB,GAAG,CAAC,CAAC,CAACD,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC;EACtE;EAEA,OAAO,IAAI;AACb;AAEA,SAAS5B,0BAA0BA,CAAC+B,MAAM,EAAE3C,SAAS,EAAEC,QAAQ,EAAE2C,WAAW,EAAEnC,WAAW,EAAE;EACzF,MAAM,IAAIoC,KAAK,CAAC,uCAAuC,CAAC;EACxD,IAAIC,mBAAmB,GAAGC,eAAe,CAAC/C,SAAS,EAAE;EACrD,qBACA,CAAC;EACD,IAAIgD,qBAAqB,GAAGC,uBAAuB,CAAChD,QAAQ,EAAE0C,MAAM,EAAE,IAAI,CAAC;EAE3E,IAAIC,WAAW,CAAC3C,QAAQ,EAAE0C,MAAM,EAAEG,mBAAmB,EAAEE,qBAAqB,CAAC,EAAE;IAC7E,OAAO,IAAI;EACb,CAAC,CAAC;;EAGF,IAAIE,gBAAgB,GAAGC,eAAe,CAACC,6BAA6B,CAACT,MAAM,CAACU,cAAc,CAAC,CAAC,CAAC;EAC7F,IAAIC,yBAAyB,GAAGC,IAAI,CAACC,4BAA4B,CAACb,MAAM,CAAC;EAEzE,IAAIO,gBAAgB,EAAE;IACpB,KAAK,IAAIO,SAAS,GAAGnG,+BAA+B,CAAC4F,gBAAgB,CAACQ,aAAa,CAAC,CAAC,CAAC,EAAEC,KAAK,EAAE,CAAC,CAACA,KAAK,GAAGF,SAAS,CAAC,CAAC,EAAErF,IAAI,GAAG;MAC3H,IAAIwF,eAAe,GAAGD,KAAK,CAACtF,KAAK;MAEjC,IAAIuF,eAAe,CAACC,qBAAqB,CAAC,CAAC,CAAC3F,MAAM,GAAG,CAAC,EAAE;QACtD;QACA,IAAI4F,mBAAmB,GAAGrD,WAAW,CAACsD,mBAAmB,CAAC,GAAG,GAAGH,eAAe,CAACC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE3G,IAAI,CAACC,mBAAmB,CAAC7E,IAAI,CAACqE,yBAAyB,CAAC,EAAE;UACxD;UACA;QACF;MACF;MAEAN,qBAAqB,GAAGC,uBAAuB,CAAChD,QAAQ,EAAE0C,MAAM,EAAEiB,eAAe,CAAC;MAElF,IAAIhB,WAAW,CAAC3C,QAAQ,EAAE0C,MAAM,EAAEG,mBAAmB,EAAEE,qBAAqB,CAAC,EAAE;QAC7E,OAAO,IAAI;MACb;IACF;EACF;EAEA,OAAO,KAAK;AACd;AACA;AACA;AACA;AACA;AACA;;AAGA,SAASC,uBAAuBA,CAAChD,QAAQ,EAAE0C,MAAM,EAAEqB,iBAAiB,EAAE;EACpE,MAAM,IAAInB,KAAK,CAAC,uCAAuC,CAAC;EAExD,IAAImB,iBAAiB,EAAE;IACrB;IACA,IAAIV,yBAAyB,GAAGC,IAAI,CAACC,4BAA4B,CAACb,MAAM,CAAC;IACzE,OAAOY,IAAI,CAACU,qBAAqB,CAACX,yBAAyB,EAAEU,iBAAiB,EAAE,SAAS,EAAE/D,QAAQ,CAAC,CAACiE,KAAK,CAAC,GAAG,CAAC;EACjH,CAAC,CAAC;;EAGF,IAAIC,aAAa,GAAGC,YAAY,CAACzB,MAAM,EAAE,SAAS,EAAE1C,QAAQ,CAAC,CAAC,CAAC;EAC/D;;EAEA,IAAIoE,QAAQ,GAAGF,aAAa,CAAC3B,OAAO,CAAC,GAAG,CAAC;EAEzC,IAAI6B,QAAQ,GAAG,CAAC,EAAE;IAChBA,QAAQ,GAAGF,aAAa,CAACjG,MAAM;EACjC,CAAC,CAAC;;EAGF,IAAIoG,UAAU,GAAGH,aAAa,CAAC3B,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC;EAC/C,OAAO2B,aAAa,CAACtF,KAAK,CAACyF,UAAU,EAAED,QAAQ,CAAC,CAACH,KAAK,CAAC,GAAG,CAAC;AAC7D;AAEA,SAASlD,gCAAgCA,CAACf,QAAQ,EAAE0C,MAAM,EAAEG,mBAAmB,EAAEE,qBAAqB,EAAE;EACtG,MAAM,IAAIH,KAAK,CAAC,uCAAuC,CAAC;EACxD,IAAI0B,eAAe,GAAGzB,mBAAmB,CAACoB,KAAK,CAACM,kBAAkB,CAAC,CAAC,CAAC;;EAErE,IAAIC,yBAAyB,GAAG9B,MAAM,CAAC+B,YAAY,CAAC,CAAC,GAAGH,eAAe,CAACrG,MAAM,GAAG,CAAC,GAAGqG,eAAe,CAACrG,MAAM,GAAG,CAAC,CAAC,CAAC;EACjH;EACA;;EAEA,IAAIqG,eAAe,CAACrG,MAAM,IAAI,CAAC,IAAIqG,eAAe,CAACE,yBAAyB,CAAC,CAACE,QAAQ,CAACpB,IAAI,CAACC,4BAA4B,CAACb,MAAM,CAAC,CAAC,EAAE;IACjI,OAAO,IAAI;EACb,CAAC,CAAC;EACF;;EAGA,IAAIiC,yBAAyB,GAAG5B,qBAAqB,CAAC9E,MAAM,GAAG,CAAC;EAEhE,OAAO0G,yBAAyB,GAAG,CAAC,IAAIH,yBAAyB,IAAI,CAAC,EAAE;IACtE,IAAIF,eAAe,CAACE,yBAAyB,CAAC,KAAKzB,qBAAqB,CAAC4B,yBAAyB,CAAC,EAAE;MACnG,OAAO,KAAK;IACd;IAEAA,yBAAyB,EAAE;IAC3BH,yBAAyB,EAAE;EAC7B,CAAC,CAAC;EACF;;EAGA,OAAOA,yBAAyB,IAAI,CAAC,IAAI7E,QAAQ,CAAC2E,eAAe,CAACE,yBAAyB,CAAC,EAAEzB,qBAAqB,CAAC,CAAC,CAAC,CAAC;AACzH;AAEA,SAASnC,4BAA4BA,CAACZ,QAAQ,EAAE0C,MAAM,EAAEG,mBAAmB,EAAEE,qBAAqB,EAAE;EAClG,MAAM,IAAIH,KAAK,CAAC,uCAAuC,CAAC;EACxD,IAAIgC,SAAS,GAAG,CAAC;EAEjB,IAAIlC,MAAM,CAACmC,oBAAoB,CAAC,CAAC,KAAKC,iBAAiB,CAACC,oBAAoB,EAAE;IAC5E;IACA,IAAIC,WAAW,GAAGC,MAAM,CAACvC,MAAM,CAACU,cAAc,CAAC,CAAC,CAAC;IACjDwB,SAAS,GAAG/B,mBAAmB,CAACN,OAAO,CAACyC,WAAW,CAAC,GAAGA,WAAW,CAAC/G,MAAM,CAAC,CAAC;EAC7E,CAAC,CAAC;EACF;;EAGA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6E,qBAAqB,CAAC9E,MAAM,EAAEC,CAAC,EAAE,EAAE;IACrD;IACA;IACA0G,SAAS,GAAG/B,mBAAmB,CAACN,OAAO,CAACQ,qBAAqB,CAAC7E,CAAC,CAAC,EAAE0G,SAAS,CAAC;IAE5E,IAAIA,SAAS,GAAG,CAAC,EAAE;MACjB,OAAO,KAAK;IACd,CAAC,CAAC;;IAGFA,SAAS,IAAI7B,qBAAqB,CAAC7E,CAAC,CAAC,CAACD,MAAM,CAAC,CAAC;IAE9C,IAAIC,CAAC,IAAI,CAAC,IAAI0G,SAAS,GAAG/B,mBAAmB,CAAC5E,MAAM,CAAC,CAAC,EAAE;MACtD;MACA;MACA;MACA;MACA,IAAIiH,MAAM,GAAG5B,IAAI,CAAC6B,2BAA2B,CAACzC,MAAM,CAACU,cAAc,CAAC,CAAC,CAAC;MAEtE,IAAIE,IAAI,CAAC8B,qBAAqB,CAACF,MAAM,EAAE,IAAI,CAAC,IAAI,IAAI,IAAIG,SAAS,CAACC,OAAO,CAACzC,mBAAmB,CAAC3B,MAAM,CAAC0D,SAAS,CAAC,CAAC,EAAE;QAChH;QACA;QACA;QACA,IAAIvB,yBAAyB,GAAGC,IAAI,CAACC,4BAA4B,CAACb,MAAM,CAAC;QACzE,OAAOhD,UAAU,CAACmD,mBAAmB,CAACjE,KAAK,CAACgG,SAAS,GAAG7B,qBAAqB,CAAC7E,CAAC,CAAC,CAACD,MAAM,CAAC,EAAEoF,yBAAyB,CAAC;MACtH;IACF;EACF,CAAC,CAAC;EACF;EACA;;EAGA,OAAOR,mBAAmB,CAACjE,KAAK,CAACgG,SAAS,CAAC,CAACF,QAAQ,CAAChC,MAAM,CAAC6C,YAAY,CAAC,CAAC,CAAC;AAC7E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}