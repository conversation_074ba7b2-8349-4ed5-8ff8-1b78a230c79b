<div class="col-12 all-overview-body m-0 p-0 border-round-lg">
    <div class="filter-sec pb-3 flex align-items-center justify-content-between">
        <div class="breadcrumb-sec">
            <p-breadcrumb [model]="items" [home]="home" [styleClass]="'py-2 px-0 border-none'" />
        </div>

        <div class="flex align-items-center gap-3">
            <p-multiSelect [options]="OrgCols" [(ngModel)]="selectedOrgColumns" optionLabel="header"
                class="table-multiselect-dropdown"
                [styleClass]="'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'">
            </p-multiSelect>
        </div>
    </div>
    <div class="shadow-1 border-round-xl surface-0 p-5 mb-4 border-1 border-solid border-50">
        <div class="filter-sec grid mt-0 mb-5">
            <div class="col-3">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-600">calendar_month</span> Date From
                    </label>
                    <p-calendar [(ngModel)]="searchParams.fromDate" [showIcon]="true" styleClass="h-3rem w-full"
                        [maxDate]="maxDate"></p-calendar>
                </div>
            </div>
            <div class="col-3">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-600">calendar_month</span> Date To
                    </label>
                    <p-calendar [(ngModel)]="searchParams.toDate" [showIcon]="true" styleClass="h-3rem w-full"
                        [minDate]="searchParams.fromDate" [maxDate]="maxDate"></p-calendar>
                </div>
            </div>
            <div class="col-3">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-600">order_approve</span> Ticket Status
                    </label>
                    <select class="p-inputtext p-component w-full h-3rem appearance-auto"
                        [(ngModel)]="searchParams.status">
                        <option *ngFor="let status of statuses" [value]="status.code">
                            {{ status.description }}
                        </option>
                    </select>
                </div>
            </div>
            <div class="col-3">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-600">list_alt</span> Ticket #
                    </label>
                    <input pInputText [(ngModel)]="searchParams.ticketNo" placeholder="Ticket #"
                        class="p-inputtext h-3rem w-full" />
                </div>
            </div>
        </div>
        <div class="flex align-items-center justify-content-center gap-3">
            <button pButton type="button" label="Clear"
                class="p-button-rounded bg-light-blue border-none text-primary-700 font-medium justify-content-center w-9rem h-3rem"
                (click)="clear()"></button>
            <button pButton type="submit" class="p-button-rounded justify-content-center w-9rem h-3rem"
                (click)="search()" [disabled]="loading">{{loading ?
                'Searching...' : 'Search'}}</button>
        </div>
    </div>
    <div class="table-sec">
        
        <p-table #myTab [value]="tickets" dataKey="id" [rows]="10" [loading]="loading" [paginator]="true"
            *ngIf="tickets.length" [lazy]="true" [totalRecords]="totalRecords"
            (onLazyLoad)="loadTickets($event)" responsiveLayout="scroll" styleClass="w-full"
            [scrollable]="true" (onColReorder)="onOrgColumnReorder($event)" [reorderableColumns]="true"
            class="scrollable-table" (onRowSelect)="goToTicket($event)" selectionMode="single">

            <ng-template pTemplate="header">
                <tr>
                    <th pFrozenColumn (click)="customSort('id', tickets, 'ORG')" class="border-round-left-lg">
                        <div class="flex align-items-end cursor-pointer">
                            Ticket #
                            <i *ngIf="sortFieldOrg === 'id'" class="ml-2 pi"
                                [ngClass]="sortOrderOrg === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'"></i>
                            <i *ngIf="sortFieldOrg !== 'id'" class="ml-2 pi pi-sort"></i>
                        </div>
                    </th>
                    <ng-container *ngFor="let col of selectedOrgColumns">
                        <th [pSortableColumn]="col.field" pReorderableColumn
                            (click)="customSort(col.field, tickets, 'ORG')">
                            <div class="flex align-items-end cursor-pointer">
                                {{ col.header }}
                                <i *ngIf="sortFieldOrg === col.field" class="ml-2 pi"
                                    [ngClass]="sortOrderOrg === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'"></i>
                                <i *ngIf="sortFieldOrg !== col.field" class="ml-2 pi pi-sort"></i>
                            </div>
                        </th>
                    </ng-container>
                </tr>
            </ng-template>

            <ng-template pTemplate="body" let-ticket let-columns="columns">
                <tr class="cursor-pointer" [pSelectableRow]="ticket">
                    <td pFrozenColumn class="text-orange-600 cursor-pointer font-semibold border-round-left-lg">
                        {{ ticket.id }}
                    </td>

                    <ng-container *ngFor="let col of selectedOrgColumns">
                        <td>
                            <ng-container [ngSwitch]="col.field">

                                <ng-container *ngSwitchCase="'account_id'">
                                    {{ getBusinessPartnerName(ticket.account_id) }}
                                </ng-container>

                                <ng-container *ngSwitchCase="'contact_id'">
                                    {{ getBusinessPartnerName(ticket.contact_id) }}
                                </ng-container>

                                <ng-container *ngSwitchCase="'assigned_to'">
                                    {{ ticket.assigned_to}}
                                </ng-container>

                                 <ng-container *ngSwitchCase="'support_team'">
                                    {{ ticket.support_team}}
                                </ng-container>

                                <ng-container *ngSwitchCase="'status_id'">
                                    <span class="capitalize">{{ statusByCode[ticket.status_id] }}</span>
                                </ng-container>

                                <ng-container *ngSwitchCase="'createdAt'">
                                    {{ ticket.createdAt | date: 'dd/MM/yyyy' }}
                                </ng-container>

                                <ng-container *ngSwitchCase="'description'">
                                    {{ ticket.description || '-' }}
                                </ng-container>

                            </ng-container>
                        </td>
                    </ng-container>
                </tr>
            </ng-template>
        </p-table>
        <div class="w-100" *ngIf="!loading && !tickets.length">{{ 'No records found.'}}</div>
    </div>
</div>