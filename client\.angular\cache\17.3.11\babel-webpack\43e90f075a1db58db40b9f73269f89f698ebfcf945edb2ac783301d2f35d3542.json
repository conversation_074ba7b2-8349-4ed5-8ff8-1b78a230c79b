{"ast": null, "code": "function _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n  return arr2;\n}\nfunction _iterableToArrayLimit(arr, i) {\n  var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n  if (_i == null) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n  var _s, _e;\n  try {\n    for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n  return _arr;\n}\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\nimport _formatNumber from '../format.js';\nimport parse from '../parse.js';\nimport isObject from '../helpers/isObject.js';\nexport default function formatNumber() {\n  var _normalizeArguments = normalizeArguments(arguments),\n    input = _normalizeArguments.input,\n    format = _normalizeArguments.format,\n    options = _normalizeArguments.options,\n    metadata = _normalizeArguments.metadata;\n  return _formatNumber(input, format, options, metadata);\n} // Sort out arguments\n\nfunction normalizeArguments(args) {\n  var _Array$prototype$slic = Array.prototype.slice.call(args),\n    _Array$prototype$slic2 = _slicedToArray(_Array$prototype$slic, 5),\n    arg_1 = _Array$prototype$slic2[0],\n    arg_2 = _Array$prototype$slic2[1],\n    arg_3 = _Array$prototype$slic2[2],\n    arg_4 = _Array$prototype$slic2[3],\n    arg_5 = _Array$prototype$slic2[4];\n  var input;\n  var format;\n  var options;\n  var metadata; // Sort out arguments.\n  // If the phone number is passed as a string.\n  // `format('8005553535', ...)`.\n\n  if (typeof arg_1 === 'string') {\n    // If country code is supplied.\n    // `format('8005553535', 'RU', 'NATIONAL', [options], metadata)`.\n    if (typeof arg_3 === 'string') {\n      format = arg_3;\n      if (arg_5) {\n        options = arg_4;\n        metadata = arg_5;\n      } else {\n        metadata = arg_4;\n      }\n      input = parse(arg_1, {\n        defaultCountry: arg_2,\n        extended: true\n      }, metadata);\n    } // Just an international phone number is supplied\n    // `format('+78005553535', 'NATIONAL', [options], metadata)`.\n    else {\n      if (typeof arg_2 !== 'string') {\n        throw new Error('`format` argument not passed to `formatNumber(number, format)`');\n      }\n      format = arg_2;\n      if (arg_4) {\n        options = arg_3;\n        metadata = arg_4;\n      } else {\n        metadata = arg_3;\n      }\n      input = parse(arg_1, {\n        extended: true\n      }, metadata);\n    }\n  } // If the phone number is passed as a parsed number object.\n  // `format({ phone: '8005553535', country: 'RU' }, 'NATIONAL', [options], metadata)`.\n  else if (isObject(arg_1)) {\n    input = arg_1;\n    format = arg_2;\n    if (arg_4) {\n      options = arg_3;\n      metadata = arg_4;\n    } else {\n      metadata = arg_3;\n    }\n  } else throw new TypeError('A phone number must either be a string or an object of shape { phone, [country] }.'); // Legacy lowercase formats.\n\n  if (format === 'International') {\n    format = 'INTERNATIONAL';\n  } else if (format === 'National') {\n    format = 'NATIONAL';\n  }\n  return {\n    input: input,\n    format: format,\n    options: options,\n    metadata: metadata\n  };\n}", "map": {"version": 3, "names": ["_slicedToArray", "arr", "i", "_arrayWithHoles", "_iterableToArrayLimit", "_unsupportedIterableToArray", "_nonIterableRest", "TypeError", "o", "minLen", "_arrayLikeToArray", "n", "Object", "prototype", "toString", "call", "slice", "constructor", "name", "Array", "from", "test", "len", "length", "arr2", "_i", "Symbol", "iterator", "_arr", "_n", "_d", "_s", "_e", "next", "done", "push", "value", "err", "isArray", "_formatNumber", "parse", "isObject", "formatNumber", "_normalizeArguments", "normalizeArguments", "arguments", "input", "format", "options", "metadata", "args", "_Array$prototype$slic", "_Array$prototype$slic2", "arg_1", "arg_2", "arg_3", "arg_4", "arg_5", "defaultCountry", "extended", "Error"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/legacy/format.js"], "sourcesContent": ["function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\nimport _formatNumber from '../format.js';\nimport parse from '../parse.js';\nimport isObject from '../helpers/isObject.js';\nexport default function formatNumber() {\n  var _normalizeArguments = normalizeArguments(arguments),\n      input = _normalizeArguments.input,\n      format = _normalizeArguments.format,\n      options = _normalizeArguments.options,\n      metadata = _normalizeArguments.metadata;\n\n  return _formatNumber(input, format, options, metadata);\n} // Sort out arguments\n\nfunction normalizeArguments(args) {\n  var _Array$prototype$slic = Array.prototype.slice.call(args),\n      _Array$prototype$slic2 = _slicedToArray(_Array$prototype$slic, 5),\n      arg_1 = _Array$prototype$slic2[0],\n      arg_2 = _Array$prototype$slic2[1],\n      arg_3 = _Array$prototype$slic2[2],\n      arg_4 = _Array$prototype$slic2[3],\n      arg_5 = _Array$prototype$slic2[4];\n\n  var input;\n  var format;\n  var options;\n  var metadata; // Sort out arguments.\n  // If the phone number is passed as a string.\n  // `format('8005553535', ...)`.\n\n  if (typeof arg_1 === 'string') {\n    // If country code is supplied.\n    // `format('8005553535', 'RU', 'NATIONAL', [options], metadata)`.\n    if (typeof arg_3 === 'string') {\n      format = arg_3;\n\n      if (arg_5) {\n        options = arg_4;\n        metadata = arg_5;\n      } else {\n        metadata = arg_4;\n      }\n\n      input = parse(arg_1, {\n        defaultCountry: arg_2,\n        extended: true\n      }, metadata);\n    } // Just an international phone number is supplied\n    // `format('+78005553535', 'NATIONAL', [options], metadata)`.\n    else {\n      if (typeof arg_2 !== 'string') {\n        throw new Error('`format` argument not passed to `formatNumber(number, format)`');\n      }\n\n      format = arg_2;\n\n      if (arg_4) {\n        options = arg_3;\n        metadata = arg_4;\n      } else {\n        metadata = arg_3;\n      }\n\n      input = parse(arg_1, {\n        extended: true\n      }, metadata);\n    }\n  } // If the phone number is passed as a parsed number object.\n  // `format({ phone: '8005553535', country: 'RU' }, 'NATIONAL', [options], metadata)`.\n  else if (isObject(arg_1)) {\n    input = arg_1;\n    format = arg_2;\n\n    if (arg_4) {\n      options = arg_3;\n      metadata = arg_4;\n    } else {\n      metadata = arg_3;\n    }\n  } else throw new TypeError('A phone number must either be a string or an object of shape { phone, [country] }.'); // Legacy lowercase formats.\n\n\n  if (format === 'International') {\n    format = 'INTERNATIONAL';\n  } else if (format === 'National') {\n    format = 'NATIONAL';\n  }\n\n  return {\n    input: input,\n    format: format,\n    options: options,\n    metadata: metadata\n  };\n}\n"], "mappings": "AAAA,SAASA,cAAcA,CAACC,GAAG,EAAEC,CAAC,EAAE;EAAE,OAAOC,eAAe,CAACF,GAAG,CAAC,IAAIG,qBAAqB,CAACH,GAAG,EAAEC,CAAC,CAAC,IAAIG,2BAA2B,CAACJ,GAAG,EAAEC,CAAC,CAAC,IAAII,gBAAgB,CAAC,CAAC;AAAE;AAE7J,SAASA,gBAAgBA,CAAA,EAAG;EAAE,MAAM,IAAIC,SAAS,CAAC,2IAA2I,CAAC;AAAE;AAEhM,SAASF,2BAA2BA,CAACG,CAAC,EAAEC,MAAM,EAAE;EAAE,IAAI,CAACD,CAAC,EAAE;EAAQ,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOE,iBAAiB,CAACF,CAAC,EAAEC,MAAM,CAAC;EAAE,IAAIE,CAAC,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACP,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAE,IAAIL,CAAC,KAAK,QAAQ,IAAIH,CAAC,CAACS,WAAW,EAAEN,CAAC,GAAGH,CAAC,CAACS,WAAW,CAACC,IAAI;EAAE,IAAIP,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOQ,KAAK,CAACC,IAAI,CAACZ,CAAC,CAAC;EAAE,IAAIG,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACU,IAAI,CAACV,CAAC,CAAC,EAAE,OAAOD,iBAAiB,CAACF,CAAC,EAAEC,MAAM,CAAC;AAAE;AAE/Z,SAASC,iBAAiBA,CAACT,GAAG,EAAEqB,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGrB,GAAG,CAACsB,MAAM,EAAED,GAAG,GAAGrB,GAAG,CAACsB,MAAM;EAAE,KAAK,IAAIrB,CAAC,GAAG,CAAC,EAAEsB,IAAI,GAAG,IAAIL,KAAK,CAACG,GAAG,CAAC,EAAEpB,CAAC,GAAGoB,GAAG,EAAEpB,CAAC,EAAE,EAAE;IAAEsB,IAAI,CAACtB,CAAC,CAAC,GAAGD,GAAG,CAACC,CAAC,CAAC;EAAE;EAAE,OAAOsB,IAAI;AAAE;AAEtL,SAASpB,qBAAqBA,CAACH,GAAG,EAAEC,CAAC,EAAE;EAAE,IAAIuB,EAAE,GAAGxB,GAAG,IAAI,IAAI,GAAG,IAAI,GAAG,OAAOyB,MAAM,KAAK,WAAW,IAAIzB,GAAG,CAACyB,MAAM,CAACC,QAAQ,CAAC,IAAI1B,GAAG,CAAC,YAAY,CAAC;EAAE,IAAIwB,EAAE,IAAI,IAAI,EAAE;EAAQ,IAAIG,IAAI,GAAG,EAAE;EAAE,IAAIC,EAAE,GAAG,IAAI;EAAE,IAAIC,EAAE,GAAG,KAAK;EAAE,IAAIC,EAAE,EAAEC,EAAE;EAAE,IAAI;IAAE,KAAKP,EAAE,GAAGA,EAAE,CAACV,IAAI,CAACd,GAAG,CAAC,EAAE,EAAE4B,EAAE,GAAG,CAACE,EAAE,GAAGN,EAAE,CAACQ,IAAI,CAAC,CAAC,EAAEC,IAAI,CAAC,EAAEL,EAAE,GAAG,IAAI,EAAE;MAAED,IAAI,CAACO,IAAI,CAACJ,EAAE,CAACK,KAAK,CAAC;MAAE,IAAIlC,CAAC,IAAI0B,IAAI,CAACL,MAAM,KAAKrB,CAAC,EAAE;IAAO;EAAE,CAAC,CAAC,OAAOmC,GAAG,EAAE;IAAEP,EAAE,GAAG,IAAI;IAAEE,EAAE,GAAGK,GAAG;EAAE,CAAC,SAAS;IAAE,IAAI;MAAE,IAAI,CAACR,EAAE,IAAIJ,EAAE,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAEA,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAAE,CAAC,SAAS;MAAE,IAAIK,EAAE,EAAE,MAAME,EAAE;IAAE;EAAE;EAAE,OAAOJ,IAAI;AAAE;AAEhgB,SAASzB,eAAeA,CAACF,GAAG,EAAE;EAAE,IAAIkB,KAAK,CAACmB,OAAO,CAACrC,GAAG,CAAC,EAAE,OAAOA,GAAG;AAAE;AAEpE,OAAOsC,aAAa,MAAM,cAAc;AACxC,OAAOC,KAAK,MAAM,aAAa;AAC/B,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,eAAe,SAASC,YAAYA,CAAA,EAAG;EACrC,IAAIC,mBAAmB,GAAGC,kBAAkB,CAACC,SAAS,CAAC;IACnDC,KAAK,GAAGH,mBAAmB,CAACG,KAAK;IACjCC,MAAM,GAAGJ,mBAAmB,CAACI,MAAM;IACnCC,OAAO,GAAGL,mBAAmB,CAACK,OAAO;IACrCC,QAAQ,GAAGN,mBAAmB,CAACM,QAAQ;EAE3C,OAAOV,aAAa,CAACO,KAAK,EAAEC,MAAM,EAAEC,OAAO,EAAEC,QAAQ,CAAC;AACxD,CAAC,CAAC;;AAEF,SAASL,kBAAkBA,CAACM,IAAI,EAAE;EAChC,IAAIC,qBAAqB,GAAGhC,KAAK,CAACN,SAAS,CAACG,KAAK,CAACD,IAAI,CAACmC,IAAI,CAAC;IACxDE,sBAAsB,GAAGpD,cAAc,CAACmD,qBAAqB,EAAE,CAAC,CAAC;IACjEE,KAAK,GAAGD,sBAAsB,CAAC,CAAC,CAAC;IACjCE,KAAK,GAAGF,sBAAsB,CAAC,CAAC,CAAC;IACjCG,KAAK,GAAGH,sBAAsB,CAAC,CAAC,CAAC;IACjCI,KAAK,GAAGJ,sBAAsB,CAAC,CAAC,CAAC;IACjCK,KAAK,GAAGL,sBAAsB,CAAC,CAAC,CAAC;EAErC,IAAIN,KAAK;EACT,IAAIC,MAAM;EACV,IAAIC,OAAO;EACX,IAAIC,QAAQ,CAAC,CAAC;EACd;EACA;;EAEA,IAAI,OAAOI,KAAK,KAAK,QAAQ,EAAE;IAC7B;IACA;IACA,IAAI,OAAOE,KAAK,KAAK,QAAQ,EAAE;MAC7BR,MAAM,GAAGQ,KAAK;MAEd,IAAIE,KAAK,EAAE;QACTT,OAAO,GAAGQ,KAAK;QACfP,QAAQ,GAAGQ,KAAK;MAClB,CAAC,MAAM;QACLR,QAAQ,GAAGO,KAAK;MAClB;MAEAV,KAAK,GAAGN,KAAK,CAACa,KAAK,EAAE;QACnBK,cAAc,EAAEJ,KAAK;QACrBK,QAAQ,EAAE;MACZ,CAAC,EAAEV,QAAQ,CAAC;IACd,CAAC,CAAC;IACF;IAAA,KACK;MACH,IAAI,OAAOK,KAAK,KAAK,QAAQ,EAAE;QAC7B,MAAM,IAAIM,KAAK,CAAC,gEAAgE,CAAC;MACnF;MAEAb,MAAM,GAAGO,KAAK;MAEd,IAAIE,KAAK,EAAE;QACTR,OAAO,GAAGO,KAAK;QACfN,QAAQ,GAAGO,KAAK;MAClB,CAAC,MAAM;QACLP,QAAQ,GAAGM,KAAK;MAClB;MAEAT,KAAK,GAAGN,KAAK,CAACa,KAAK,EAAE;QACnBM,QAAQ,EAAE;MACZ,CAAC,EAAEV,QAAQ,CAAC;IACd;EACF,CAAC,CAAC;EACF;EAAA,KACK,IAAIR,QAAQ,CAACY,KAAK,CAAC,EAAE;IACxBP,KAAK,GAAGO,KAAK;IACbN,MAAM,GAAGO,KAAK;IAEd,IAAIE,KAAK,EAAE;MACTR,OAAO,GAAGO,KAAK;MACfN,QAAQ,GAAGO,KAAK;IAClB,CAAC,MAAM;MACLP,QAAQ,GAAGM,KAAK;IAClB;EACF,CAAC,MAAM,MAAM,IAAIhD,SAAS,CAAC,oFAAoF,CAAC,CAAC,CAAC;;EAGlH,IAAIwC,MAAM,KAAK,eAAe,EAAE;IAC9BA,MAAM,GAAG,eAAe;EAC1B,CAAC,MAAM,IAAIA,MAAM,KAAK,UAAU,EAAE;IAChCA,MAAM,GAAG,UAAU;EACrB;EAEA,OAAO;IACLD,KAAK,EAAEA,KAAK;IACZC,MAAM,EAAEA,MAAM;IACdC,OAAO,EAAEA,OAAO;IAChBC,QAAQ,EAAEA;EACZ,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}