{"ast": null, "code": "function ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n  return arr2;\n}\nfunction _iterableToArrayLimit(arr, i) {\n  var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n  if (_i == null) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n  var _s, _e;\n  try {\n    for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n  return _arr;\n}\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\nimport isObject from './helpers/isObject.js'; // Extracts the following properties from function arguments:\n// * input `text`\n// * `options` object\n// * `metadata` JSON\n\nexport default function normalizeArguments(args) {\n  var _Array$prototype$slic = Array.prototype.slice.call(args),\n    _Array$prototype$slic2 = _slicedToArray(_Array$prototype$slic, 4),\n    arg_1 = _Array$prototype$slic2[0],\n    arg_2 = _Array$prototype$slic2[1],\n    arg_3 = _Array$prototype$slic2[2],\n    arg_4 = _Array$prototype$slic2[3];\n  var text;\n  var options;\n  var metadata; // If the phone number is passed as a string.\n  // `parsePhoneNumber('88005553535', ...)`.\n\n  if (typeof arg_1 === 'string') {\n    text = arg_1;\n  } else throw new TypeError('A text for parsing must be a string.'); // If \"default country\" argument is being passed then move it to `options`.\n  // `parsePhoneNumber('88005553535', 'RU', [options], metadata)`.\n\n  if (!arg_2 || typeof arg_2 === 'string') {\n    if (arg_4) {\n      options = arg_3;\n      metadata = arg_4;\n    } else {\n      options = undefined;\n      metadata = arg_3;\n    }\n    if (arg_2) {\n      options = _objectSpread({\n        defaultCountry: arg_2\n      }, options);\n    }\n  } // `defaultCountry` is not passed.\n  // Example: `parsePhoneNumber('+78005553535', [options], metadata)`.\n  else if (isObject(arg_2)) {\n    if (arg_3) {\n      options = arg_2;\n      metadata = arg_3;\n    } else {\n      metadata = arg_2;\n    }\n  } else throw new Error(\"Invalid second argument: \".concat(arg_2));\n  return {\n    text: text,\n    options: options,\n    metadata: metadata\n  };\n}", "map": {"version": 3, "names": ["ownKeys", "object", "enumerableOnly", "keys", "Object", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "target", "i", "arguments", "length", "source", "for<PERSON>ach", "key", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "obj", "value", "configurable", "writable", "_slicedToArray", "arr", "_arrayWithHoles", "_iterableToArrayLimit", "_unsupportedIterableToArray", "_nonIterableRest", "TypeError", "o", "minLen", "_arrayLikeToArray", "n", "prototype", "toString", "call", "slice", "constructor", "name", "Array", "from", "test", "len", "arr2", "_i", "Symbol", "iterator", "_arr", "_n", "_d", "_s", "_e", "next", "done", "err", "isArray", "isObject", "normalizeArguments", "args", "_Array$prototype$slic", "_Array$prototype$slic2", "arg_1", "arg_2", "arg_3", "arg_4", "text", "options", "metadata", "undefined", "defaultCountry", "Error", "concat"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/normalizeArguments.js"], "sourcesContent": ["function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\nimport isObject from './helpers/isObject.js'; // Extracts the following properties from function arguments:\n// * input `text`\n// * `options` object\n// * `metadata` JSON\n\nexport default function normalizeArguments(args) {\n  var _Array$prototype$slic = Array.prototype.slice.call(args),\n      _Array$prototype$slic2 = _slicedToArray(_Array$prototype$slic, 4),\n      arg_1 = _Array$prototype$slic2[0],\n      arg_2 = _Array$prototype$slic2[1],\n      arg_3 = _Array$prototype$slic2[2],\n      arg_4 = _Array$prototype$slic2[3];\n\n  var text;\n  var options;\n  var metadata; // If the phone number is passed as a string.\n  // `parsePhoneNumber('88005553535', ...)`.\n\n  if (typeof arg_1 === 'string') {\n    text = arg_1;\n  } else throw new TypeError('A text for parsing must be a string.'); // If \"default country\" argument is being passed then move it to `options`.\n  // `parsePhoneNumber('88005553535', 'RU', [options], metadata)`.\n\n\n  if (!arg_2 || typeof arg_2 === 'string') {\n    if (arg_4) {\n      options = arg_3;\n      metadata = arg_4;\n    } else {\n      options = undefined;\n      metadata = arg_3;\n    }\n\n    if (arg_2) {\n      options = _objectSpread({\n        defaultCountry: arg_2\n      }, options);\n    }\n  } // `defaultCountry` is not passed.\n  // Example: `parsePhoneNumber('+78005553535', [options], metadata)`.\n  else if (isObject(arg_2)) {\n    if (arg_3) {\n      options = arg_2;\n      metadata = arg_3;\n    } else {\n      metadata = arg_2;\n    }\n  } else throw new Error(\"Invalid second argument: \".concat(arg_2));\n\n  return {\n    text: text,\n    options: options,\n    metadata: metadata\n  };\n}\n"], "mappings": "AAAA,SAASA,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACF,MAAM,CAAC;EAAE,IAAIG,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,OAAO,GAAGF,MAAM,CAACC,qBAAqB,CAACJ,MAAM,CAAC;IAAEC,cAAc,KAAKI,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;MAAE,OAAOJ,MAAM,CAACK,wBAAwB,CAACR,MAAM,EAAEO,GAAG,CAAC,CAACE,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEP,IAAI,CAACQ,IAAI,CAACC,KAAK,CAACT,IAAI,EAAEG,OAAO,CAAC;EAAE;EAAE,OAAOH,IAAI;AAAE;AAEpV,SAASU,aAAaA,CAACC,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAG,IAAI,IAAIF,SAAS,CAACD,CAAC,CAAC,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGf,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;MAAEC,eAAe,CAACP,MAAM,EAAEM,GAAG,EAAEF,MAAM,CAACE,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGhB,MAAM,CAACkB,yBAAyB,GAAGlB,MAAM,CAACmB,gBAAgB,CAACT,MAAM,EAAEV,MAAM,CAACkB,yBAAyB,CAACJ,MAAM,CAAC,CAAC,GAAGlB,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;MAAEhB,MAAM,CAACoB,cAAc,CAACV,MAAM,EAAEM,GAAG,EAAEhB,MAAM,CAACK,wBAAwB,CAACS,MAAM,EAAEE,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAON,MAAM;AAAE;AAEzf,SAASO,eAAeA,CAACI,GAAG,EAAEL,GAAG,EAAEM,KAAK,EAAE;EAAE,IAAIN,GAAG,IAAIK,GAAG,EAAE;IAAErB,MAAM,CAACoB,cAAc,CAACC,GAAG,EAAEL,GAAG,EAAE;MAAEM,KAAK,EAAEA,KAAK;MAAEhB,UAAU,EAAE,IAAI;MAAEiB,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEH,GAAG,CAACL,GAAG,CAAC,GAAGM,KAAK;EAAE;EAAE,OAAOD,GAAG;AAAE;AAEhN,SAASI,cAAcA,CAACC,GAAG,EAAEf,CAAC,EAAE;EAAE,OAAOgB,eAAe,CAACD,GAAG,CAAC,IAAIE,qBAAqB,CAACF,GAAG,EAAEf,CAAC,CAAC,IAAIkB,2BAA2B,CAACH,GAAG,EAAEf,CAAC,CAAC,IAAImB,gBAAgB,CAAC,CAAC;AAAE;AAE7J,SAASA,gBAAgBA,CAAA,EAAG;EAAE,MAAM,IAAIC,SAAS,CAAC,2IAA2I,CAAC;AAAE;AAEhM,SAASF,2BAA2BA,CAACG,CAAC,EAAEC,MAAM,EAAE;EAAE,IAAI,CAACD,CAAC,EAAE;EAAQ,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOE,iBAAiB,CAACF,CAAC,EAAEC,MAAM,CAAC;EAAE,IAAIE,CAAC,GAAGnC,MAAM,CAACoC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACN,CAAC,CAAC,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAE,IAAIJ,CAAC,KAAK,QAAQ,IAAIH,CAAC,CAACQ,WAAW,EAAEL,CAAC,GAAGH,CAAC,CAACQ,WAAW,CAACC,IAAI;EAAE,IAAIN,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOO,KAAK,CAACC,IAAI,CAACX,CAAC,CAAC;EAAE,IAAIG,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACS,IAAI,CAACT,CAAC,CAAC,EAAE,OAAOD,iBAAiB,CAACF,CAAC,EAAEC,MAAM,CAAC;AAAE;AAE/Z,SAASC,iBAAiBA,CAACR,GAAG,EAAEmB,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGnB,GAAG,CAACb,MAAM,EAAEgC,GAAG,GAAGnB,GAAG,CAACb,MAAM;EAAE,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEmC,IAAI,GAAG,IAAIJ,KAAK,CAACG,GAAG,CAAC,EAAElC,CAAC,GAAGkC,GAAG,EAAElC,CAAC,EAAE,EAAE;IAAEmC,IAAI,CAACnC,CAAC,CAAC,GAAGe,GAAG,CAACf,CAAC,CAAC;EAAE;EAAE,OAAOmC,IAAI;AAAE;AAEtL,SAASlB,qBAAqBA,CAACF,GAAG,EAAEf,CAAC,EAAE;EAAE,IAAIoC,EAAE,GAAGrB,GAAG,IAAI,IAAI,GAAG,IAAI,GAAG,OAAOsB,MAAM,KAAK,WAAW,IAAItB,GAAG,CAACsB,MAAM,CAACC,QAAQ,CAAC,IAAIvB,GAAG,CAAC,YAAY,CAAC;EAAE,IAAIqB,EAAE,IAAI,IAAI,EAAE;EAAQ,IAAIG,IAAI,GAAG,EAAE;EAAE,IAAIC,EAAE,GAAG,IAAI;EAAE,IAAIC,EAAE,GAAG,KAAK;EAAE,IAAIC,EAAE,EAAEC,EAAE;EAAE,IAAI;IAAE,KAAKP,EAAE,GAAGA,EAAE,CAACT,IAAI,CAACZ,GAAG,CAAC,EAAE,EAAEyB,EAAE,GAAG,CAACE,EAAE,GAAGN,EAAE,CAACQ,IAAI,CAAC,CAAC,EAAEC,IAAI,CAAC,EAAEL,EAAE,GAAG,IAAI,EAAE;MAAED,IAAI,CAAC3C,IAAI,CAAC8C,EAAE,CAAC/B,KAAK,CAAC;MAAE,IAAIX,CAAC,IAAIuC,IAAI,CAACrC,MAAM,KAAKF,CAAC,EAAE;IAAO;EAAE,CAAC,CAAC,OAAO8C,GAAG,EAAE;IAAEL,EAAE,GAAG,IAAI;IAAEE,EAAE,GAAGG,GAAG;EAAE,CAAC,SAAS;IAAE,IAAI;MAAE,IAAI,CAACN,EAAE,IAAIJ,EAAE,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAEA,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAAE,CAAC,SAAS;MAAE,IAAIK,EAAE,EAAE,MAAME,EAAE;IAAE;EAAE;EAAE,OAAOJ,IAAI;AAAE;AAEhgB,SAASvB,eAAeA,CAACD,GAAG,EAAE;EAAE,IAAIgB,KAAK,CAACgB,OAAO,CAAChC,GAAG,CAAC,EAAE,OAAOA,GAAG;AAAE;AAEpE,OAAOiC,QAAQ,MAAM,uBAAuB,CAAC,CAAC;AAC9C;AACA;AACA;;AAEA,eAAe,SAASC,kBAAkBA,CAACC,IAAI,EAAE;EAC/C,IAAIC,qBAAqB,GAAGpB,KAAK,CAACN,SAAS,CAACG,KAAK,CAACD,IAAI,CAACuB,IAAI,CAAC;IACxDE,sBAAsB,GAAGtC,cAAc,CAACqC,qBAAqB,EAAE,CAAC,CAAC;IACjEE,KAAK,GAAGD,sBAAsB,CAAC,CAAC,CAAC;IACjCE,KAAK,GAAGF,sBAAsB,CAAC,CAAC,CAAC;IACjCG,KAAK,GAAGH,sBAAsB,CAAC,CAAC,CAAC;IACjCI,KAAK,GAAGJ,sBAAsB,CAAC,CAAC,CAAC;EAErC,IAAIK,IAAI;EACR,IAAIC,OAAO;EACX,IAAIC,QAAQ,CAAC,CAAC;EACd;;EAEA,IAAI,OAAON,KAAK,KAAK,QAAQ,EAAE;IAC7BI,IAAI,GAAGJ,KAAK;EACd,CAAC,MAAM,MAAM,IAAIjC,SAAS,CAAC,sCAAsC,CAAC,CAAC,CAAC;EACpE;;EAGA,IAAI,CAACkC,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IACvC,IAAIE,KAAK,EAAE;MACTE,OAAO,GAAGH,KAAK;MACfI,QAAQ,GAAGH,KAAK;IAClB,CAAC,MAAM;MACLE,OAAO,GAAGE,SAAS;MACnBD,QAAQ,GAAGJ,KAAK;IAClB;IAEA,IAAID,KAAK,EAAE;MACTI,OAAO,GAAG5D,aAAa,CAAC;QACtB+D,cAAc,EAAEP;MAClB,CAAC,EAAEI,OAAO,CAAC;IACb;EACF,CAAC,CAAC;EACF;EAAA,KACK,IAAIV,QAAQ,CAACM,KAAK,CAAC,EAAE;IACxB,IAAIC,KAAK,EAAE;MACTG,OAAO,GAAGJ,KAAK;MACfK,QAAQ,GAAGJ,KAAK;IAClB,CAAC,MAAM;MACLI,QAAQ,GAAGL,KAAK;IAClB;EACF,CAAC,MAAM,MAAM,IAAIQ,KAAK,CAAC,2BAA2B,CAACC,MAAM,CAACT,KAAK,CAAC,CAAC;EAEjE,OAAO;IACLG,IAAI,EAAEA,IAAI;IACVC,OAAO,EAAEA,OAAO;IAChBC,QAAQ,EAAEA;EACZ,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}