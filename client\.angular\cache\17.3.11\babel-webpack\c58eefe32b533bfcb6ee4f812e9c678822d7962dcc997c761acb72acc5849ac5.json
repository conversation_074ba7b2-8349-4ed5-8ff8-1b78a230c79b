{"ast": null, "code": "function _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nimport LRUCache from './LRUCache.js'; // A cache for frequently used country-specific regular expressions. Set to 32 to cover ~2-3\n// countries being used for the same doc with ~10 patterns for each country. Some pages will have\n// a lot more countries in use, but typically fewer numbers for each so expanding the cache for\n// that use-case won't have a lot of benefit.\n\nvar RegExpCache = /*#__PURE__*/function () {\n  function RegExpCache(size) {\n    _classCallCheck(this, RegExpCache);\n    this.cache = new LRUCache(size);\n  }\n  _createClass(RegExpCache, [{\n    key: \"getPatternForRegExp\",\n    value: function getPatternForRegExp(pattern) {\n      var regExp = this.cache.get(pattern);\n      if (!regExp) {\n        regExp = new RegExp('^' + pattern);\n        this.cache.put(pattern, regExp);\n      }\n      return regExp;\n    }\n  }]);\n  return RegExpCache;\n}();\nexport { RegExpCache as default };", "map": {"version": 3, "names": ["_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "Object", "defineProperty", "key", "_createClass", "protoProps", "staticProps", "prototype", "L<PERSON><PERSON><PERSON>", "RegExpCache", "size", "cache", "value", "getPatternForRegExp", "pattern", "regExp", "get", "RegExp", "put", "default"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/findNumbers/RegExpCache.js"], "sourcesContent": ["function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\n\nimport LRUCache from './LRUCache.js'; // A cache for frequently used country-specific regular expressions. Set to 32 to cover ~2-3\n// countries being used for the same doc with ~10 patterns for each country. Some pages will have\n// a lot more countries in use, but typically fewer numbers for each so expanding the cache for\n// that use-case won't have a lot of benefit.\n\nvar RegExpCache = /*#__PURE__*/function () {\n  function RegExpCache(size) {\n    _classCallCheck(this, RegExpCache);\n\n    this.cache = new LRUCache(size);\n  }\n\n  _createClass(RegExpCache, [{\n    key: \"getPatternForRegExp\",\n    value: function getPatternForRegExp(pattern) {\n      var regExp = this.cache.get(pattern);\n\n      if (!regExp) {\n        regExp = new RegExp('^' + pattern);\n        this.cache.put(pattern, regExp);\n      }\n\n      return regExp;\n    }\n  }]);\n\n  return RegExpCache;\n}();\n\nexport { RegExpCache as default };\n"], "mappings": "AAAA,SAASA,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AAExJ,SAASC,iBAAiBA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IAAE,IAAIE,UAAU,GAAGH,KAAK,CAACC,CAAC,CAAC;IAAEE,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;IAAED,UAAU,CAACE,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;IAAEC,MAAM,CAACC,cAAc,CAACT,MAAM,EAAEI,UAAU,CAACM,GAAG,EAAEN,UAAU,CAAC;EAAE;AAAE;AAE5T,SAASO,YAAYA,CAACd,WAAW,EAAEe,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEb,iBAAiB,CAACF,WAAW,CAACiB,SAAS,EAAEF,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAEd,iBAAiB,CAACF,WAAW,EAAEgB,WAAW,CAAC;EAAEL,MAAM,CAACC,cAAc,CAACZ,WAAW,EAAE,WAAW,EAAE;IAAEU,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAOV,WAAW;AAAE;AAE5R,OAAOkB,QAAQ,MAAM,eAAe,CAAC,CAAC;AACtC;AACA;AACA;;AAEA,IAAIC,WAAW,GAAG,aAAa,YAAY;EACzC,SAASA,WAAWA,CAACC,IAAI,EAAE;IACzBtB,eAAe,CAAC,IAAI,EAAEqB,WAAW,CAAC;IAElC,IAAI,CAACE,KAAK,GAAG,IAAIH,QAAQ,CAACE,IAAI,CAAC;EACjC;EAEAN,YAAY,CAACK,WAAW,EAAE,CAAC;IACzBN,GAAG,EAAE,qBAAqB;IAC1BS,KAAK,EAAE,SAASC,mBAAmBA,CAACC,OAAO,EAAE;MAC3C,IAAIC,MAAM,GAAG,IAAI,CAACJ,KAAK,CAACK,GAAG,CAACF,OAAO,CAAC;MAEpC,IAAI,CAACC,MAAM,EAAE;QACXA,MAAM,GAAG,IAAIE,MAAM,CAAC,GAAG,GAAGH,OAAO,CAAC;QAClC,IAAI,CAACH,KAAK,CAACO,GAAG,CAACJ,OAAO,EAAEC,MAAM,CAAC;MACjC;MAEA,OAAOA,MAAM;IACf;EACF,CAAC,CAAC,CAAC;EAEH,OAAON,WAAW;AACpB,CAAC,CAAC,CAAC;AAEH,SAASA,WAAW,IAAIU,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}