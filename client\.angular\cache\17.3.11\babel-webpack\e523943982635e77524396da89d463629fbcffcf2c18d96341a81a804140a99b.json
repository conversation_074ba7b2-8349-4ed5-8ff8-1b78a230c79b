{"ast": null, "code": "function _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n  return arr2;\n}\nfunction _iterableToArrayLimit(arr, i) {\n  var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n  if (_i == null) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n  var _s, _e;\n  try {\n    for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n  return _arr;\n}\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nimport Metadata from './metadata.js';\nimport PhoneNumber from './PhoneNumber.js';\nimport AsYouTypeState from './AsYouTypeState.js';\nimport AsYouTypeFormatter, { DIGIT_PLACEHOLDER } from './AsYouTypeFormatter.js';\nimport AsYouTypeParser, { extractFormattedDigitsAndPlus } from './AsYouTypeParser.js';\nimport getCountryByCallingCode from './helpers/getCountryByCallingCode.js';\nimport getCountryByNationalNumber from './helpers/getCountryByNationalNumber.js';\nimport isObject from './helpers/isObject.js';\nvar USE_NON_GEOGRAPHIC_COUNTRY_CODE = false;\nvar AsYouType = /*#__PURE__*/function () {\n  /**\r\n   * @param {(string|object)?} [optionsOrDefaultCountry] - The default country used for parsing non-international phone numbers. Can also be an `options` object.\r\n   * @param {Object} metadata\r\n   */\n  function AsYouType(optionsOrDefaultCountry, metadata) {\n    _classCallCheck(this, AsYouType);\n    this.metadata = new Metadata(metadata);\n    var _this$getCountryAndCa = this.getCountryAndCallingCode(optionsOrDefaultCountry),\n      _this$getCountryAndCa2 = _slicedToArray(_this$getCountryAndCa, 2),\n      defaultCountry = _this$getCountryAndCa2[0],\n      defaultCallingCode = _this$getCountryAndCa2[1]; // `this.defaultCountry` and `this.defaultCallingCode` aren't required to be in sync.\n    // For example, `this.defaultCountry` could be `\"AR\"` and `this.defaultCallingCode` could be `undefined`.\n    // So `this.defaultCountry` and `this.defaultCallingCode` are totally independent.\n\n    this.defaultCountry = defaultCountry;\n    this.defaultCallingCode = defaultCallingCode;\n    this.reset();\n  }\n  _createClass(AsYouType, [{\n    key: \"getCountryAndCallingCode\",\n    value: function getCountryAndCallingCode(optionsOrDefaultCountry) {\n      // Set `defaultCountry` and `defaultCallingCode` options.\n      var defaultCountry;\n      var defaultCallingCode; // Turns out `null` also has type \"object\". Weird.\n\n      if (optionsOrDefaultCountry) {\n        if (isObject(optionsOrDefaultCountry)) {\n          defaultCountry = optionsOrDefaultCountry.defaultCountry;\n          defaultCallingCode = optionsOrDefaultCountry.defaultCallingCode;\n        } else {\n          defaultCountry = optionsOrDefaultCountry;\n        }\n      }\n      if (defaultCountry && !this.metadata.hasCountry(defaultCountry)) {\n        defaultCountry = undefined;\n      }\n      if (defaultCallingCode) {\n        /* istanbul ignore if */\n        if (USE_NON_GEOGRAPHIC_COUNTRY_CODE) {\n          if (this.metadata.isNonGeographicCallingCode(defaultCallingCode)) {\n            defaultCountry = '001';\n          }\n        }\n      }\n      return [defaultCountry, defaultCallingCode];\n    }\n    /**\r\n     * Inputs \"next\" phone number characters.\r\n     * @param  {string} text\r\n     * @return {string} Formatted phone number characters that have been input so far.\r\n     */\n  }, {\n    key: \"input\",\n    value: function input(text) {\n      var _this$parser$input = this.parser.input(text, this.state),\n        digits = _this$parser$input.digits,\n        justLeadingPlus = _this$parser$input.justLeadingPlus;\n      if (justLeadingPlus) {\n        this.formattedOutput = '+';\n      } else if (digits) {\n        this.determineTheCountryIfNeeded(); // Match the available formats by the currently available leading digits.\n\n        if (this.state.nationalSignificantNumber) {\n          this.formatter.narrowDownMatchingFormats(this.state);\n        }\n        var formattedNationalNumber;\n        if (this.metadata.hasSelectedNumberingPlan()) {\n          formattedNationalNumber = this.formatter.format(digits, this.state);\n        }\n        if (formattedNationalNumber === undefined) {\n          // See if another national (significant) number could be re-extracted.\n          if (this.parser.reExtractNationalSignificantNumber(this.state)) {\n            this.determineTheCountryIfNeeded(); // If it could, then re-try formatting the new national (significant) number.\n\n            var nationalDigits = this.state.getNationalDigits();\n            if (nationalDigits) {\n              formattedNationalNumber = this.formatter.format(nationalDigits, this.state);\n            }\n          }\n        }\n        this.formattedOutput = formattedNationalNumber ? this.getFullNumber(formattedNationalNumber) : this.getNonFormattedNumber();\n      }\n      return this.formattedOutput;\n    }\n  }, {\n    key: \"reset\",\n    value: function reset() {\n      var _this = this;\n      this.state = new AsYouTypeState({\n        onCountryChange: function onCountryChange(country) {\n          // Before version `1.6.0`, the official `AsYouType` formatter API\n          // included the `.country` property of an `AsYouType` instance.\n          // Since that property (along with the others) have been moved to\n          // `this.state`, `this.country` property is emulated for compatibility\n          // with the old versions.\n          _this.country = country;\n        },\n        onCallingCodeChange: function onCallingCodeChange(callingCode, country) {\n          _this.metadata.selectNumberingPlan(country, callingCode);\n          _this.formatter.reset(_this.metadata.numberingPlan, _this.state);\n          _this.parser.reset(_this.metadata.numberingPlan);\n        }\n      });\n      this.formatter = new AsYouTypeFormatter({\n        state: this.state,\n        metadata: this.metadata\n      });\n      this.parser = new AsYouTypeParser({\n        defaultCountry: this.defaultCountry,\n        defaultCallingCode: this.defaultCallingCode,\n        metadata: this.metadata,\n        state: this.state,\n        onNationalSignificantNumberChange: function onNationalSignificantNumberChange() {\n          _this.determineTheCountryIfNeeded();\n          _this.formatter.reset(_this.metadata.numberingPlan, _this.state);\n        }\n      });\n      this.state.reset({\n        country: this.defaultCountry,\n        callingCode: this.defaultCallingCode\n      });\n      this.formattedOutput = '';\n      return this;\n    }\n    /**\r\n     * Returns `true` if the phone number is being input in international format.\r\n     * In other words, returns `true` if and only if the parsed phone number starts with a `\"+\"`.\r\n     * @return {boolean}\r\n     */\n  }, {\n    key: \"isInternational\",\n    value: function isInternational() {\n      return this.state.international;\n    }\n    /**\r\n     * Returns the \"calling code\" part of the phone number when it's being input\r\n     * in an international format.\r\n     * If no valid calling code has been entered so far, returns `undefined`.\r\n     * @return {string} [callingCode]\r\n     */\n  }, {\n    key: \"getCallingCode\",\n    value: function getCallingCode() {\n      // If the number is being input in national format and some \"default calling code\"\n      // has been passed to `AsYouType` constructor, then `this.state.callingCode`\n      // is equal to that \"default calling code\".\n      //\n      // If the number is being input in national format and no \"default calling code\"\n      // has been passed to `AsYouType` constructor, then returns `undefined`,\n      // even if a \"default country\" has been passed to `AsYouType` constructor.\n      //\n      if (this.isInternational()) {\n        return this.state.callingCode;\n      }\n    } // A legacy alias.\n  }, {\n    key: \"getCountryCallingCode\",\n    value: function getCountryCallingCode() {\n      return this.getCallingCode();\n    }\n    /**\r\n     * Returns a two-letter country code of the phone number.\r\n     * Returns `undefined` for \"non-geographic\" phone numbering plans.\r\n     * Returns `undefined` if no phone number has been input yet.\r\n     * @return {string} [country]\r\n     */\n  }, {\n    key: \"getCountry\",\n    value: function getCountry() {\n      var digits = this.state.digits; // Return `undefined` if no digits have been input yet.\n\n      if (digits) {\n        return this._getCountry();\n      }\n    }\n    /**\r\n     * Returns a two-letter country code of the phone number.\r\n     * Returns `undefined` for \"non-geographic\" phone numbering plans.\r\n     * @return {string} [country]\r\n     */\n  }, {\n    key: \"_getCountry\",\n    value: function _getCountry() {\n      var country = this.state.country;\n      /* istanbul ignore if */\n\n      if (USE_NON_GEOGRAPHIC_COUNTRY_CODE) {\n        // `AsYouType.getCountry()` returns `undefined`\n        // for \"non-geographic\" phone numbering plans.\n        if (country === '001') {\n          return;\n        }\n      }\n      return country;\n    }\n  }, {\n    key: \"determineTheCountryIfNeeded\",\n    value: function determineTheCountryIfNeeded() {\n      // Suppose a user enters a phone number in international format,\n      // and there're several countries corresponding to that country calling code,\n      // and a country has been derived from the number, and then\n      // a user enters one more digit and the number is no longer\n      // valid for the derived country, so the country should be re-derived\n      // on every new digit in those cases.\n      //\n      // If the phone number is being input in national format,\n      // then it could be a case when `defaultCountry` wasn't specified\n      // when creating `AsYouType` instance, and just `defaultCallingCode` was specified,\n      // and that \"calling code\" could correspond to a \"non-geographic entity\",\n      // or there could be several countries corresponding to that country calling code.\n      // In those cases, `this.country` is `undefined` and should be derived\n      // from the number. Again, if country calling code is ambiguous, then\n      // `this.country` should be re-derived with each new digit.\n      //\n      if (!this.state.country || this.isCountryCallingCodeAmbiguous()) {\n        this.determineTheCountry();\n      }\n    } // Prepends `+CountryCode ` in case of an international phone number\n  }, {\n    key: \"getFullNumber\",\n    value: function getFullNumber(formattedNationalNumber) {\n      var _this2 = this;\n      if (this.isInternational()) {\n        var prefix = function prefix(text) {\n          return _this2.formatter.getInternationalPrefixBeforeCountryCallingCode(_this2.state, {\n            spacing: text ? true : false\n          }) + text;\n        };\n        var callingCode = this.state.callingCode;\n        if (!callingCode) {\n          return prefix(\"\".concat(this.state.getDigitsWithoutInternationalPrefix()));\n        }\n        if (!formattedNationalNumber) {\n          return prefix(callingCode);\n        }\n        return prefix(\"\".concat(callingCode, \" \").concat(formattedNationalNumber));\n      }\n      return formattedNationalNumber;\n    }\n  }, {\n    key: \"getNonFormattedNationalNumberWithPrefix\",\n    value: function getNonFormattedNationalNumberWithPrefix() {\n      var _this$state = this.state,\n        nationalSignificantNumber = _this$state.nationalSignificantNumber,\n        complexPrefixBeforeNationalSignificantNumber = _this$state.complexPrefixBeforeNationalSignificantNumber,\n        nationalPrefix = _this$state.nationalPrefix;\n      var number = nationalSignificantNumber;\n      var prefix = complexPrefixBeforeNationalSignificantNumber || nationalPrefix;\n      if (prefix) {\n        number = prefix + number;\n      }\n      return number;\n    }\n  }, {\n    key: \"getNonFormattedNumber\",\n    value: function getNonFormattedNumber() {\n      var nationalSignificantNumberMatchesInput = this.state.nationalSignificantNumberMatchesInput;\n      return this.getFullNumber(nationalSignificantNumberMatchesInput ? this.getNonFormattedNationalNumberWithPrefix() : this.state.getNationalDigits());\n    }\n  }, {\n    key: \"getNonFormattedTemplate\",\n    value: function getNonFormattedTemplate() {\n      var number = this.getNonFormattedNumber();\n      if (number) {\n        return number.replace(/[\\+\\d]/g, DIGIT_PLACEHOLDER);\n      }\n    }\n  }, {\n    key: \"isCountryCallingCodeAmbiguous\",\n    value: function isCountryCallingCodeAmbiguous() {\n      var callingCode = this.state.callingCode;\n      var countryCodes = this.metadata.getCountryCodesForCallingCode(callingCode);\n      return countryCodes && countryCodes.length > 1;\n    } // Determines the country of the phone number\n    // entered so far based on the country phone code\n    // and the national phone number.\n  }, {\n    key: \"determineTheCountry\",\n    value: function determineTheCountry() {\n      this.state.setCountry(getCountryByCallingCode(this.isInternational() ? this.state.callingCode : this.defaultCallingCode, {\n        nationalNumber: this.state.nationalSignificantNumber,\n        defaultCountry: this.defaultCountry,\n        metadata: this.metadata\n      }));\n    }\n    /**\r\n     * Returns a E.164 phone number value for the user's input.\r\n     *\r\n     * For example, for country `\"US\"` and input `\"(*************\"`\r\n     * it will return `\"+12223334444\"`.\r\n     *\r\n     * For international phone number input, it will also auto-correct\r\n     * some minor errors such as using a national prefix when writing\r\n     * an international phone number. For example, if the user inputs\r\n     * `\"+44 0 7400 000000\"` then it will return an auto-corrected\r\n     * `\"+447400000000\"` phone number value.\r\n     *\r\n     * Will return `undefined` if no digits have been input,\r\n     * or when inputting a phone number in national format and no\r\n     * default country or default \"country calling code\" have been set.\r\n     *\r\n     * @return {string} [value]\r\n     */\n  }, {\n    key: \"getNumberValue\",\n    value: function getNumberValue() {\n      var _this$state2 = this.state,\n        digits = _this$state2.digits,\n        callingCode = _this$state2.callingCode,\n        country = _this$state2.country,\n        nationalSignificantNumber = _this$state2.nationalSignificantNumber; // Will return `undefined` if no digits have been input.\n\n      if (!digits) {\n        return;\n      }\n      if (this.isInternational()) {\n        if (callingCode) {\n          return '+' + callingCode + nationalSignificantNumber;\n        } else {\n          return '+' + digits;\n        }\n      } else {\n        if (country || callingCode) {\n          var callingCode_ = country ? this.metadata.countryCallingCode() : callingCode;\n          return '+' + callingCode_ + nationalSignificantNumber;\n        }\n      }\n    }\n    /**\r\n     * Returns an instance of `PhoneNumber` class.\r\n     * Will return `undefined` if no national (significant) number\r\n     * digits have been entered so far, or if no `defaultCountry` has been\r\n     * set and the user enters a phone number not in international format.\r\n     */\n  }, {\n    key: \"getNumber\",\n    value: function getNumber() {\n      var _this$state3 = this.state,\n        nationalSignificantNumber = _this$state3.nationalSignificantNumber,\n        carrierCode = _this$state3.carrierCode,\n        callingCode = _this$state3.callingCode; // `this._getCountry()` is basically same as `this.state.country`\n      // with the only change that it return `undefined` in case of a\n      // \"non-geographic\" numbering plan instead of `\"001\"` \"internal use\" value.\n\n      var country = this._getCountry();\n      if (!nationalSignificantNumber) {\n        return;\n      } // `state.country` and `state.callingCode` aren't required to be in sync.\n      // For example, `country` could be `\"AR\"` and `callingCode` could be `undefined`.\n      // So `country` and `callingCode` are totally independent.\n\n      if (!country && !callingCode) {\n        return;\n      } // By default, if `defaultCountry` parameter was passed when\n      // creating `AsYouType` instance, `state.country` is gonna be\n      // that `defaultCountry`, which doesn't entirely conform with\n      // `parsePhoneNumber()`'s behavior where it attempts to determine\n      // the country more precisely in cases when multiple countries\n      // could correspond to the same `countryCallingCode`.\n      // https://gitlab.com/catamphetamine/libphonenumber-js/-/issues/103#note_1417192969\n      //\n      // Because `AsYouType.getNumber()` method is supposed to be a 1:1\n      // equivalent for `parsePhoneNumber(AsYouType.getNumberValue())`,\n      // then it should also behave accordingly in cases of `country` ambiguity.\n      // That's how users of this library would expect it to behave anyway.\n      //\n\n      if (country) {\n        if (country === this.defaultCountry) {\n          // `state.country` and `state.callingCode` aren't required to be in sync.\n          // For example, `state.country` could be `\"AR\"` and `state.callingCode` could be `undefined`.\n          // So `state.country` and `state.callingCode` are totally independent.\n          var metadata = new Metadata(this.metadata.metadata);\n          metadata.selectNumberingPlan(country);\n          var _callingCode = metadata.numberingPlan.callingCode();\n          var ambiguousCountries = this.metadata.getCountryCodesForCallingCode(_callingCode);\n          if (ambiguousCountries.length > 1) {\n            var exactCountry = getCountryByNationalNumber(nationalSignificantNumber, {\n              countries: ambiguousCountries,\n              defaultCountry: this.defaultCountry,\n              metadata: this.metadata.metadata\n            });\n            if (exactCountry) {\n              country = exactCountry;\n            }\n          }\n        }\n      }\n      var phoneNumber = new PhoneNumber(country || callingCode, nationalSignificantNumber, this.metadata.metadata);\n      if (carrierCode) {\n        phoneNumber.carrierCode = carrierCode;\n      } // Phone number extensions are not supported by \"As You Type\" formatter.\n\n      return phoneNumber;\n    }\n    /**\r\n     * Returns `true` if the phone number is \"possible\".\r\n     * Is just a shortcut for `PhoneNumber.isPossible()`.\r\n     * @return {boolean}\r\n     */\n  }, {\n    key: \"isPossible\",\n    value: function isPossible() {\n      var phoneNumber = this.getNumber();\n      if (!phoneNumber) {\n        return false;\n      }\n      return phoneNumber.isPossible();\n    }\n    /**\r\n     * Returns `true` if the phone number is \"valid\".\r\n     * Is just a shortcut for `PhoneNumber.isValid()`.\r\n     * @return {boolean}\r\n     */\n  }, {\n    key: \"isValid\",\n    value: function isValid() {\n      var phoneNumber = this.getNumber();\n      if (!phoneNumber) {\n        return false;\n      }\n      return phoneNumber.isValid();\n    }\n    /**\r\n     * @deprecated\r\n     * This method is used in `react-phone-number-input/source/input-control.js`\r\n     * in versions before `3.0.16`.\r\n     */\n  }, {\n    key: \"getNationalNumber\",\n    value: function getNationalNumber() {\n      return this.state.nationalSignificantNumber;\n    }\n    /**\r\n     * Returns the phone number characters entered by the user.\r\n     * @return {string}\r\n     */\n  }, {\n    key: \"getChars\",\n    value: function getChars() {\n      return (this.state.international ? '+' : '') + this.state.digits;\n    }\n    /**\r\n     * Returns the template for the formatted phone number.\r\n     * @return {string}\r\n     */\n  }, {\n    key: \"getTemplate\",\n    value: function getTemplate() {\n      return this.formatter.getTemplate(this.state) || this.getNonFormattedTemplate() || '';\n    }\n  }]);\n  return AsYouType;\n}();\nexport { AsYouType as default };", "map": {"version": 3, "names": ["_slicedToArray", "arr", "i", "_arrayWithHoles", "_iterableToArrayLimit", "_unsupportedIterableToArray", "_nonIterableRest", "TypeError", "o", "minLen", "_arrayLikeToArray", "n", "Object", "prototype", "toString", "call", "slice", "constructor", "name", "Array", "from", "test", "len", "length", "arr2", "_i", "Symbol", "iterator", "_arr", "_n", "_d", "_s", "_e", "next", "done", "push", "value", "err", "isArray", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "_defineProperties", "target", "props", "descriptor", "enumerable", "configurable", "writable", "defineProperty", "key", "_createClass", "protoProps", "staticProps", "<PERSON><PERSON><PERSON>", "PhoneNumber", "AsYouTypeState", "AsYouTypeFormatter", "DIGIT_PLACEHOLDER", "AsYouType<PERSON><PERSON><PERSON>", "extractFormattedDigitsAndPlus", "getCountryByCallingCode", "getCountryByNationalNumber", "isObject", "USE_NON_GEOGRAPHIC_COUNTRY_CODE", "AsYouType", "optionsOrDefaultCountry", "metadata", "_this$getCountryAndCa", "getCountryAndCallingCode", "_this$getCountryAndCa2", "defaultCountry", "defaultCallingCode", "reset", "hasCountry", "undefined", "isNonGeographicCallingCode", "input", "text", "_this$parser$input", "parser", "state", "digits", "justLeadingPlus", "formattedOutput", "determineTheCountryIfNeeded", "nationalSignificantNumber", "formatter", "narrowDownMatchingFormats", "formattedNationalNumber", "hasSelectedNumberingPlan", "format", "reExtractNationalSignificantNumber", "nationalDigits", "getNationalDigits", "getFullNumber", "getNonFormattedNumber", "_this", "onCountryChange", "country", "onCallingCodeChange", "callingCode", "selectNumberingPlan", "numberingPlan", "onNationalSignificantNumberChange", "isInternational", "international", "getCallingCode", "getCountryCallingCode", "getCountry", "_getCountry", "isCountryCallingCodeAmbiguous", "determineTheCountry", "_this2", "prefix", "getInternationalPrefixBeforeCountryCallingCode", "spacing", "concat", "getDigitsWithoutInternationalPrefix", "getNonFormattedNationalNumberWithPrefix", "_this$state", "complexPrefixBeforeNationalSignificantNumber", "nationalPrefix", "number", "nationalSignificantNumberMatchesInput", "getNonFormattedTemplate", "replace", "countryCodes", "getCountryCodesForCallingCode", "setCountry", "nationalNumber", "getNumberValue", "_this$state2", "callingCode_", "countryCallingCode", "getNumber", "_this$state3", "carrierCode", "_callingCode", "ambiguousCountries", "exactCountry", "countries", "phoneNumber", "isPossible", "<PERSON><PERSON><PERSON><PERSON>", "getNationalNumber", "getChars", "getTemplate", "default"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/AsYouType.js"], "sourcesContent": ["function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\n\nimport Metadata from './metadata.js';\nimport PhoneNumber from './PhoneNumber.js';\nimport AsYouTypeState from './AsYouTypeState.js';\nimport AsYouTypeFormatter, { DIGIT_PLACEHOLDER } from './AsYouTypeFormatter.js';\nimport AsYouTypeParser, { extractFormattedDigitsAndPlus } from './AsYouTypeParser.js';\nimport getCountryByCallingCode from './helpers/getCountryByCallingCode.js';\nimport getCountryByNationalNumber from './helpers/getCountryByNationalNumber.js';\nimport isObject from './helpers/isObject.js';\nvar USE_NON_GEOGRAPHIC_COUNTRY_CODE = false;\n\nvar AsYouType = /*#__PURE__*/function () {\n  /**\r\n   * @param {(string|object)?} [optionsOrDefaultCountry] - The default country used for parsing non-international phone numbers. Can also be an `options` object.\r\n   * @param {Object} metadata\r\n   */\n  function AsYouType(optionsOrDefaultCountry, metadata) {\n    _classCallCheck(this, AsYouType);\n\n    this.metadata = new Metadata(metadata);\n\n    var _this$getCountryAndCa = this.getCountryAndCallingCode(optionsOrDefaultCountry),\n        _this$getCountryAndCa2 = _slicedToArray(_this$getCountryAndCa, 2),\n        defaultCountry = _this$getCountryAndCa2[0],\n        defaultCallingCode = _this$getCountryAndCa2[1]; // `this.defaultCountry` and `this.defaultCallingCode` aren't required to be in sync.\n    // For example, `this.defaultCountry` could be `\"AR\"` and `this.defaultCallingCode` could be `undefined`.\n    // So `this.defaultCountry` and `this.defaultCallingCode` are totally independent.\n\n\n    this.defaultCountry = defaultCountry;\n    this.defaultCallingCode = defaultCallingCode;\n    this.reset();\n  }\n\n  _createClass(AsYouType, [{\n    key: \"getCountryAndCallingCode\",\n    value: function getCountryAndCallingCode(optionsOrDefaultCountry) {\n      // Set `defaultCountry` and `defaultCallingCode` options.\n      var defaultCountry;\n      var defaultCallingCode; // Turns out `null` also has type \"object\". Weird.\n\n      if (optionsOrDefaultCountry) {\n        if (isObject(optionsOrDefaultCountry)) {\n          defaultCountry = optionsOrDefaultCountry.defaultCountry;\n          defaultCallingCode = optionsOrDefaultCountry.defaultCallingCode;\n        } else {\n          defaultCountry = optionsOrDefaultCountry;\n        }\n      }\n\n      if (defaultCountry && !this.metadata.hasCountry(defaultCountry)) {\n        defaultCountry = undefined;\n      }\n\n      if (defaultCallingCode) {\n        /* istanbul ignore if */\n        if (USE_NON_GEOGRAPHIC_COUNTRY_CODE) {\n          if (this.metadata.isNonGeographicCallingCode(defaultCallingCode)) {\n            defaultCountry = '001';\n          }\n        }\n      }\n\n      return [defaultCountry, defaultCallingCode];\n    }\n    /**\r\n     * Inputs \"next\" phone number characters.\r\n     * @param  {string} text\r\n     * @return {string} Formatted phone number characters that have been input so far.\r\n     */\n\n  }, {\n    key: \"input\",\n    value: function input(text) {\n      var _this$parser$input = this.parser.input(text, this.state),\n          digits = _this$parser$input.digits,\n          justLeadingPlus = _this$parser$input.justLeadingPlus;\n\n      if (justLeadingPlus) {\n        this.formattedOutput = '+';\n      } else if (digits) {\n        this.determineTheCountryIfNeeded(); // Match the available formats by the currently available leading digits.\n\n        if (this.state.nationalSignificantNumber) {\n          this.formatter.narrowDownMatchingFormats(this.state);\n        }\n\n        var formattedNationalNumber;\n\n        if (this.metadata.hasSelectedNumberingPlan()) {\n          formattedNationalNumber = this.formatter.format(digits, this.state);\n        }\n\n        if (formattedNationalNumber === undefined) {\n          // See if another national (significant) number could be re-extracted.\n          if (this.parser.reExtractNationalSignificantNumber(this.state)) {\n            this.determineTheCountryIfNeeded(); // If it could, then re-try formatting the new national (significant) number.\n\n            var nationalDigits = this.state.getNationalDigits();\n\n            if (nationalDigits) {\n              formattedNationalNumber = this.formatter.format(nationalDigits, this.state);\n            }\n          }\n        }\n\n        this.formattedOutput = formattedNationalNumber ? this.getFullNumber(formattedNationalNumber) : this.getNonFormattedNumber();\n      }\n\n      return this.formattedOutput;\n    }\n  }, {\n    key: \"reset\",\n    value: function reset() {\n      var _this = this;\n\n      this.state = new AsYouTypeState({\n        onCountryChange: function onCountryChange(country) {\n          // Before version `1.6.0`, the official `AsYouType` formatter API\n          // included the `.country` property of an `AsYouType` instance.\n          // Since that property (along with the others) have been moved to\n          // `this.state`, `this.country` property is emulated for compatibility\n          // with the old versions.\n          _this.country = country;\n        },\n        onCallingCodeChange: function onCallingCodeChange(callingCode, country) {\n          _this.metadata.selectNumberingPlan(country, callingCode);\n\n          _this.formatter.reset(_this.metadata.numberingPlan, _this.state);\n\n          _this.parser.reset(_this.metadata.numberingPlan);\n        }\n      });\n      this.formatter = new AsYouTypeFormatter({\n        state: this.state,\n        metadata: this.metadata\n      });\n      this.parser = new AsYouTypeParser({\n        defaultCountry: this.defaultCountry,\n        defaultCallingCode: this.defaultCallingCode,\n        metadata: this.metadata,\n        state: this.state,\n        onNationalSignificantNumberChange: function onNationalSignificantNumberChange() {\n          _this.determineTheCountryIfNeeded();\n\n          _this.formatter.reset(_this.metadata.numberingPlan, _this.state);\n        }\n      });\n      this.state.reset({\n        country: this.defaultCountry,\n        callingCode: this.defaultCallingCode\n      });\n      this.formattedOutput = '';\n      return this;\n    }\n    /**\r\n     * Returns `true` if the phone number is being input in international format.\r\n     * In other words, returns `true` if and only if the parsed phone number starts with a `\"+\"`.\r\n     * @return {boolean}\r\n     */\n\n  }, {\n    key: \"isInternational\",\n    value: function isInternational() {\n      return this.state.international;\n    }\n    /**\r\n     * Returns the \"calling code\" part of the phone number when it's being input\r\n     * in an international format.\r\n     * If no valid calling code has been entered so far, returns `undefined`.\r\n     * @return {string} [callingCode]\r\n     */\n\n  }, {\n    key: \"getCallingCode\",\n    value: function getCallingCode() {\n      // If the number is being input in national format and some \"default calling code\"\n      // has been passed to `AsYouType` constructor, then `this.state.callingCode`\n      // is equal to that \"default calling code\".\n      //\n      // If the number is being input in national format and no \"default calling code\"\n      // has been passed to `AsYouType` constructor, then returns `undefined`,\n      // even if a \"default country\" has been passed to `AsYouType` constructor.\n      //\n      if (this.isInternational()) {\n        return this.state.callingCode;\n      }\n    } // A legacy alias.\n\n  }, {\n    key: \"getCountryCallingCode\",\n    value: function getCountryCallingCode() {\n      return this.getCallingCode();\n    }\n    /**\r\n     * Returns a two-letter country code of the phone number.\r\n     * Returns `undefined` for \"non-geographic\" phone numbering plans.\r\n     * Returns `undefined` if no phone number has been input yet.\r\n     * @return {string} [country]\r\n     */\n\n  }, {\n    key: \"getCountry\",\n    value: function getCountry() {\n      var digits = this.state.digits; // Return `undefined` if no digits have been input yet.\n\n      if (digits) {\n        return this._getCountry();\n      }\n    }\n    /**\r\n     * Returns a two-letter country code of the phone number.\r\n     * Returns `undefined` for \"non-geographic\" phone numbering plans.\r\n     * @return {string} [country]\r\n     */\n\n  }, {\n    key: \"_getCountry\",\n    value: function _getCountry() {\n      var country = this.state.country;\n      /* istanbul ignore if */\n\n      if (USE_NON_GEOGRAPHIC_COUNTRY_CODE) {\n        // `AsYouType.getCountry()` returns `undefined`\n        // for \"non-geographic\" phone numbering plans.\n        if (country === '001') {\n          return;\n        }\n      }\n\n      return country;\n    }\n  }, {\n    key: \"determineTheCountryIfNeeded\",\n    value: function determineTheCountryIfNeeded() {\n      // Suppose a user enters a phone number in international format,\n      // and there're several countries corresponding to that country calling code,\n      // and a country has been derived from the number, and then\n      // a user enters one more digit and the number is no longer\n      // valid for the derived country, so the country should be re-derived\n      // on every new digit in those cases.\n      //\n      // If the phone number is being input in national format,\n      // then it could be a case when `defaultCountry` wasn't specified\n      // when creating `AsYouType` instance, and just `defaultCallingCode` was specified,\n      // and that \"calling code\" could correspond to a \"non-geographic entity\",\n      // or there could be several countries corresponding to that country calling code.\n      // In those cases, `this.country` is `undefined` and should be derived\n      // from the number. Again, if country calling code is ambiguous, then\n      // `this.country` should be re-derived with each new digit.\n      //\n      if (!this.state.country || this.isCountryCallingCodeAmbiguous()) {\n        this.determineTheCountry();\n      }\n    } // Prepends `+CountryCode ` in case of an international phone number\n\n  }, {\n    key: \"getFullNumber\",\n    value: function getFullNumber(formattedNationalNumber) {\n      var _this2 = this;\n\n      if (this.isInternational()) {\n        var prefix = function prefix(text) {\n          return _this2.formatter.getInternationalPrefixBeforeCountryCallingCode(_this2.state, {\n            spacing: text ? true : false\n          }) + text;\n        };\n\n        var callingCode = this.state.callingCode;\n\n        if (!callingCode) {\n          return prefix(\"\".concat(this.state.getDigitsWithoutInternationalPrefix()));\n        }\n\n        if (!formattedNationalNumber) {\n          return prefix(callingCode);\n        }\n\n        return prefix(\"\".concat(callingCode, \" \").concat(formattedNationalNumber));\n      }\n\n      return formattedNationalNumber;\n    }\n  }, {\n    key: \"getNonFormattedNationalNumberWithPrefix\",\n    value: function getNonFormattedNationalNumberWithPrefix() {\n      var _this$state = this.state,\n          nationalSignificantNumber = _this$state.nationalSignificantNumber,\n          complexPrefixBeforeNationalSignificantNumber = _this$state.complexPrefixBeforeNationalSignificantNumber,\n          nationalPrefix = _this$state.nationalPrefix;\n      var number = nationalSignificantNumber;\n      var prefix = complexPrefixBeforeNationalSignificantNumber || nationalPrefix;\n\n      if (prefix) {\n        number = prefix + number;\n      }\n\n      return number;\n    }\n  }, {\n    key: \"getNonFormattedNumber\",\n    value: function getNonFormattedNumber() {\n      var nationalSignificantNumberMatchesInput = this.state.nationalSignificantNumberMatchesInput;\n      return this.getFullNumber(nationalSignificantNumberMatchesInput ? this.getNonFormattedNationalNumberWithPrefix() : this.state.getNationalDigits());\n    }\n  }, {\n    key: \"getNonFormattedTemplate\",\n    value: function getNonFormattedTemplate() {\n      var number = this.getNonFormattedNumber();\n\n      if (number) {\n        return number.replace(/[\\+\\d]/g, DIGIT_PLACEHOLDER);\n      }\n    }\n  }, {\n    key: \"isCountryCallingCodeAmbiguous\",\n    value: function isCountryCallingCodeAmbiguous() {\n      var callingCode = this.state.callingCode;\n      var countryCodes = this.metadata.getCountryCodesForCallingCode(callingCode);\n      return countryCodes && countryCodes.length > 1;\n    } // Determines the country of the phone number\n    // entered so far based on the country phone code\n    // and the national phone number.\n\n  }, {\n    key: \"determineTheCountry\",\n    value: function determineTheCountry() {\n      this.state.setCountry(getCountryByCallingCode(this.isInternational() ? this.state.callingCode : this.defaultCallingCode, {\n        nationalNumber: this.state.nationalSignificantNumber,\n        defaultCountry: this.defaultCountry,\n        metadata: this.metadata\n      }));\n    }\n    /**\r\n     * Returns a E.164 phone number value for the user's input.\r\n     *\r\n     * For example, for country `\"US\"` and input `\"(*************\"`\r\n     * it will return `\"+12223334444\"`.\r\n     *\r\n     * For international phone number input, it will also auto-correct\r\n     * some minor errors such as using a national prefix when writing\r\n     * an international phone number. For example, if the user inputs\r\n     * `\"+44 0 7400 000000\"` then it will return an auto-corrected\r\n     * `\"+447400000000\"` phone number value.\r\n     *\r\n     * Will return `undefined` if no digits have been input,\r\n     * or when inputting a phone number in national format and no\r\n     * default country or default \"country calling code\" have been set.\r\n     *\r\n     * @return {string} [value]\r\n     */\n\n  }, {\n    key: \"getNumberValue\",\n    value: function getNumberValue() {\n      var _this$state2 = this.state,\n          digits = _this$state2.digits,\n          callingCode = _this$state2.callingCode,\n          country = _this$state2.country,\n          nationalSignificantNumber = _this$state2.nationalSignificantNumber; // Will return `undefined` if no digits have been input.\n\n      if (!digits) {\n        return;\n      }\n\n      if (this.isInternational()) {\n        if (callingCode) {\n          return '+' + callingCode + nationalSignificantNumber;\n        } else {\n          return '+' + digits;\n        }\n      } else {\n        if (country || callingCode) {\n          var callingCode_ = country ? this.metadata.countryCallingCode() : callingCode;\n          return '+' + callingCode_ + nationalSignificantNumber;\n        }\n      }\n    }\n    /**\r\n     * Returns an instance of `PhoneNumber` class.\r\n     * Will return `undefined` if no national (significant) number\r\n     * digits have been entered so far, or if no `defaultCountry` has been\r\n     * set and the user enters a phone number not in international format.\r\n     */\n\n  }, {\n    key: \"getNumber\",\n    value: function getNumber() {\n      var _this$state3 = this.state,\n          nationalSignificantNumber = _this$state3.nationalSignificantNumber,\n          carrierCode = _this$state3.carrierCode,\n          callingCode = _this$state3.callingCode; // `this._getCountry()` is basically same as `this.state.country`\n      // with the only change that it return `undefined` in case of a\n      // \"non-geographic\" numbering plan instead of `\"001\"` \"internal use\" value.\n\n      var country = this._getCountry();\n\n      if (!nationalSignificantNumber) {\n        return;\n      } // `state.country` and `state.callingCode` aren't required to be in sync.\n      // For example, `country` could be `\"AR\"` and `callingCode` could be `undefined`.\n      // So `country` and `callingCode` are totally independent.\n\n\n      if (!country && !callingCode) {\n        return;\n      } // By default, if `defaultCountry` parameter was passed when\n      // creating `AsYouType` instance, `state.country` is gonna be\n      // that `defaultCountry`, which doesn't entirely conform with\n      // `parsePhoneNumber()`'s behavior where it attempts to determine\n      // the country more precisely in cases when multiple countries\n      // could correspond to the same `countryCallingCode`.\n      // https://gitlab.com/catamphetamine/libphonenumber-js/-/issues/103#note_1417192969\n      //\n      // Because `AsYouType.getNumber()` method is supposed to be a 1:1\n      // equivalent for `parsePhoneNumber(AsYouType.getNumberValue())`,\n      // then it should also behave accordingly in cases of `country` ambiguity.\n      // That's how users of this library would expect it to behave anyway.\n      //\n\n\n      if (country) {\n        if (country === this.defaultCountry) {\n          // `state.country` and `state.callingCode` aren't required to be in sync.\n          // For example, `state.country` could be `\"AR\"` and `state.callingCode` could be `undefined`.\n          // So `state.country` and `state.callingCode` are totally independent.\n          var metadata = new Metadata(this.metadata.metadata);\n          metadata.selectNumberingPlan(country);\n\n          var _callingCode = metadata.numberingPlan.callingCode();\n\n          var ambiguousCountries = this.metadata.getCountryCodesForCallingCode(_callingCode);\n\n          if (ambiguousCountries.length > 1) {\n            var exactCountry = getCountryByNationalNumber(nationalSignificantNumber, {\n              countries: ambiguousCountries,\n              defaultCountry: this.defaultCountry,\n              metadata: this.metadata.metadata\n            });\n\n            if (exactCountry) {\n              country = exactCountry;\n            }\n          }\n        }\n      }\n\n      var phoneNumber = new PhoneNumber(country || callingCode, nationalSignificantNumber, this.metadata.metadata);\n\n      if (carrierCode) {\n        phoneNumber.carrierCode = carrierCode;\n      } // Phone number extensions are not supported by \"As You Type\" formatter.\n\n\n      return phoneNumber;\n    }\n    /**\r\n     * Returns `true` if the phone number is \"possible\".\r\n     * Is just a shortcut for `PhoneNumber.isPossible()`.\r\n     * @return {boolean}\r\n     */\n\n  }, {\n    key: \"isPossible\",\n    value: function isPossible() {\n      var phoneNumber = this.getNumber();\n\n      if (!phoneNumber) {\n        return false;\n      }\n\n      return phoneNumber.isPossible();\n    }\n    /**\r\n     * Returns `true` if the phone number is \"valid\".\r\n     * Is just a shortcut for `PhoneNumber.isValid()`.\r\n     * @return {boolean}\r\n     */\n\n  }, {\n    key: \"isValid\",\n    value: function isValid() {\n      var phoneNumber = this.getNumber();\n\n      if (!phoneNumber) {\n        return false;\n      }\n\n      return phoneNumber.isValid();\n    }\n    /**\r\n     * @deprecated\r\n     * This method is used in `react-phone-number-input/source/input-control.js`\r\n     * in versions before `3.0.16`.\r\n     */\n\n  }, {\n    key: \"getNationalNumber\",\n    value: function getNationalNumber() {\n      return this.state.nationalSignificantNumber;\n    }\n    /**\r\n     * Returns the phone number characters entered by the user.\r\n     * @return {string}\r\n     */\n\n  }, {\n    key: \"getChars\",\n    value: function getChars() {\n      return (this.state.international ? '+' : '') + this.state.digits;\n    }\n    /**\r\n     * Returns the template for the formatted phone number.\r\n     * @return {string}\r\n     */\n\n  }, {\n    key: \"getTemplate\",\n    value: function getTemplate() {\n      return this.formatter.getTemplate(this.state) || this.getNonFormattedTemplate() || '';\n    }\n  }]);\n\n  return AsYouType;\n}();\n\nexport { AsYouType as default };\n"], "mappings": "AAAA,SAASA,cAAcA,CAACC,GAAG,EAAEC,CAAC,EAAE;EAAE,OAAOC,eAAe,CAACF,GAAG,CAAC,IAAIG,qBAAqB,CAACH,GAAG,EAAEC,CAAC,CAAC,IAAIG,2BAA2B,CAACJ,GAAG,EAAEC,CAAC,CAAC,IAAII,gBAAgB,CAAC,CAAC;AAAE;AAE7J,SAASA,gBAAgBA,CAAA,EAAG;EAAE,MAAM,IAAIC,SAAS,CAAC,2IAA2I,CAAC;AAAE;AAEhM,SAASF,2BAA2BA,CAACG,CAAC,EAAEC,MAAM,EAAE;EAAE,IAAI,CAACD,CAAC,EAAE;EAAQ,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOE,iBAAiB,CAACF,CAAC,EAAEC,MAAM,CAAC;EAAE,IAAIE,CAAC,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACP,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAE,IAAIL,CAAC,KAAK,QAAQ,IAAIH,CAAC,CAACS,WAAW,EAAEN,CAAC,GAAGH,CAAC,CAACS,WAAW,CAACC,IAAI;EAAE,IAAIP,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOQ,KAAK,CAACC,IAAI,CAACZ,CAAC,CAAC;EAAE,IAAIG,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACU,IAAI,CAACV,CAAC,CAAC,EAAE,OAAOD,iBAAiB,CAACF,CAAC,EAAEC,MAAM,CAAC;AAAE;AAE/Z,SAASC,iBAAiBA,CAACT,GAAG,EAAEqB,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGrB,GAAG,CAACsB,MAAM,EAAED,GAAG,GAAGrB,GAAG,CAACsB,MAAM;EAAE,KAAK,IAAIrB,CAAC,GAAG,CAAC,EAAEsB,IAAI,GAAG,IAAIL,KAAK,CAACG,GAAG,CAAC,EAAEpB,CAAC,GAAGoB,GAAG,EAAEpB,CAAC,EAAE,EAAE;IAAEsB,IAAI,CAACtB,CAAC,CAAC,GAAGD,GAAG,CAACC,CAAC,CAAC;EAAE;EAAE,OAAOsB,IAAI;AAAE;AAEtL,SAASpB,qBAAqBA,CAACH,GAAG,EAAEC,CAAC,EAAE;EAAE,IAAIuB,EAAE,GAAGxB,GAAG,IAAI,IAAI,GAAG,IAAI,GAAG,OAAOyB,MAAM,KAAK,WAAW,IAAIzB,GAAG,CAACyB,MAAM,CAACC,QAAQ,CAAC,IAAI1B,GAAG,CAAC,YAAY,CAAC;EAAE,IAAIwB,EAAE,IAAI,IAAI,EAAE;EAAQ,IAAIG,IAAI,GAAG,EAAE;EAAE,IAAIC,EAAE,GAAG,IAAI;EAAE,IAAIC,EAAE,GAAG,KAAK;EAAE,IAAIC,EAAE,EAAEC,EAAE;EAAE,IAAI;IAAE,KAAKP,EAAE,GAAGA,EAAE,CAACV,IAAI,CAACd,GAAG,CAAC,EAAE,EAAE4B,EAAE,GAAG,CAACE,EAAE,GAAGN,EAAE,CAACQ,IAAI,CAAC,CAAC,EAAEC,IAAI,CAAC,EAAEL,EAAE,GAAG,IAAI,EAAE;MAAED,IAAI,CAACO,IAAI,CAACJ,EAAE,CAACK,KAAK,CAAC;MAAE,IAAIlC,CAAC,IAAI0B,IAAI,CAACL,MAAM,KAAKrB,CAAC,EAAE;IAAO;EAAE,CAAC,CAAC,OAAOmC,GAAG,EAAE;IAAEP,EAAE,GAAG,IAAI;IAAEE,EAAE,GAAGK,GAAG;EAAE,CAAC,SAAS;IAAE,IAAI;MAAE,IAAI,CAACR,EAAE,IAAIJ,EAAE,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAEA,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAAE,CAAC,SAAS;MAAE,IAAIK,EAAE,EAAE,MAAME,EAAE;IAAE;EAAE;EAAE,OAAOJ,IAAI;AAAE;AAEhgB,SAASzB,eAAeA,CAACF,GAAG,EAAE;EAAE,IAAIkB,KAAK,CAACmB,OAAO,CAACrC,GAAG,CAAC,EAAE,OAAOA,GAAG;AAAE;AAEpE,SAASsC,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIlC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AAExJ,SAASmC,iBAAiBA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAAE,KAAK,IAAI1C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0C,KAAK,CAACrB,MAAM,EAAErB,CAAC,EAAE,EAAE;IAAE,IAAI2C,UAAU,GAAGD,KAAK,CAAC1C,CAAC,CAAC;IAAE2C,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;IAAED,UAAU,CAACE,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;IAAEpC,MAAM,CAACqC,cAAc,CAACN,MAAM,EAAEE,UAAU,CAACK,GAAG,EAAEL,UAAU,CAAC;EAAE;AAAE;AAE5T,SAASM,YAAYA,CAACV,WAAW,EAAEW,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEV,iBAAiB,CAACD,WAAW,CAAC5B,SAAS,EAAEuC,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAEX,iBAAiB,CAACD,WAAW,EAAEY,WAAW,CAAC;EAAEzC,MAAM,CAACqC,cAAc,CAACR,WAAW,EAAE,WAAW,EAAE;IAAEO,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAOP,WAAW;AAAE;AAE5R,OAAOa,QAAQ,MAAM,eAAe;AACpC,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,OAAOC,cAAc,MAAM,qBAAqB;AAChD,OAAOC,kBAAkB,IAAIC,iBAAiB,QAAQ,yBAAyB;AAC/E,OAAOC,eAAe,IAAIC,6BAA6B,QAAQ,sBAAsB;AACrF,OAAOC,uBAAuB,MAAM,sCAAsC;AAC1E,OAAOC,0BAA0B,MAAM,yCAAyC;AAChF,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,IAAIC,+BAA+B,GAAG,KAAK;AAE3C,IAAIC,SAAS,GAAG,aAAa,YAAY;EACvC;AACF;AACA;AACA;EACE,SAASA,SAASA,CAACC,uBAAuB,EAAEC,QAAQ,EAAE;IACpD5B,eAAe,CAAC,IAAI,EAAE0B,SAAS,CAAC;IAEhC,IAAI,CAACE,QAAQ,GAAG,IAAIb,QAAQ,CAACa,QAAQ,CAAC;IAEtC,IAAIC,qBAAqB,GAAG,IAAI,CAACC,wBAAwB,CAACH,uBAAuB,CAAC;MAC9EI,sBAAsB,GAAGtE,cAAc,CAACoE,qBAAqB,EAAE,CAAC,CAAC;MACjEG,cAAc,GAAGD,sBAAsB,CAAC,CAAC,CAAC;MAC1CE,kBAAkB,GAAGF,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD;IACA;;IAGA,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,KAAK,CAAC,CAAC;EACd;EAEAtB,YAAY,CAACc,SAAS,EAAE,CAAC;IACvBf,GAAG,EAAE,0BAA0B;IAC/Bd,KAAK,EAAE,SAASiC,wBAAwBA,CAACH,uBAAuB,EAAE;MAChE;MACA,IAAIK,cAAc;MAClB,IAAIC,kBAAkB,CAAC,CAAC;;MAExB,IAAIN,uBAAuB,EAAE;QAC3B,IAAIH,QAAQ,CAACG,uBAAuB,CAAC,EAAE;UACrCK,cAAc,GAAGL,uBAAuB,CAACK,cAAc;UACvDC,kBAAkB,GAAGN,uBAAuB,CAACM,kBAAkB;QACjE,CAAC,MAAM;UACLD,cAAc,GAAGL,uBAAuB;QAC1C;MACF;MAEA,IAAIK,cAAc,IAAI,CAAC,IAAI,CAACJ,QAAQ,CAACO,UAAU,CAACH,cAAc,CAAC,EAAE;QAC/DA,cAAc,GAAGI,SAAS;MAC5B;MAEA,IAAIH,kBAAkB,EAAE;QACtB;QACA,IAAIR,+BAA+B,EAAE;UACnC,IAAI,IAAI,CAACG,QAAQ,CAACS,0BAA0B,CAACJ,kBAAkB,CAAC,EAAE;YAChED,cAAc,GAAG,KAAK;UACxB;QACF;MACF;MAEA,OAAO,CAACA,cAAc,EAAEC,kBAAkB,CAAC;IAC7C;IACA;AACJ;AACA;AACA;AACA;EAEE,CAAC,EAAE;IACDtB,GAAG,EAAE,OAAO;IACZd,KAAK,EAAE,SAASyC,KAAKA,CAACC,IAAI,EAAE;MAC1B,IAAIC,kBAAkB,GAAG,IAAI,CAACC,MAAM,CAACH,KAAK,CAACC,IAAI,EAAE,IAAI,CAACG,KAAK,CAAC;QACxDC,MAAM,GAAGH,kBAAkB,CAACG,MAAM;QAClCC,eAAe,GAAGJ,kBAAkB,CAACI,eAAe;MAExD,IAAIA,eAAe,EAAE;QACnB,IAAI,CAACC,eAAe,GAAG,GAAG;MAC5B,CAAC,MAAM,IAAIF,MAAM,EAAE;QACjB,IAAI,CAACG,2BAA2B,CAAC,CAAC,CAAC,CAAC;;QAEpC,IAAI,IAAI,CAACJ,KAAK,CAACK,yBAAyB,EAAE;UACxC,IAAI,CAACC,SAAS,CAACC,yBAAyB,CAAC,IAAI,CAACP,KAAK,CAAC;QACtD;QAEA,IAAIQ,uBAAuB;QAE3B,IAAI,IAAI,CAACtB,QAAQ,CAACuB,wBAAwB,CAAC,CAAC,EAAE;UAC5CD,uBAAuB,GAAG,IAAI,CAACF,SAAS,CAACI,MAAM,CAACT,MAAM,EAAE,IAAI,CAACD,KAAK,CAAC;QACrE;QAEA,IAAIQ,uBAAuB,KAAKd,SAAS,EAAE;UACzC;UACA,IAAI,IAAI,CAACK,MAAM,CAACY,kCAAkC,CAAC,IAAI,CAACX,KAAK,CAAC,EAAE;YAC9D,IAAI,CAACI,2BAA2B,CAAC,CAAC,CAAC,CAAC;;YAEpC,IAAIQ,cAAc,GAAG,IAAI,CAACZ,KAAK,CAACa,iBAAiB,CAAC,CAAC;YAEnD,IAAID,cAAc,EAAE;cAClBJ,uBAAuB,GAAG,IAAI,CAACF,SAAS,CAACI,MAAM,CAACE,cAAc,EAAE,IAAI,CAACZ,KAAK,CAAC;YAC7E;UACF;QACF;QAEA,IAAI,CAACG,eAAe,GAAGK,uBAAuB,GAAG,IAAI,CAACM,aAAa,CAACN,uBAAuB,CAAC,GAAG,IAAI,CAACO,qBAAqB,CAAC,CAAC;MAC7H;MAEA,OAAO,IAAI,CAACZ,eAAe;IAC7B;EACF,CAAC,EAAE;IACDlC,GAAG,EAAE,OAAO;IACZd,KAAK,EAAE,SAASqC,KAAKA,CAAA,EAAG;MACtB,IAAIwB,KAAK,GAAG,IAAI;MAEhB,IAAI,CAAChB,KAAK,GAAG,IAAIzB,cAAc,CAAC;QAC9B0C,eAAe,EAAE,SAASA,eAAeA,CAACC,OAAO,EAAE;UACjD;UACA;UACA;UACA;UACA;UACAF,KAAK,CAACE,OAAO,GAAGA,OAAO;QACzB,CAAC;QACDC,mBAAmB,EAAE,SAASA,mBAAmBA,CAACC,WAAW,EAAEF,OAAO,EAAE;UACtEF,KAAK,CAAC9B,QAAQ,CAACmC,mBAAmB,CAACH,OAAO,EAAEE,WAAW,CAAC;UAExDJ,KAAK,CAACV,SAAS,CAACd,KAAK,CAACwB,KAAK,CAAC9B,QAAQ,CAACoC,aAAa,EAAEN,KAAK,CAAChB,KAAK,CAAC;UAEhEgB,KAAK,CAACjB,MAAM,CAACP,KAAK,CAACwB,KAAK,CAAC9B,QAAQ,CAACoC,aAAa,CAAC;QAClD;MACF,CAAC,CAAC;MACF,IAAI,CAAChB,SAAS,GAAG,IAAI9B,kBAAkB,CAAC;QACtCwB,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBd,QAAQ,EAAE,IAAI,CAACA;MACjB,CAAC,CAAC;MACF,IAAI,CAACa,MAAM,GAAG,IAAIrB,eAAe,CAAC;QAChCY,cAAc,EAAE,IAAI,CAACA,cAAc;QACnCC,kBAAkB,EAAE,IAAI,CAACA,kBAAkB;QAC3CL,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBc,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBuB,iCAAiC,EAAE,SAASA,iCAAiCA,CAAA,EAAG;UAC9EP,KAAK,CAACZ,2BAA2B,CAAC,CAAC;UAEnCY,KAAK,CAACV,SAAS,CAACd,KAAK,CAACwB,KAAK,CAAC9B,QAAQ,CAACoC,aAAa,EAAEN,KAAK,CAAChB,KAAK,CAAC;QAClE;MACF,CAAC,CAAC;MACF,IAAI,CAACA,KAAK,CAACR,KAAK,CAAC;QACf0B,OAAO,EAAE,IAAI,CAAC5B,cAAc;QAC5B8B,WAAW,EAAE,IAAI,CAAC7B;MACpB,CAAC,CAAC;MACF,IAAI,CAACY,eAAe,GAAG,EAAE;MACzB,OAAO,IAAI;IACb;IACA;AACJ;AACA;AACA;AACA;EAEE,CAAC,EAAE;IACDlC,GAAG,EAAE,iBAAiB;IACtBd,KAAK,EAAE,SAASqE,eAAeA,CAAA,EAAG;MAChC,OAAO,IAAI,CAACxB,KAAK,CAACyB,aAAa;IACjC;IACA;AACJ;AACA;AACA;AACA;AACA;EAEE,CAAC,EAAE;IACDxD,GAAG,EAAE,gBAAgB;IACrBd,KAAK,EAAE,SAASuE,cAAcA,CAAA,EAAG;MAC/B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,IAAI,CAACF,eAAe,CAAC,CAAC,EAAE;QAC1B,OAAO,IAAI,CAACxB,KAAK,CAACoB,WAAW;MAC/B;IACF,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDnD,GAAG,EAAE,uBAAuB;IAC5Bd,KAAK,EAAE,SAASwE,qBAAqBA,CAAA,EAAG;MACtC,OAAO,IAAI,CAACD,cAAc,CAAC,CAAC;IAC9B;IACA;AACJ;AACA;AACA;AACA;AACA;EAEE,CAAC,EAAE;IACDzD,GAAG,EAAE,YAAY;IACjBd,KAAK,EAAE,SAASyE,UAAUA,CAAA,EAAG;MAC3B,IAAI3B,MAAM,GAAG,IAAI,CAACD,KAAK,CAACC,MAAM,CAAC,CAAC;;MAEhC,IAAIA,MAAM,EAAE;QACV,OAAO,IAAI,CAAC4B,WAAW,CAAC,CAAC;MAC3B;IACF;IACA;AACJ;AACA;AACA;AACA;EAEE,CAAC,EAAE;IACD5D,GAAG,EAAE,aAAa;IAClBd,KAAK,EAAE,SAAS0E,WAAWA,CAAA,EAAG;MAC5B,IAAIX,OAAO,GAAG,IAAI,CAAClB,KAAK,CAACkB,OAAO;MAChC;;MAEA,IAAInC,+BAA+B,EAAE;QACnC;QACA;QACA,IAAImC,OAAO,KAAK,KAAK,EAAE;UACrB;QACF;MACF;MAEA,OAAOA,OAAO;IAChB;EACF,CAAC,EAAE;IACDjD,GAAG,EAAE,6BAA6B;IAClCd,KAAK,EAAE,SAASiD,2BAA2BA,CAAA,EAAG;MAC5C;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,CAAC,IAAI,CAACJ,KAAK,CAACkB,OAAO,IAAI,IAAI,CAACY,6BAA6B,CAAC,CAAC,EAAE;QAC/D,IAAI,CAACC,mBAAmB,CAAC,CAAC;MAC5B;IACF,CAAC,CAAC;EAEJ,CAAC,EAAE;IACD9D,GAAG,EAAE,eAAe;IACpBd,KAAK,EAAE,SAAS2D,aAAaA,CAACN,uBAAuB,EAAE;MACrD,IAAIwB,MAAM,GAAG,IAAI;MAEjB,IAAI,IAAI,CAACR,eAAe,CAAC,CAAC,EAAE;QAC1B,IAAIS,MAAM,GAAG,SAASA,MAAMA,CAACpC,IAAI,EAAE;UACjC,OAAOmC,MAAM,CAAC1B,SAAS,CAAC4B,8CAA8C,CAACF,MAAM,CAAChC,KAAK,EAAE;YACnFmC,OAAO,EAAEtC,IAAI,GAAG,IAAI,GAAG;UACzB,CAAC,CAAC,GAAGA,IAAI;QACX,CAAC;QAED,IAAIuB,WAAW,GAAG,IAAI,CAACpB,KAAK,CAACoB,WAAW;QAExC,IAAI,CAACA,WAAW,EAAE;UAChB,OAAOa,MAAM,CAAC,EAAE,CAACG,MAAM,CAAC,IAAI,CAACpC,KAAK,CAACqC,mCAAmC,CAAC,CAAC,CAAC,CAAC;QAC5E;QAEA,IAAI,CAAC7B,uBAAuB,EAAE;UAC5B,OAAOyB,MAAM,CAACb,WAAW,CAAC;QAC5B;QAEA,OAAOa,MAAM,CAAC,EAAE,CAACG,MAAM,CAAChB,WAAW,EAAE,GAAG,CAAC,CAACgB,MAAM,CAAC5B,uBAAuB,CAAC,CAAC;MAC5E;MAEA,OAAOA,uBAAuB;IAChC;EACF,CAAC,EAAE;IACDvC,GAAG,EAAE,yCAAyC;IAC9Cd,KAAK,EAAE,SAASmF,uCAAuCA,CAAA,EAAG;MACxD,IAAIC,WAAW,GAAG,IAAI,CAACvC,KAAK;QACxBK,yBAAyB,GAAGkC,WAAW,CAAClC,yBAAyB;QACjEmC,4CAA4C,GAAGD,WAAW,CAACC,4CAA4C;QACvGC,cAAc,GAAGF,WAAW,CAACE,cAAc;MAC/C,IAAIC,MAAM,GAAGrC,yBAAyB;MACtC,IAAI4B,MAAM,GAAGO,4CAA4C,IAAIC,cAAc;MAE3E,IAAIR,MAAM,EAAE;QACVS,MAAM,GAAGT,MAAM,GAAGS,MAAM;MAC1B;MAEA,OAAOA,MAAM;IACf;EACF,CAAC,EAAE;IACDzE,GAAG,EAAE,uBAAuB;IAC5Bd,KAAK,EAAE,SAAS4D,qBAAqBA,CAAA,EAAG;MACtC,IAAI4B,qCAAqC,GAAG,IAAI,CAAC3C,KAAK,CAAC2C,qCAAqC;MAC5F,OAAO,IAAI,CAAC7B,aAAa,CAAC6B,qCAAqC,GAAG,IAAI,CAACL,uCAAuC,CAAC,CAAC,GAAG,IAAI,CAACtC,KAAK,CAACa,iBAAiB,CAAC,CAAC,CAAC;IACpJ;EACF,CAAC,EAAE;IACD5C,GAAG,EAAE,yBAAyB;IAC9Bd,KAAK,EAAE,SAASyF,uBAAuBA,CAAA,EAAG;MACxC,IAAIF,MAAM,GAAG,IAAI,CAAC3B,qBAAqB,CAAC,CAAC;MAEzC,IAAI2B,MAAM,EAAE;QACV,OAAOA,MAAM,CAACG,OAAO,CAAC,SAAS,EAAEpE,iBAAiB,CAAC;MACrD;IACF;EACF,CAAC,EAAE;IACDR,GAAG,EAAE,+BAA+B;IACpCd,KAAK,EAAE,SAAS2E,6BAA6BA,CAAA,EAAG;MAC9C,IAAIV,WAAW,GAAG,IAAI,CAACpB,KAAK,CAACoB,WAAW;MACxC,IAAI0B,YAAY,GAAG,IAAI,CAAC5D,QAAQ,CAAC6D,6BAA6B,CAAC3B,WAAW,CAAC;MAC3E,OAAO0B,YAAY,IAAIA,YAAY,CAACxG,MAAM,GAAG,CAAC;IAChD,CAAC,CAAC;IACF;IACA;EAEF,CAAC,EAAE;IACD2B,GAAG,EAAE,qBAAqB;IAC1Bd,KAAK,EAAE,SAAS4E,mBAAmBA,CAAA,EAAG;MACpC,IAAI,CAAC/B,KAAK,CAACgD,UAAU,CAACpE,uBAAuB,CAAC,IAAI,CAAC4C,eAAe,CAAC,CAAC,GAAG,IAAI,CAACxB,KAAK,CAACoB,WAAW,GAAG,IAAI,CAAC7B,kBAAkB,EAAE;QACvH0D,cAAc,EAAE,IAAI,CAACjD,KAAK,CAACK,yBAAyB;QACpDf,cAAc,EAAE,IAAI,CAACA,cAAc;QACnCJ,QAAQ,EAAE,IAAI,CAACA;MACjB,CAAC,CAAC,CAAC;IACL;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAEE,CAAC,EAAE;IACDjB,GAAG,EAAE,gBAAgB;IACrBd,KAAK,EAAE,SAAS+F,cAAcA,CAAA,EAAG;MAC/B,IAAIC,YAAY,GAAG,IAAI,CAACnD,KAAK;QACzBC,MAAM,GAAGkD,YAAY,CAAClD,MAAM;QAC5BmB,WAAW,GAAG+B,YAAY,CAAC/B,WAAW;QACtCF,OAAO,GAAGiC,YAAY,CAACjC,OAAO;QAC9Bb,yBAAyB,GAAG8C,YAAY,CAAC9C,yBAAyB,CAAC,CAAC;;MAExE,IAAI,CAACJ,MAAM,EAAE;QACX;MACF;MAEA,IAAI,IAAI,CAACuB,eAAe,CAAC,CAAC,EAAE;QAC1B,IAAIJ,WAAW,EAAE;UACf,OAAO,GAAG,GAAGA,WAAW,GAAGf,yBAAyB;QACtD,CAAC,MAAM;UACL,OAAO,GAAG,GAAGJ,MAAM;QACrB;MACF,CAAC,MAAM;QACL,IAAIiB,OAAO,IAAIE,WAAW,EAAE;UAC1B,IAAIgC,YAAY,GAAGlC,OAAO,GAAG,IAAI,CAAChC,QAAQ,CAACmE,kBAAkB,CAAC,CAAC,GAAGjC,WAAW;UAC7E,OAAO,GAAG,GAAGgC,YAAY,GAAG/C,yBAAyB;QACvD;MACF;IACF;IACA;AACJ;AACA;AACA;AACA;AACA;EAEE,CAAC,EAAE;IACDpC,GAAG,EAAE,WAAW;IAChBd,KAAK,EAAE,SAASmG,SAASA,CAAA,EAAG;MAC1B,IAAIC,YAAY,GAAG,IAAI,CAACvD,KAAK;QACzBK,yBAAyB,GAAGkD,YAAY,CAAClD,yBAAyB;QAClEmD,WAAW,GAAGD,YAAY,CAACC,WAAW;QACtCpC,WAAW,GAAGmC,YAAY,CAACnC,WAAW,CAAC,CAAC;MAC5C;MACA;;MAEA,IAAIF,OAAO,GAAG,IAAI,CAACW,WAAW,CAAC,CAAC;MAEhC,IAAI,CAACxB,yBAAyB,EAAE;QAC9B;MACF,CAAC,CAAC;MACF;MACA;;MAGA,IAAI,CAACa,OAAO,IAAI,CAACE,WAAW,EAAE;QAC5B;MACF,CAAC,CAAC;MACF;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAGA,IAAIF,OAAO,EAAE;QACX,IAAIA,OAAO,KAAK,IAAI,CAAC5B,cAAc,EAAE;UACnC;UACA;UACA;UACA,IAAIJ,QAAQ,GAAG,IAAIb,QAAQ,CAAC,IAAI,CAACa,QAAQ,CAACA,QAAQ,CAAC;UACnDA,QAAQ,CAACmC,mBAAmB,CAACH,OAAO,CAAC;UAErC,IAAIuC,YAAY,GAAGvE,QAAQ,CAACoC,aAAa,CAACF,WAAW,CAAC,CAAC;UAEvD,IAAIsC,kBAAkB,GAAG,IAAI,CAACxE,QAAQ,CAAC6D,6BAA6B,CAACU,YAAY,CAAC;UAElF,IAAIC,kBAAkB,CAACpH,MAAM,GAAG,CAAC,EAAE;YACjC,IAAIqH,YAAY,GAAG9E,0BAA0B,CAACwB,yBAAyB,EAAE;cACvEuD,SAAS,EAAEF,kBAAkB;cAC7BpE,cAAc,EAAE,IAAI,CAACA,cAAc;cACnCJ,QAAQ,EAAE,IAAI,CAACA,QAAQ,CAACA;YAC1B,CAAC,CAAC;YAEF,IAAIyE,YAAY,EAAE;cAChBzC,OAAO,GAAGyC,YAAY;YACxB;UACF;QACF;MACF;MAEA,IAAIE,WAAW,GAAG,IAAIvF,WAAW,CAAC4C,OAAO,IAAIE,WAAW,EAAEf,yBAAyB,EAAE,IAAI,CAACnB,QAAQ,CAACA,QAAQ,CAAC;MAE5G,IAAIsE,WAAW,EAAE;QACfK,WAAW,CAACL,WAAW,GAAGA,WAAW;MACvC,CAAC,CAAC;;MAGF,OAAOK,WAAW;IACpB;IACA;AACJ;AACA;AACA;AACA;EAEE,CAAC,EAAE;IACD5F,GAAG,EAAE,YAAY;IACjBd,KAAK,EAAE,SAAS2G,UAAUA,CAAA,EAAG;MAC3B,IAAID,WAAW,GAAG,IAAI,CAACP,SAAS,CAAC,CAAC;MAElC,IAAI,CAACO,WAAW,EAAE;QAChB,OAAO,KAAK;MACd;MAEA,OAAOA,WAAW,CAACC,UAAU,CAAC,CAAC;IACjC;IACA;AACJ;AACA;AACA;AACA;EAEE,CAAC,EAAE;IACD7F,GAAG,EAAE,SAAS;IACdd,KAAK,EAAE,SAAS4G,OAAOA,CAAA,EAAG;MACxB,IAAIF,WAAW,GAAG,IAAI,CAACP,SAAS,CAAC,CAAC;MAElC,IAAI,CAACO,WAAW,EAAE;QAChB,OAAO,KAAK;MACd;MAEA,OAAOA,WAAW,CAACE,OAAO,CAAC,CAAC;IAC9B;IACA;AACJ;AACA;AACA;AACA;EAEE,CAAC,EAAE;IACD9F,GAAG,EAAE,mBAAmB;IACxBd,KAAK,EAAE,SAAS6G,iBAAiBA,CAAA,EAAG;MAClC,OAAO,IAAI,CAAChE,KAAK,CAACK,yBAAyB;IAC7C;IACA;AACJ;AACA;AACA;EAEE,CAAC,EAAE;IACDpC,GAAG,EAAE,UAAU;IACfd,KAAK,EAAE,SAAS8G,QAAQA,CAAA,EAAG;MACzB,OAAO,CAAC,IAAI,CAACjE,KAAK,CAACyB,aAAa,GAAG,GAAG,GAAG,EAAE,IAAI,IAAI,CAACzB,KAAK,CAACC,MAAM;IAClE;IACA;AACJ;AACA;AACA;EAEE,CAAC,EAAE;IACDhC,GAAG,EAAE,aAAa;IAClBd,KAAK,EAAE,SAAS+G,WAAWA,CAAA,EAAG;MAC5B,OAAO,IAAI,CAAC5D,SAAS,CAAC4D,WAAW,CAAC,IAAI,CAAClE,KAAK,CAAC,IAAI,IAAI,CAAC4C,uBAAuB,CAAC,CAAC,IAAI,EAAE;IACvF;EACF,CAAC,CAAC,CAAC;EAEH,OAAO5D,SAAS;AAClB,CAAC,CAAC,CAAC;AAEH,SAASA,SAAS,IAAImF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}