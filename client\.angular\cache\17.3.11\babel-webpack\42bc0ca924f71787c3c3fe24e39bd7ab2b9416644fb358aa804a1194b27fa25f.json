{"ast": null, "code": "function _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\n// https://medium.com/dsinjs/implementing-lru-cache-in-javascript-94ba6755cda9\nvar Node = /*#__PURE__*/_createClass(function Node(key, value) {\n  var next = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n  var prev = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;\n  _classCallCheck(this, Node);\n  this.key = key;\n  this.value = value;\n  this.next = next;\n  this.prev = prev;\n});\nvar LRUCache = /*#__PURE__*/function () {\n  //set default limit of 10 if limit is not passed.\n  function LRUCache() {\n    var limit = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 10;\n    _classCallCheck(this, LRUCache);\n    this.size = 0;\n    this.limit = limit;\n    this.head = null;\n    this.tail = null;\n    this.cache = {};\n  } // Write Node to head of LinkedList\n  // update cache with Node key and Node reference\n\n  _createClass(LRUCache, [{\n    key: \"put\",\n    value: function put(key, value) {\n      this.ensureLimit();\n      if (!this.head) {\n        this.head = this.tail = new Node(key, value);\n      } else {\n        var node = new Node(key, value, this.head);\n        this.head.prev = node;\n        this.head = node;\n      } //Update the cache map\n\n      this.cache[key] = this.head;\n      this.size++;\n    } // Read from cache map and make that node as new Head of LinkedList\n  }, {\n    key: \"get\",\n    value: function get(key) {\n      if (this.cache[key]) {\n        var value = this.cache[key].value; // node removed from it's position and cache\n\n        this.remove(key); // write node again to the head of LinkedList to make it most recently used\n\n        this.put(key, value);\n        return value;\n      }\n      console.log(\"Item not available in cache for key \".concat(key));\n    }\n  }, {\n    key: \"ensureLimit\",\n    value: function ensureLimit() {\n      if (this.size === this.limit) {\n        this.remove(this.tail.key);\n      }\n    }\n  }, {\n    key: \"remove\",\n    value: function remove(key) {\n      var node = this.cache[key];\n      if (node.prev !== null) {\n        node.prev.next = node.next;\n      } else {\n        this.head = node.next;\n      }\n      if (node.next !== null) {\n        node.next.prev = node.prev;\n      } else {\n        this.tail = node.prev;\n      }\n      delete this.cache[key];\n      this.size--;\n    }\n  }, {\n    key: \"clear\",\n    value: function clear() {\n      this.head = null;\n      this.tail = null;\n      this.size = 0;\n      this.cache = {};\n    } // // Invokes the callback function with every node of the chain and the index of the node.\n    // forEach(fn) {\n    //   let node = this.head;\n    //   let counter = 0;\n    //   while (node) {\n    //     fn(node, counter);\n    //     node = node.next;\n    //     counter++;\n    //   }\n    // }\n    // // To iterate over LRU with a 'for...of' loop\n    // *[Symbol.iterator]() {\n    //   let node = this.head;\n    //   while (node) {\n    //     yield node;\n    //     node = node.next;\n    //   }\n    // }\n  }]);\n  return LRUCache;\n}();\nexport { LRUCache as default };", "map": {"version": 3, "names": ["_defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "Object", "defineProperty", "key", "_createClass", "<PERSON><PERSON><PERSON><PERSON>", "protoProps", "staticProps", "prototype", "_classCallCheck", "instance", "TypeError", "Node", "value", "next", "arguments", "undefined", "prev", "L<PERSON><PERSON><PERSON>", "limit", "size", "head", "tail", "cache", "put", "ensureLimit", "node", "get", "remove", "console", "log", "concat", "clear", "default"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/findNumbers/LRUCache.js"], "sourcesContent": ["function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\n// https://medium.com/dsinjs/implementing-lru-cache-in-javascript-94ba6755cda9\nvar Node = /*#__PURE__*/_createClass(function Node(key, value) {\n  var next = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n  var prev = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;\n\n  _classCallCheck(this, Node);\n\n  this.key = key;\n  this.value = value;\n  this.next = next;\n  this.prev = prev;\n});\n\nvar LRUCache = /*#__PURE__*/function () {\n  //set default limit of 10 if limit is not passed.\n  function LRUCache() {\n    var limit = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 10;\n\n    _classCallCheck(this, LRUCache);\n\n    this.size = 0;\n    this.limit = limit;\n    this.head = null;\n    this.tail = null;\n    this.cache = {};\n  } // Write Node to head of LinkedList\n  // update cache with Node key and Node reference\n\n\n  _createClass(LRUCache, [{\n    key: \"put\",\n    value: function put(key, value) {\n      this.ensureLimit();\n\n      if (!this.head) {\n        this.head = this.tail = new Node(key, value);\n      } else {\n        var node = new Node(key, value, this.head);\n        this.head.prev = node;\n        this.head = node;\n      } //Update the cache map\n\n\n      this.cache[key] = this.head;\n      this.size++;\n    } // Read from cache map and make that node as new Head of LinkedList\n\n  }, {\n    key: \"get\",\n    value: function get(key) {\n      if (this.cache[key]) {\n        var value = this.cache[key].value; // node removed from it's position and cache\n\n        this.remove(key); // write node again to the head of LinkedList to make it most recently used\n\n        this.put(key, value);\n        return value;\n      }\n\n      console.log(\"Item not available in cache for key \".concat(key));\n    }\n  }, {\n    key: \"ensureLimit\",\n    value: function ensureLimit() {\n      if (this.size === this.limit) {\n        this.remove(this.tail.key);\n      }\n    }\n  }, {\n    key: \"remove\",\n    value: function remove(key) {\n      var node = this.cache[key];\n\n      if (node.prev !== null) {\n        node.prev.next = node.next;\n      } else {\n        this.head = node.next;\n      }\n\n      if (node.next !== null) {\n        node.next.prev = node.prev;\n      } else {\n        this.tail = node.prev;\n      }\n\n      delete this.cache[key];\n      this.size--;\n    }\n  }, {\n    key: \"clear\",\n    value: function clear() {\n      this.head = null;\n      this.tail = null;\n      this.size = 0;\n      this.cache = {};\n    } // // Invokes the callback function with every node of the chain and the index of the node.\n    // forEach(fn) {\n    //   let node = this.head;\n    //   let counter = 0;\n    //   while (node) {\n    //     fn(node, counter);\n    //     node = node.next;\n    //     counter++;\n    //   }\n    // }\n    // // To iterate over LRU with a 'for...of' loop\n    // *[Symbol.iterator]() {\n    //   let node = this.head;\n    //   while (node) {\n    //     yield node;\n    //     node = node.next;\n    //   }\n    // }\n\n  }]);\n\n  return LRUCache;\n}();\n\nexport { LRUCache as default };\n"], "mappings": "AAAA,SAASA,iBAAiBA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IAAE,IAAIE,UAAU,GAAGH,KAAK,CAACC,CAAC,CAAC;IAAEE,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;IAAED,UAAU,CAACE,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;IAAEC,MAAM,CAACC,cAAc,CAACT,MAAM,EAAEI,UAAU,CAACM,GAAG,EAAEN,UAAU,CAAC;EAAE;AAAE;AAE5T,SAASO,YAAYA,CAACC,WAAW,EAAEC,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEd,iBAAiB,CAACa,WAAW,CAACG,SAAS,EAAEF,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAEf,iBAAiB,CAACa,WAAW,EAAEE,WAAW,CAAC;EAAEN,MAAM,CAACC,cAAc,CAACG,WAAW,EAAE,WAAW,EAAE;IAAEL,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAOK,WAAW;AAAE;AAE5R,SAASI,eAAeA,CAACC,QAAQ,EAAEL,WAAW,EAAE;EAAE,IAAI,EAAEK,QAAQ,YAAYL,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIM,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;;AAExJ;AACA,IAAIC,IAAI,GAAG,aAAaR,YAAY,CAAC,SAASQ,IAAIA,CAACT,GAAG,EAAEU,KAAK,EAAE;EAC7D,IAAIC,IAAI,GAAGC,SAAS,CAACnB,MAAM,GAAG,CAAC,IAAImB,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;EACnF,IAAIE,IAAI,GAAGF,SAAS,CAACnB,MAAM,GAAG,CAAC,IAAImB,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;EAEnFN,eAAe,CAAC,IAAI,EAAEG,IAAI,CAAC;EAE3B,IAAI,CAACT,GAAG,GAAGA,GAAG;EACd,IAAI,CAACU,KAAK,GAAGA,KAAK;EAClB,IAAI,CAACC,IAAI,GAAGA,IAAI;EAChB,IAAI,CAACG,IAAI,GAAGA,IAAI;AAClB,CAAC,CAAC;AAEF,IAAIC,QAAQ,GAAG,aAAa,YAAY;EACtC;EACA,SAASA,QAAQA,CAAA,EAAG;IAClB,IAAIC,KAAK,GAAGJ,SAAS,CAACnB,MAAM,GAAG,CAAC,IAAImB,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;IAElFN,eAAe,CAAC,IAAI,EAAES,QAAQ,CAAC;IAE/B,IAAI,CAACE,IAAI,GAAG,CAAC;IACb,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACE,IAAI,GAAG,IAAI;IAChB,IAAI,CAACC,IAAI,GAAG,IAAI;IAChB,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;EACjB,CAAC,CAAC;EACF;;EAGAnB,YAAY,CAACc,QAAQ,EAAE,CAAC;IACtBf,GAAG,EAAE,KAAK;IACVU,KAAK,EAAE,SAASW,GAAGA,CAACrB,GAAG,EAAEU,KAAK,EAAE;MAC9B,IAAI,CAACY,WAAW,CAAC,CAAC;MAElB,IAAI,CAAC,IAAI,CAACJ,IAAI,EAAE;QACd,IAAI,CAACA,IAAI,GAAG,IAAI,CAACC,IAAI,GAAG,IAAIV,IAAI,CAACT,GAAG,EAAEU,KAAK,CAAC;MAC9C,CAAC,MAAM;QACL,IAAIa,IAAI,GAAG,IAAId,IAAI,CAACT,GAAG,EAAEU,KAAK,EAAE,IAAI,CAACQ,IAAI,CAAC;QAC1C,IAAI,CAACA,IAAI,CAACJ,IAAI,GAAGS,IAAI;QACrB,IAAI,CAACL,IAAI,GAAGK,IAAI;MAClB,CAAC,CAAC;;MAGF,IAAI,CAACH,KAAK,CAACpB,GAAG,CAAC,GAAG,IAAI,CAACkB,IAAI;MAC3B,IAAI,CAACD,IAAI,EAAE;IACb,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDjB,GAAG,EAAE,KAAK;IACVU,KAAK,EAAE,SAASc,GAAGA,CAACxB,GAAG,EAAE;MACvB,IAAI,IAAI,CAACoB,KAAK,CAACpB,GAAG,CAAC,EAAE;QACnB,IAAIU,KAAK,GAAG,IAAI,CAACU,KAAK,CAACpB,GAAG,CAAC,CAACU,KAAK,CAAC,CAAC;;QAEnC,IAAI,CAACe,MAAM,CAACzB,GAAG,CAAC,CAAC,CAAC;;QAElB,IAAI,CAACqB,GAAG,CAACrB,GAAG,EAAEU,KAAK,CAAC;QACpB,OAAOA,KAAK;MACd;MAEAgB,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAACC,MAAM,CAAC5B,GAAG,CAAC,CAAC;IACjE;EACF,CAAC,EAAE;IACDA,GAAG,EAAE,aAAa;IAClBU,KAAK,EAAE,SAASY,WAAWA,CAAA,EAAG;MAC5B,IAAI,IAAI,CAACL,IAAI,KAAK,IAAI,CAACD,KAAK,EAAE;QAC5B,IAAI,CAACS,MAAM,CAAC,IAAI,CAACN,IAAI,CAACnB,GAAG,CAAC;MAC5B;IACF;EACF,CAAC,EAAE;IACDA,GAAG,EAAE,QAAQ;IACbU,KAAK,EAAE,SAASe,MAAMA,CAACzB,GAAG,EAAE;MAC1B,IAAIuB,IAAI,GAAG,IAAI,CAACH,KAAK,CAACpB,GAAG,CAAC;MAE1B,IAAIuB,IAAI,CAACT,IAAI,KAAK,IAAI,EAAE;QACtBS,IAAI,CAACT,IAAI,CAACH,IAAI,GAAGY,IAAI,CAACZ,IAAI;MAC5B,CAAC,MAAM;QACL,IAAI,CAACO,IAAI,GAAGK,IAAI,CAACZ,IAAI;MACvB;MAEA,IAAIY,IAAI,CAACZ,IAAI,KAAK,IAAI,EAAE;QACtBY,IAAI,CAACZ,IAAI,CAACG,IAAI,GAAGS,IAAI,CAACT,IAAI;MAC5B,CAAC,MAAM;QACL,IAAI,CAACK,IAAI,GAAGI,IAAI,CAACT,IAAI;MACvB;MAEA,OAAO,IAAI,CAACM,KAAK,CAACpB,GAAG,CAAC;MACtB,IAAI,CAACiB,IAAI,EAAE;IACb;EACF,CAAC,EAAE;IACDjB,GAAG,EAAE,OAAO;IACZU,KAAK,EAAE,SAASmB,KAAKA,CAAA,EAAG;MACtB,IAAI,CAACX,IAAI,GAAG,IAAI;MAChB,IAAI,CAACC,IAAI,GAAG,IAAI;MAChB,IAAI,CAACF,IAAI,GAAG,CAAC;MACb,IAAI,CAACG,KAAK,GAAG,CAAC,CAAC;IACjB,CAAC,CAAC;IACF;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAEF,CAAC,CAAC,CAAC;EAEH,OAAOL,QAAQ;AACjB,CAAC,CAAC,CAAC;AAEH,SAASA,QAAQ,IAAIe,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}