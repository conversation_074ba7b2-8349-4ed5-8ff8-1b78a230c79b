{"ast": null, "code": "function _createForOfIteratorHelperLoose(o, allowArrayLike) {\n  var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"];\n  if (it) return (it = it.call(o)).next.bind(it);\n  if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") {\n    if (it) o = it;\n    var i = 0;\n    return function () {\n      if (i >= o.length) return {\n        done: true\n      };\n      return {\n        done: false,\n        value: o[i++]\n      };\n    };\n  }\n  throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n  return arr2;\n}\nimport { parseDigit } from './helpers/parseDigits.js';\n/**\r\n * Parses phone number characters from a string.\r\n * Drops all punctuation leaving only digits and the leading `+` sign (if any).\r\n * Also converts wide-ascii and arabic-indic numerals to conventional numerals.\r\n * E.g. in Iraq they don't write `+442323234` but rather `+٤٤٢٣٢٣٢٣٤`.\r\n * @param  {string} string\r\n * @return {string}\r\n * @example\r\n * ```js\r\n * // Outputs '8800555'.\r\n * parseIncompletePhoneNumber('8 (800) 555')\r\n * // Outputs '+7800555'.\r\n * parseIncompletePhoneNumber('****** 555')\r\n * ```\r\n */\n\nexport default function parseIncompletePhoneNumber(string) {\n  var result = ''; // Using `.split('')` here instead of normal `for ... of`\n  // because the importing application doesn't neccessarily include an ES6 polyfill.\n  // The `.split('')` approach discards \"exotic\" UTF-8 characters\n  // (the ones consisting of four bytes) but digits\n  // (including non-European ones) don't fall into that range\n  // so such \"exotic\" characters would be discarded anyway.\n\n  for (var _iterator = _createForOfIteratorHelperLoose(string.split('')), _step; !(_step = _iterator()).done;) {\n    var character = _step.value;\n    result += parsePhoneNumberCharacter(character, result) || '';\n  }\n  return result;\n}\n/**\r\n * Parses next character while parsing phone number digits (including a `+`)\r\n * from text: discards everything except `+` and digits, and `+` is only allowed\r\n * at the start of a phone number.\r\n * For example, is used in `react-phone-number-input` where it uses\r\n * [`input-format`](https://gitlab.com/catamphetamine/input-format).\r\n * @param  {string} character - Yet another character from raw input string.\r\n * @param  {string?} prevParsedCharacters - Previous parsed characters.\r\n * @param  {function?} emitEvent - An optional \"emit event\" function.\r\n * @return {string?} The parsed character.\r\n */\n\nexport function parsePhoneNumberCharacter(character, prevParsedCharacters, emitEvent) {\n  // Only allow a leading `+`.\n  if (character === '+') {\n    // If this `+` is not the first parsed character\n    // then discard it.\n    if (prevParsedCharacters) {\n      // `emitEvent` argument was added to this `export`ed function on Dec 26th, 2023.\n      // Any 3rd-party code that used to `import` and call this function before that\n      // won't be passing any `emitEvent` argument.\n      //\n      // The addition of the `emitEvent` argument was to fix the slightly-weird behavior\n      // of parsing an input string when the user inputs something like `\"2+7\"\n      // https://github.com/catamphetamine/react-phone-number-input/issues/437\n      //\n      // If the parser encounters an unexpected `+` in a string being parsed\n      // then it simply discards that out-of-place `+` and any following characters.\n      //\n      if (typeof emitEvent === 'function') {\n        emitEvent('end');\n      }\n      return;\n    }\n    return '+';\n  } // Allow digits.\n\n  return parseDigit(character);\n}", "map": {"version": 3, "names": ["_createForOfIteratorHelperLoose", "o", "allowArrayLike", "it", "Symbol", "iterator", "call", "next", "bind", "Array", "isArray", "_unsupportedIterableToArray", "length", "i", "done", "value", "TypeError", "minLen", "_arrayLikeToArray", "n", "Object", "prototype", "toString", "slice", "constructor", "name", "from", "test", "arr", "len", "arr2", "parseDigit", "parseIncompletePhoneNumber", "string", "result", "_iterator", "split", "_step", "character", "parsePhoneNumberCharacter", "prevParsedCharacters", "emitEvent"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/parseIncompletePhoneNumber.js"], "sourcesContent": ["function _createForOfIteratorHelperLoose(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (it) return (it = it.call(o)).next.bind(it); if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; return function () { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nimport { parseDigit } from './helpers/parseDigits.js';\n/**\r\n * Parses phone number characters from a string.\r\n * Drops all punctuation leaving only digits and the leading `+` sign (if any).\r\n * Also converts wide-ascii and arabic-indic numerals to conventional numerals.\r\n * E.g. in Iraq they don't write `+442323234` but rather `+٤٤٢٣٢٣٢٣٤`.\r\n * @param  {string} string\r\n * @return {string}\r\n * @example\r\n * ```js\r\n * // Outputs '8800555'.\r\n * parseIncompletePhoneNumber('8 (800) 555')\r\n * // Outputs '+7800555'.\r\n * parseIncompletePhoneNumber('****** 555')\r\n * ```\r\n */\n\nexport default function parseIncompletePhoneNumber(string) {\n  var result = ''; // Using `.split('')` here instead of normal `for ... of`\n  // because the importing application doesn't neccessarily include an ES6 polyfill.\n  // The `.split('')` approach discards \"exotic\" UTF-8 characters\n  // (the ones consisting of four bytes) but digits\n  // (including non-European ones) don't fall into that range\n  // so such \"exotic\" characters would be discarded anyway.\n\n  for (var _iterator = _createForOfIteratorHelperLoose(string.split('')), _step; !(_step = _iterator()).done;) {\n    var character = _step.value;\n    result += parsePhoneNumberCharacter(character, result) || '';\n  }\n\n  return result;\n}\n/**\r\n * Parses next character while parsing phone number digits (including a `+`)\r\n * from text: discards everything except `+` and digits, and `+` is only allowed\r\n * at the start of a phone number.\r\n * For example, is used in `react-phone-number-input` where it uses\r\n * [`input-format`](https://gitlab.com/catamphetamine/input-format).\r\n * @param  {string} character - Yet another character from raw input string.\r\n * @param  {string?} prevParsedCharacters - Previous parsed characters.\r\n * @param  {function?} emitEvent - An optional \"emit event\" function.\r\n * @return {string?} The parsed character.\r\n */\n\nexport function parsePhoneNumberCharacter(character, prevParsedCharacters, emitEvent) {\n  // Only allow a leading `+`.\n  if (character === '+') {\n    // If this `+` is not the first parsed character\n    // then discard it.\n    if (prevParsedCharacters) {\n      // `emitEvent` argument was added to this `export`ed function on Dec 26th, 2023.\n      // Any 3rd-party code that used to `import` and call this function before that\n      // won't be passing any `emitEvent` argument.\n      //\n      // The addition of the `emitEvent` argument was to fix the slightly-weird behavior\n      // of parsing an input string when the user inputs something like `\"2+7\"\n      // https://github.com/catamphetamine/react-phone-number-input/issues/437\n      //\n      // If the parser encounters an unexpected `+` in a string being parsed\n      // then it simply discards that out-of-place `+` and any following characters.\n      //\n      if (typeof emitEvent === 'function') {\n        emitEvent('end');\n      }\n\n      return;\n    }\n\n    return '+';\n  } // Allow digits.\n\n\n  return parseDigit(character);\n}\n"], "mappings": "AAAA,SAASA,+BAA+BA,CAACC,CAAC,EAAEC,cAAc,EAAE;EAAE,IAAIC,EAAE,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAIH,CAAC,CAACG,MAAM,CAACC,QAAQ,CAAC,IAAIJ,CAAC,CAAC,YAAY,CAAC;EAAE,IAAIE,EAAE,EAAE,OAAO,CAACA,EAAE,GAAGA,EAAE,CAACG,IAAI,CAACL,CAAC,CAAC,EAAEM,IAAI,CAACC,IAAI,CAACL,EAAE,CAAC;EAAE,IAAIM,KAAK,CAACC,OAAO,CAACT,CAAC,CAAC,KAAKE,EAAE,GAAGQ,2BAA2B,CAACV,CAAC,CAAC,CAAC,IAAIC,cAAc,IAAID,CAAC,IAAI,OAAOA,CAAC,CAACW,MAAM,KAAK,QAAQ,EAAE;IAAE,IAAIT,EAAE,EAAEF,CAAC,GAAGE,EAAE;IAAE,IAAIU,CAAC,GAAG,CAAC;IAAE,OAAO,YAAY;MAAE,IAAIA,CAAC,IAAIZ,CAAC,CAACW,MAAM,EAAE,OAAO;QAAEE,IAAI,EAAE;MAAK,CAAC;MAAE,OAAO;QAAEA,IAAI,EAAE,KAAK;QAAEC,KAAK,EAAEd,CAAC,CAACY,CAAC,EAAE;MAAE,CAAC;IAAE,CAAC;EAAE;EAAE,MAAM,IAAIG,SAAS,CAAC,uIAAuI,CAAC;AAAE;AAE3lB,SAASL,2BAA2BA,CAACV,CAAC,EAAEgB,MAAM,EAAE;EAAE,IAAI,CAAChB,CAAC,EAAE;EAAQ,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOiB,iBAAiB,CAACjB,CAAC,EAAEgB,MAAM,CAAC;EAAE,IAAIE,CAAC,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAAChB,IAAI,CAACL,CAAC,CAAC,CAACsB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAE,IAAIJ,CAAC,KAAK,QAAQ,IAAIlB,CAAC,CAACuB,WAAW,EAAEL,CAAC,GAAGlB,CAAC,CAACuB,WAAW,CAACC,IAAI;EAAE,IAAIN,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOV,KAAK,CAACiB,IAAI,CAACzB,CAAC,CAAC;EAAE,IAAIkB,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACQ,IAAI,CAACR,CAAC,CAAC,EAAE,OAAOD,iBAAiB,CAACjB,CAAC,EAAEgB,MAAM,CAAC;AAAE;AAE/Z,SAASC,iBAAiBA,CAACU,GAAG,EAAEC,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGD,GAAG,CAAChB,MAAM,EAAEiB,GAAG,GAAGD,GAAG,CAAChB,MAAM;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEiB,IAAI,GAAG,IAAIrB,KAAK,CAACoB,GAAG,CAAC,EAAEhB,CAAC,GAAGgB,GAAG,EAAEhB,CAAC,EAAE,EAAE;IAAEiB,IAAI,CAACjB,CAAC,CAAC,GAAGe,GAAG,CAACf,CAAC,CAAC;EAAE;EAAE,OAAOiB,IAAI;AAAE;AAEtL,SAASC,UAAU,QAAQ,0BAA0B;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,0BAA0BA,CAACC,MAAM,EAAE;EACzD,IAAIC,MAAM,GAAG,EAAE,CAAC,CAAC;EACjB;EACA;EACA;EACA;EACA;;EAEA,KAAK,IAAIC,SAAS,GAAGnC,+BAA+B,CAACiC,MAAM,CAACG,KAAK,CAAC,EAAE,CAAC,CAAC,EAAEC,KAAK,EAAE,CAAC,CAACA,KAAK,GAAGF,SAAS,CAAC,CAAC,EAAErB,IAAI,GAAG;IAC3G,IAAIwB,SAAS,GAAGD,KAAK,CAACtB,KAAK;IAC3BmB,MAAM,IAAIK,yBAAyB,CAACD,SAAS,EAAEJ,MAAM,CAAC,IAAI,EAAE;EAC9D;EAEA,OAAOA,MAAM;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASK,yBAAyBA,CAACD,SAAS,EAAEE,oBAAoB,EAAEC,SAAS,EAAE;EACpF;EACA,IAAIH,SAAS,KAAK,GAAG,EAAE;IACrB;IACA;IACA,IAAIE,oBAAoB,EAAE;MACxB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,OAAOC,SAAS,KAAK,UAAU,EAAE;QACnCA,SAAS,CAAC,KAAK,CAAC;MAClB;MAEA;IACF;IAEA,OAAO,GAAG;EACZ,CAAC,CAAC;;EAGF,OAAOV,UAAU,CAACO,SAAS,CAAC;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}