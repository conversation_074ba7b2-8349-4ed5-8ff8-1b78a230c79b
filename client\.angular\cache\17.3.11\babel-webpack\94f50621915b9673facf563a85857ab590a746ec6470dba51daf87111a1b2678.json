{"ast": null, "code": "// This is a legacy function.\n// Use `findNumbers()` instead.\nimport _findPhoneNumbers, { searchPhoneNumbers as _searchPhoneNumbers } from './findPhoneNumbersInitialImplementation.js';\nimport normalizeArguments from '../normalizeArguments.js';\nexport default function findPhoneNumbers() {\n  var _normalizeArguments = normalizeArguments(arguments),\n    text = _normalizeArguments.text,\n    options = _normalizeArguments.options,\n    metadata = _normalizeArguments.metadata;\n  return _findPhoneNumbers(text, options, metadata);\n}\n/**\r\n * @return ES6 `for ... of` iterator.\r\n */\n\nexport function searchPhoneNumbers() {\n  var _normalizeArguments2 = normalizeArguments(arguments),\n    text = _normalizeArguments2.text,\n    options = _normalizeArguments2.options,\n    metadata = _normalizeArguments2.metadata;\n  return _searchPhoneNumbers(text, options, metadata);\n}", "map": {"version": 3, "names": ["_findPhoneNumbers", "searchPhoneNumbers", "_searchPhoneNumbers", "normalizeArguments", "findPhoneNumbers", "_normalizeArguments", "arguments", "text", "options", "metadata", "_normalizeArguments2"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/legacy/findPhoneNumbers.js"], "sourcesContent": ["// This is a legacy function.\n// Use `findNumbers()` instead.\nimport _findPhoneNumbers, { searchPhoneNumbers as _searchPhoneNumbers } from './findPhoneNumbersInitialImplementation.js';\nimport normalizeArguments from '../normalizeArguments.js';\nexport default function findPhoneNumbers() {\n  var _normalizeArguments = normalizeArguments(arguments),\n      text = _normalizeArguments.text,\n      options = _normalizeArguments.options,\n      metadata = _normalizeArguments.metadata;\n\n  return _findPhoneNumbers(text, options, metadata);\n}\n/**\r\n * @return ES6 `for ... of` iterator.\r\n */\n\nexport function searchPhoneNumbers() {\n  var _normalizeArguments2 = normalizeArguments(arguments),\n      text = _normalizeArguments2.text,\n      options = _normalizeArguments2.options,\n      metadata = _normalizeArguments2.metadata;\n\n  return _searchPhoneNumbers(text, options, metadata);\n}\n"], "mappings": "AAAA;AACA;AACA,OAAOA,iBAAiB,IAAIC,kBAAkB,IAAIC,mBAAmB,QAAQ,4CAA4C;AACzH,OAAOC,kBAAkB,MAAM,0BAA0B;AACzD,eAAe,SAASC,gBAAgBA,CAAA,EAAG;EACzC,IAAIC,mBAAmB,GAAGF,kBAAkB,CAACG,SAAS,CAAC;IACnDC,IAAI,GAAGF,mBAAmB,CAACE,IAAI;IAC/BC,OAAO,GAAGH,mBAAmB,CAACG,OAAO;IACrCC,QAAQ,GAAGJ,mBAAmB,CAACI,QAAQ;EAE3C,OAAOT,iBAAiB,CAACO,IAAI,EAAEC,OAAO,EAAEC,QAAQ,CAAC;AACnD;AACA;AACA;AACA;;AAEA,OAAO,SAASR,kBAAkBA,CAAA,EAAG;EACnC,IAAIS,oBAAoB,GAAGP,kBAAkB,CAACG,SAAS,CAAC;IACpDC,IAAI,GAAGG,oBAAoB,CAACH,IAAI;IAChCC,OAAO,GAAGE,oBAAoB,CAACF,OAAO;IACtCC,QAAQ,GAAGC,oBAAoB,CAACD,QAAQ;EAE5C,OAAOP,mBAAmB,CAACK,IAAI,EAAEC,OAAO,EAAEC,QAAQ,CAAC;AACrD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}