{"ast": null, "code": "// Copy-pasted from `PhoneNumberMatcher.js`.\nimport { PLUS_CHARS } from '../constants.js';\nimport { limit } from './util.js';\nimport { isLatinLetter, isInvalidPunctuationSymbol } from './utf-8.js';\nvar OPENING_PARENS = \"(\\\\[\\uFF08\\uFF3B\";\nvar CLOSING_PARENS = \")\\\\]\\uFF09\\uFF3D\";\nvar NON_PARENS = \"[^\".concat(OPENING_PARENS).concat(CLOSING_PARENS, \"]\");\nexport var LEAD_CLASS = \"[\".concat(OPENING_PARENS).concat(PLUS_CHARS, \"]\"); // Punctuation that may be at the start of a phone number - brackets and plus signs.\n\nvar LEAD_CLASS_LEADING = new RegExp('^' + LEAD_CLASS); // Limit on the number of pairs of brackets in a phone number.\n\nvar BRACKET_PAIR_LIMIT = limit(0, 3);\n/**\r\n * <PERSON><PERSON> to check that brackets match. Opening brackets should be closed within a phone number.\r\n * This also checks that there is something inside the brackets. Having no brackets at all is also\r\n * fine.\r\n *\r\n * An opening bracket at the beginning may not be closed, but subsequent ones should be.  It's\r\n * also possible that the leading bracket was dropped, so we shouldn't be surprised if we see a\r\n * closing bracket first. We limit the sets of brackets in a phone number to four.\r\n */\n\nvar MATCHING_BRACKETS_ENTIRE = new RegExp('^' + \"(?:[\" + OPENING_PARENS + \"])?\" + \"(?:\" + NON_PARENS + \"+\" + \"[\" + CLOSING_PARENS + \"])?\" + NON_PARENS + \"+\" + \"(?:[\" + OPENING_PARENS + \"]\" + NON_PARENS + \"+[\" + CLOSING_PARENS + \"])\" + BRACKET_PAIR_LIMIT + NON_PARENS + \"*\" + '$');\n/**\r\n * Matches strings that look like publication pages. Example:\r\n * <pre>Computing Complete Answers to Queries in the Presence of Limited Access Patterns.\r\n * Chen Li. VLDB J. 12(3): 211-227 (2003).</pre>\r\n *\r\n * The string \"211-227 (2003)\" is not a telephone number.\r\n */\n\nvar PUB_PAGES = /\\d{1,5}-+\\d{1,5}\\s{0,4}\\(\\d{1,4}/;\nexport default function isValidCandidate(candidate, offset, text, leniency) {\n  // Check the candidate doesn't contain any formatting\n  // which would indicate that it really isn't a phone number.\n  if (!MATCHING_BRACKETS_ENTIRE.test(candidate) || PUB_PAGES.test(candidate)) {\n    return;\n  } // If leniency is set to VALID or stricter, we also want to skip numbers that are surrounded\n  // by Latin alphabetic characters, to skip cases like abc8005001234 or 8005001234def.\n\n  if (leniency !== 'POSSIBLE') {\n    // If the candidate is not at the start of the text,\n    // and does not start with phone-number punctuation,\n    // check the previous character.\n    if (offset > 0 && !LEAD_CLASS_LEADING.test(candidate)) {\n      var previousChar = text[offset - 1]; // We return null if it is a latin letter or an invalid punctuation symbol.\n\n      if (isInvalidPunctuationSymbol(previousChar) || isLatinLetter(previousChar)) {\n        return false;\n      }\n    }\n    var lastCharIndex = offset + candidate.length;\n    if (lastCharIndex < text.length) {\n      var nextChar = text[lastCharIndex];\n      if (isInvalidPunctuationSymbol(nextChar) || isLatinLetter(nextChar)) {\n        return false;\n      }\n    }\n  }\n  return true;\n}", "map": {"version": 3, "names": ["PLUS_CHARS", "limit", "isLatinLetter", "isInvalidPunctuationSymbol", "OPENING_PARENS", "CLOSING_PARENS", "NON_PARENS", "concat", "LEAD_CLASS", "LEAD_CLASS_LEADING", "RegExp", "BRACKET_PAIR_LIMIT", "MATCHING_BRACKETS_ENTIRE", "PUB_PAGES", "isValidCandidate", "candidate", "offset", "text", "leniency", "test", "previousChar", "lastCharIndex", "length", "nextChar"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/findNumbers/isValidCandidate.js"], "sourcesContent": ["// Copy-pasted from `PhoneNumberMatcher.js`.\nimport { PLUS_CHARS } from '../constants.js';\nimport { limit } from './util.js';\nimport { isLatinLetter, isInvalidPunctuationSymbol } from './utf-8.js';\nvar OPENING_PARENS = \"(\\\\[\\uFF08\\uFF3B\";\nvar CLOSING_PARENS = \")\\\\]\\uFF09\\uFF3D\";\nvar NON_PARENS = \"[^\".concat(OPENING_PARENS).concat(CLOSING_PARENS, \"]\");\nexport var LEAD_CLASS = \"[\".concat(OPENING_PARENS).concat(PLUS_CHARS, \"]\"); // Punctuation that may be at the start of a phone number - brackets and plus signs.\n\nvar LEAD_CLASS_LEADING = new RegExp('^' + LEAD_CLASS); // Limit on the number of pairs of brackets in a phone number.\n\nvar BRACKET_PAIR_LIMIT = limit(0, 3);\n/**\r\n * <PERSON><PERSON> to check that brackets match. Opening brackets should be closed within a phone number.\r\n * This also checks that there is something inside the brackets. Having no brackets at all is also\r\n * fine.\r\n *\r\n * An opening bracket at the beginning may not be closed, but subsequent ones should be.  It's\r\n * also possible that the leading bracket was dropped, so we shouldn't be surprised if we see a\r\n * closing bracket first. We limit the sets of brackets in a phone number to four.\r\n */\n\nvar MATCHING_BRACKETS_ENTIRE = new RegExp('^' + \"(?:[\" + OPENING_PARENS + \"])?\" + \"(?:\" + NON_PARENS + \"+\" + \"[\" + CLOSING_PARENS + \"])?\" + NON_PARENS + \"+\" + \"(?:[\" + OPENING_PARENS + \"]\" + NON_PARENS + \"+[\" + CLOSING_PARENS + \"])\" + BRACKET_PAIR_LIMIT + NON_PARENS + \"*\" + '$');\n/**\r\n * Matches strings that look like publication pages. Example:\r\n * <pre>Computing Complete Answers to Queries in the Presence of Limited Access Patterns.\r\n * Chen Li. VLDB J. 12(3): 211-227 (2003).</pre>\r\n *\r\n * The string \"211-227 (2003)\" is not a telephone number.\r\n */\n\nvar PUB_PAGES = /\\d{1,5}-+\\d{1,5}\\s{0,4}\\(\\d{1,4}/;\nexport default function isValidCandidate(candidate, offset, text, leniency) {\n  // Check the candidate doesn't contain any formatting\n  // which would indicate that it really isn't a phone number.\n  if (!MATCHING_BRACKETS_ENTIRE.test(candidate) || PUB_PAGES.test(candidate)) {\n    return;\n  } // If leniency is set to VALID or stricter, we also want to skip numbers that are surrounded\n  // by Latin alphabetic characters, to skip cases like abc8005001234 or 8005001234def.\n\n\n  if (leniency !== 'POSSIBLE') {\n    // If the candidate is not at the start of the text,\n    // and does not start with phone-number punctuation,\n    // check the previous character.\n    if (offset > 0 && !LEAD_CLASS_LEADING.test(candidate)) {\n      var previousChar = text[offset - 1]; // We return null if it is a latin letter or an invalid punctuation symbol.\n\n      if (isInvalidPunctuationSymbol(previousChar) || isLatinLetter(previousChar)) {\n        return false;\n      }\n    }\n\n    var lastCharIndex = offset + candidate.length;\n\n    if (lastCharIndex < text.length) {\n      var nextChar = text[lastCharIndex];\n\n      if (isInvalidPunctuationSymbol(nextChar) || isLatinLetter(nextChar)) {\n        return false;\n      }\n    }\n  }\n\n  return true;\n}\n"], "mappings": "AAAA;AACA,SAASA,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,KAAK,QAAQ,WAAW;AACjC,SAASC,aAAa,EAAEC,0BAA0B,QAAQ,YAAY;AACtE,IAAIC,cAAc,GAAG,kBAAkB;AACvC,IAAIC,cAAc,GAAG,kBAAkB;AACvC,IAAIC,UAAU,GAAG,IAAI,CAACC,MAAM,CAACH,cAAc,CAAC,CAACG,MAAM,CAACF,cAAc,EAAE,GAAG,CAAC;AACxE,OAAO,IAAIG,UAAU,GAAG,GAAG,CAACD,MAAM,CAACH,cAAc,CAAC,CAACG,MAAM,CAACP,UAAU,EAAE,GAAG,CAAC,CAAC,CAAC;;AAE5E,IAAIS,kBAAkB,GAAG,IAAIC,MAAM,CAAC,GAAG,GAAGF,UAAU,CAAC,CAAC,CAAC;;AAEvD,IAAIG,kBAAkB,GAAGV,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIW,wBAAwB,GAAG,IAAIF,MAAM,CAAC,GAAG,GAAG,MAAM,GAAGN,cAAc,GAAG,KAAK,GAAG,KAAK,GAAGE,UAAU,GAAG,GAAG,GAAG,GAAG,GAAGD,cAAc,GAAG,KAAK,GAAGC,UAAU,GAAG,GAAG,GAAG,MAAM,GAAGF,cAAc,GAAG,GAAG,GAAGE,UAAU,GAAG,IAAI,GAAGD,cAAc,GAAG,IAAI,GAAGM,kBAAkB,GAAGL,UAAU,GAAG,GAAG,GAAG,GAAG,CAAC;AACvR;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIO,SAAS,GAAG,kCAAkC;AAClD,eAAe,SAASC,gBAAgBA,CAACC,SAAS,EAAEC,MAAM,EAAEC,IAAI,EAAEC,QAAQ,EAAE;EAC1E;EACA;EACA,IAAI,CAACN,wBAAwB,CAACO,IAAI,CAACJ,SAAS,CAAC,IAAIF,SAAS,CAACM,IAAI,CAACJ,SAAS,CAAC,EAAE;IAC1E;EACF,CAAC,CAAC;EACF;;EAGA,IAAIG,QAAQ,KAAK,UAAU,EAAE;IAC3B;IACA;IACA;IACA,IAAIF,MAAM,GAAG,CAAC,IAAI,CAACP,kBAAkB,CAACU,IAAI,CAACJ,SAAS,CAAC,EAAE;MACrD,IAAIK,YAAY,GAAGH,IAAI,CAACD,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;;MAErC,IAAIb,0BAA0B,CAACiB,YAAY,CAAC,IAAIlB,aAAa,CAACkB,YAAY,CAAC,EAAE;QAC3E,OAAO,KAAK;MACd;IACF;IAEA,IAAIC,aAAa,GAAGL,MAAM,GAAGD,SAAS,CAACO,MAAM;IAE7C,IAAID,aAAa,GAAGJ,IAAI,CAACK,MAAM,EAAE;MAC/B,IAAIC,QAAQ,GAAGN,IAAI,CAACI,aAAa,CAAC;MAElC,IAAIlB,0BAA0B,CAACoB,QAAQ,CAAC,IAAIrB,aAAa,CAACqB,QAAQ,CAAC,EAAE;QACnE,OAAO,KAAK;MACd;IACF;EACF;EAEA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}