{"ast": null, "code": "import { VALID_DIGITS } from '../../constants.js'; // The RFC 3966 format for extensions.\n\nvar RFC3966_EXTN_PREFIX = ';ext=';\n/**\r\n * Helper method for constructing regular expressions for parsing. Creates\r\n * an expression that captures up to max_length digits.\r\n * @return {string} RegEx pattern to capture extension digits.\r\n */\n\nvar getExtensionDigitsPattern = function getExtensionDigitsPattern(maxLength) {\n  return \"([\".concat(VALID_DIGITS, \"]{1,\").concat(maxLength, \"})\");\n};\n/**\r\n * Helper initialiser method to create the regular-expression pattern to match\r\n * extensions.\r\n * Copy-pasted from Google's `libphonenumber`:\r\n * https://github.com/google/libphonenumber/blob/55b2646ec9393f4d3d6661b9c82ef9e258e8b829/javascript/i18n/phonenumbers/phonenumberutil.js#L759-L766\r\n * @return {string} RegEx pattern to capture extensions.\r\n */\n\nexport default function createExtensionPattern(purpose) {\n  // We cap the maximum length of an extension based on the ambiguity of the way\n  // the extension is prefixed. As per ITU, the officially allowed length for\n  // extensions is actually 40, but we don't support this since we haven't seen real\n  // examples and this introduces many false interpretations as the extension labels\n  // are not standardized.\n\n  /** @type {string} */\n  var extLimitAfterExplicitLabel = '20';\n  /** @type {string} */\n\n  var extLimitAfterLikelyLabel = '15';\n  /** @type {string} */\n\n  var extLimitAfterAmbiguousChar = '9';\n  /** @type {string} */\n\n  var extLimitWhenNotSure = '6';\n  /** @type {string} */\n\n  var possibleSeparatorsBetweenNumberAndExtLabel = \"[ \\xA0\\\\t,]*\"; // Optional full stop (.) or colon, followed by zero or more spaces/tabs/commas.\n\n  /** @type {string} */\n\n  var possibleCharsAfterExtLabel = \"[:\\\\.\\uFF0E]?[ \\xA0\\\\t,-]*\";\n  /** @type {string} */\n\n  var optionalExtnSuffix = \"#?\"; // Here the extension is called out in more explicit way, i.e mentioning it obvious\n  // patterns like \"ext.\".\n\n  /** @type {string} */\n\n  var explicitExtLabels = \"(?:e?xt(?:ensi(?:o\\u0301?|\\xF3))?n?|\\uFF45?\\uFF58\\uFF54\\uFF4E?|\\u0434\\u043E\\u0431|anexo)\"; // One-character symbols that can be used to indicate an extension, and less\n  // commonly used or more ambiguous extension labels.\n\n  /** @type {string} */\n\n  var ambiguousExtLabels = \"(?:[x\\uFF58#\\uFF03~\\uFF5E]|int|\\uFF49\\uFF4E\\uFF54)\"; // When extension is not separated clearly.\n\n  /** @type {string} */\n\n  var ambiguousSeparator = \"[- ]+\"; // This is the same as possibleSeparatorsBetweenNumberAndExtLabel, but not matching\n  // comma as extension label may have it.\n\n  /** @type {string} */\n\n  var possibleSeparatorsNumberExtLabelNoComma = \"[ \\xA0\\\\t]*\"; // \",,\" is commonly used for auto dialling the extension when connected. First\n  // comma is matched through possibleSeparatorsBetweenNumberAndExtLabel, so we do\n  // not repeat it here. Semi-colon works in Iphone and Android also to pop up a\n  // button with the extension number following.\n\n  /** @type {string} */\n\n  var autoDiallingAndExtLabelsFound = \"(?:,{2}|;)\";\n  /** @type {string} */\n\n  var rfcExtn = RFC3966_EXTN_PREFIX + getExtensionDigitsPattern(extLimitAfterExplicitLabel);\n  /** @type {string} */\n\n  var explicitExtn = possibleSeparatorsBetweenNumberAndExtLabel + explicitExtLabels + possibleCharsAfterExtLabel + getExtensionDigitsPattern(extLimitAfterExplicitLabel) + optionalExtnSuffix;\n  /** @type {string} */\n\n  var ambiguousExtn = possibleSeparatorsBetweenNumberAndExtLabel + ambiguousExtLabels + possibleCharsAfterExtLabel + getExtensionDigitsPattern(extLimitAfterAmbiguousChar) + optionalExtnSuffix;\n  /** @type {string} */\n\n  var americanStyleExtnWithSuffix = ambiguousSeparator + getExtensionDigitsPattern(extLimitWhenNotSure) + \"#\";\n  /** @type {string} */\n\n  var autoDiallingExtn = possibleSeparatorsNumberExtLabelNoComma + autoDiallingAndExtLabelsFound + possibleCharsAfterExtLabel + getExtensionDigitsPattern(extLimitAfterLikelyLabel) + optionalExtnSuffix;\n  /** @type {string} */\n\n  var onlyCommasExtn = possibleSeparatorsNumberExtLabelNoComma + \"(?:,)+\" + possibleCharsAfterExtLabel + getExtensionDigitsPattern(extLimitAfterAmbiguousChar) + optionalExtnSuffix; // The first regular expression covers RFC 3966 format, where the extension is added\n  // using \";ext=\". The second more generic where extension is mentioned with explicit\n  // labels like \"ext:\". In both the above cases we allow more numbers in extension than\n  // any other extension labels. The third one captures when single character extension\n  // labels or less commonly used labels are used. In such cases we capture fewer\n  // extension digits in order to reduce the chance of falsely interpreting two\n  // numbers beside each other as a number + extension. The fourth one covers the\n  // special case of American numbers where the extension is written with a hash\n  // at the end, such as \"- 503#\". The fifth one is exclusively for extension\n  // autodialling formats which are used when dialling and in this case we accept longer\n  // extensions. The last one is more liberal on the number of commas that acts as\n  // extension labels, so we have a strict cap on the number of digits in such extensions.\n\n  return rfcExtn + \"|\" + explicitExtn + \"|\" + ambiguousExtn + \"|\" + americanStyleExtnWithSuffix + \"|\" + autoDiallingExtn + \"|\" + onlyCommasExtn;\n}", "map": {"version": 3, "names": ["VALID_DIGITS", "RFC3966_EXTN_PREFIX", "getExtensionDigitsPattern", "max<PERSON><PERSON><PERSON>", "concat", "createExtensionPattern", "purpose", "extLimitAfterExplicitLabel", "extLimitAfterLikelyLabel", "extLimitAfterAmbiguousChar", "extLimitWhenNotSure", "possibleSeparatorsBetweenNumberAndExtLabel", "possibleCharsAfterExtLabel", "optionalExtnSuffix", "explicitExtLabels", "ambiguousExtLabels", "ambiguous<PERSON><PERSON><PERSON><PERSON>", "possibleSeparatorsNumberExtLabelNoComma", "autoDiallingAndExtLabelsFound", "rfcExtn", "explicitExtn", "ambiguousExtn", "americanStyleExtnWithSuffix", "autoDiallingExtn", "onlyCommasExtn"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/es6/helpers/extension/createExtensionPattern.js"], "sourcesContent": ["import { VALID_DIGITS } from '../../constants.js'; // The RFC 3966 format for extensions.\n\nvar RFC3966_EXTN_PREFIX = ';ext=';\n/**\r\n * Helper method for constructing regular expressions for parsing. Creates\r\n * an expression that captures up to max_length digits.\r\n * @return {string} RegEx pattern to capture extension digits.\r\n */\n\nvar getExtensionDigitsPattern = function getExtensionDigitsPattern(maxLength) {\n  return \"([\".concat(VALID_DIGITS, \"]{1,\").concat(maxLength, \"})\");\n};\n/**\r\n * Helper initialiser method to create the regular-expression pattern to match\r\n * extensions.\r\n * Copy-pasted from Google's `libphonenumber`:\r\n * https://github.com/google/libphonenumber/blob/55b2646ec9393f4d3d6661b9c82ef9e258e8b829/javascript/i18n/phonenumbers/phonenumberutil.js#L759-L766\r\n * @return {string} RegEx pattern to capture extensions.\r\n */\n\n\nexport default function createExtensionPattern(purpose) {\n  // We cap the maximum length of an extension based on the ambiguity of the way\n  // the extension is prefixed. As per ITU, the officially allowed length for\n  // extensions is actually 40, but we don't support this since we haven't seen real\n  // examples and this introduces many false interpretations as the extension labels\n  // are not standardized.\n\n  /** @type {string} */\n  var extLimitAfterExplicitLabel = '20';\n  /** @type {string} */\n\n  var extLimitAfterLikelyLabel = '15';\n  /** @type {string} */\n\n  var extLimitAfterAmbiguousChar = '9';\n  /** @type {string} */\n\n  var extLimitWhenNotSure = '6';\n  /** @type {string} */\n\n  var possibleSeparatorsBetweenNumberAndExtLabel = \"[ \\xA0\\\\t,]*\"; // Optional full stop (.) or colon, followed by zero or more spaces/tabs/commas.\n\n  /** @type {string} */\n\n  var possibleCharsAfterExtLabel = \"[:\\\\.\\uFF0E]?[ \\xA0\\\\t,-]*\";\n  /** @type {string} */\n\n  var optionalExtnSuffix = \"#?\"; // Here the extension is called out in more explicit way, i.e mentioning it obvious\n  // patterns like \"ext.\".\n\n  /** @type {string} */\n\n  var explicitExtLabels = \"(?:e?xt(?:ensi(?:o\\u0301?|\\xF3))?n?|\\uFF45?\\uFF58\\uFF54\\uFF4E?|\\u0434\\u043E\\u0431|anexo)\"; // One-character symbols that can be used to indicate an extension, and less\n  // commonly used or more ambiguous extension labels.\n\n  /** @type {string} */\n\n  var ambiguousExtLabels = \"(?:[x\\uFF58#\\uFF03~\\uFF5E]|int|\\uFF49\\uFF4E\\uFF54)\"; // When extension is not separated clearly.\n\n  /** @type {string} */\n\n  var ambiguousSeparator = \"[- ]+\"; // This is the same as possibleSeparatorsBetweenNumberAndExtLabel, but not matching\n  // comma as extension label may have it.\n\n  /** @type {string} */\n\n  var possibleSeparatorsNumberExtLabelNoComma = \"[ \\xA0\\\\t]*\"; // \",,\" is commonly used for auto dialling the extension when connected. First\n  // comma is matched through possibleSeparatorsBetweenNumberAndExtLabel, so we do\n  // not repeat it here. Semi-colon works in Iphone and Android also to pop up a\n  // button with the extension number following.\n\n  /** @type {string} */\n\n  var autoDiallingAndExtLabelsFound = \"(?:,{2}|;)\";\n  /** @type {string} */\n\n  var rfcExtn = RFC3966_EXTN_PREFIX + getExtensionDigitsPattern(extLimitAfterExplicitLabel);\n  /** @type {string} */\n\n  var explicitExtn = possibleSeparatorsBetweenNumberAndExtLabel + explicitExtLabels + possibleCharsAfterExtLabel + getExtensionDigitsPattern(extLimitAfterExplicitLabel) + optionalExtnSuffix;\n  /** @type {string} */\n\n  var ambiguousExtn = possibleSeparatorsBetweenNumberAndExtLabel + ambiguousExtLabels + possibleCharsAfterExtLabel + getExtensionDigitsPattern(extLimitAfterAmbiguousChar) + optionalExtnSuffix;\n  /** @type {string} */\n\n  var americanStyleExtnWithSuffix = ambiguousSeparator + getExtensionDigitsPattern(extLimitWhenNotSure) + \"#\";\n  /** @type {string} */\n\n  var autoDiallingExtn = possibleSeparatorsNumberExtLabelNoComma + autoDiallingAndExtLabelsFound + possibleCharsAfterExtLabel + getExtensionDigitsPattern(extLimitAfterLikelyLabel) + optionalExtnSuffix;\n  /** @type {string} */\n\n  var onlyCommasExtn = possibleSeparatorsNumberExtLabelNoComma + \"(?:,)+\" + possibleCharsAfterExtLabel + getExtensionDigitsPattern(extLimitAfterAmbiguousChar) + optionalExtnSuffix; // The first regular expression covers RFC 3966 format, where the extension is added\n  // using \";ext=\". The second more generic where extension is mentioned with explicit\n  // labels like \"ext:\". In both the above cases we allow more numbers in extension than\n  // any other extension labels. The third one captures when single character extension\n  // labels or less commonly used labels are used. In such cases we capture fewer\n  // extension digits in order to reduce the chance of falsely interpreting two\n  // numbers beside each other as a number + extension. The fourth one covers the\n  // special case of American numbers where the extension is written with a hash\n  // at the end, such as \"- 503#\". The fifth one is exclusively for extension\n  // autodialling formats which are used when dialling and in this case we accept longer\n  // extensions. The last one is more liberal on the number of commas that acts as\n  // extension labels, so we have a strict cap on the number of digits in such extensions.\n\n  return rfcExtn + \"|\" + explicitExtn + \"|\" + ambiguousExtn + \"|\" + americanStyleExtnWithSuffix + \"|\" + autoDiallingExtn + \"|\" + onlyCommasExtn;\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,oBAAoB,CAAC,CAAC;;AAEnD,IAAIC,mBAAmB,GAAG,OAAO;AACjC;AACA;AACA;AACA;AACA;;AAEA,IAAIC,yBAAyB,GAAG,SAASA,yBAAyBA,CAACC,SAAS,EAAE;EAC5E,OAAO,IAAI,CAACC,MAAM,CAACJ,YAAY,EAAE,MAAM,CAAC,CAACI,MAAM,CAACD,SAAS,EAAE,IAAI,CAAC;AAClE,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,eAAe,SAASE,sBAAsBA,CAACC,OAAO,EAAE;EACtD;EACA;EACA;EACA;EACA;;EAEA;EACA,IAAIC,0BAA0B,GAAG,IAAI;EACrC;;EAEA,IAAIC,wBAAwB,GAAG,IAAI;EACnC;;EAEA,IAAIC,0BAA0B,GAAG,GAAG;EACpC;;EAEA,IAAIC,mBAAmB,GAAG,GAAG;EAC7B;;EAEA,IAAIC,0CAA0C,GAAG,cAAc,CAAC,CAAC;;EAEjE;;EAEA,IAAIC,0BAA0B,GAAG,4BAA4B;EAC7D;;EAEA,IAAIC,kBAAkB,GAAG,IAAI,CAAC,CAAC;EAC/B;;EAEA;;EAEA,IAAIC,iBAAiB,GAAG,0FAA0F,CAAC,CAAC;EACpH;;EAEA;;EAEA,IAAIC,kBAAkB,GAAG,oDAAoD,CAAC,CAAC;;EAE/E;;EAEA,IAAIC,kBAAkB,GAAG,OAAO,CAAC,CAAC;EAClC;;EAEA;;EAEA,IAAIC,uCAAuC,GAAG,aAAa,CAAC,CAAC;EAC7D;EACA;EACA;;EAEA;;EAEA,IAAIC,6BAA6B,GAAG,YAAY;EAChD;;EAEA,IAAIC,OAAO,GAAGlB,mBAAmB,GAAGC,yBAAyB,CAACK,0BAA0B,CAAC;EACzF;;EAEA,IAAIa,YAAY,GAAGT,0CAA0C,GAAGG,iBAAiB,GAAGF,0BAA0B,GAAGV,yBAAyB,CAACK,0BAA0B,CAAC,GAAGM,kBAAkB;EAC3L;;EAEA,IAAIQ,aAAa,GAAGV,0CAA0C,GAAGI,kBAAkB,GAAGH,0BAA0B,GAAGV,yBAAyB,CAACO,0BAA0B,CAAC,GAAGI,kBAAkB;EAC7L;;EAEA,IAAIS,2BAA2B,GAAGN,kBAAkB,GAAGd,yBAAyB,CAACQ,mBAAmB,CAAC,GAAG,GAAG;EAC3G;;EAEA,IAAIa,gBAAgB,GAAGN,uCAAuC,GAAGC,6BAA6B,GAAGN,0BAA0B,GAAGV,yBAAyB,CAACM,wBAAwB,CAAC,GAAGK,kBAAkB;EACtM;;EAEA,IAAIW,cAAc,GAAGP,uCAAuC,GAAG,QAAQ,GAAGL,0BAA0B,GAAGV,yBAAyB,CAACO,0BAA0B,CAAC,GAAGI,kBAAkB,CAAC,CAAC;EACnL;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA,OAAOM,OAAO,GAAG,GAAG,GAAGC,YAAY,GAAG,GAAG,GAAGC,aAAa,GAAG,GAAG,GAAGC,2BAA2B,GAAG,GAAG,GAAGC,gBAAgB,GAAG,GAAG,GAAGC,cAAc;AAC/I", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}