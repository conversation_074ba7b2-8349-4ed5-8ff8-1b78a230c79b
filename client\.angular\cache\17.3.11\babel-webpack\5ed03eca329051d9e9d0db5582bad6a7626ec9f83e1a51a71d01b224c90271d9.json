{"ast": null, "code": "import withMetadataArgument from './withMetadataArgument.js';\nimport { parsePhoneNumberWithError as _parsePhoneNumberWithError } from '../../core/index.js';\nexport function parsePhoneNumberWithError() {\n  return withMetadataArgument(_parsePhoneNumberWithError, arguments);\n}", "map": {"version": 3, "names": ["withMetadataArgument", "parsePhoneNumberWithError", "_parsePhoneNumberWithError", "arguments"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/min/exports/parsePhoneNumberWithError.js"], "sourcesContent": ["import withMetadataArgument from './withMetadataArgument.js'\r\nimport { parsePhoneNumberWithError as _parsePhoneNumberWithError } from '../../core/index.js'\r\n\r\nexport function parsePhoneNumberWithError() {\r\n\treturn withMetadataArgument(_parsePhoneNumberWithError, arguments)\r\n}\r\n"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,2BAA2B;AAC5D,SAASC,yBAAyB,IAAIC,0BAA0B,QAAQ,qBAAqB;AAE7F,OAAO,SAASD,yBAAyBA,CAAA,EAAG;EAC3C,OAAOD,oBAAoB,CAACE,0BAA0B,EAAEC,SAAS,CAAC;AACnE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}