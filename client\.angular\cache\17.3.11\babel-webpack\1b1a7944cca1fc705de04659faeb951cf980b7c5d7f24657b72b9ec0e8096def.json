{"ast": null, "code": "import withMetadataArgument from '../min/exports/withMetadataArgument.js';\nimport _isValidNumberForRegion from '../es6/legacy/isValidNumberForRegion.js';\nexport function isValidNumberForRegion() {\n  return withMetadataArgument(_isValidNumberForRegion, arguments);\n}", "map": {"version": 3, "names": ["withMetadataArgument", "_isValidNumberForRegion", "isValidNumberForRegion", "arguments"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/index.es6.exports/isValidNumberForRegion.js"], "sourcesContent": ["import withMetadataArgument from '../min/exports/withMetadataArgument.js'\r\n\r\nimport _isValidNumberForRegion from '../es6/legacy/isValidNumberForRegion.js'\r\n\r\nexport function isValidNumberForRegion() {\r\n\treturn withMetadataArgument(_isValidNumberForRegion, arguments)\r\n}\r\n"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,wCAAwC;AAEzE,OAAOC,uBAAuB,MAAM,yCAAyC;AAE7E,OAAO,SAASC,sBAAsBA,CAAA,EAAG;EACxC,OAAOF,oBAAoB,CAACC,uBAAuB,EAAEE,SAAS,CAAC;AAChE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}